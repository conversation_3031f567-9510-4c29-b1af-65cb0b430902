import { getLanguage } from '../../../app';

const common = (field) => {
	let lang = getLanguage();
	let params = {
		'all': {
			'cn': '全部',
			'en': 'All'
		},
		'amountSuccess': {
			'cn': '成功量',
			'en': 'Succeed'
		},
		'amountFailure': {
			'cn': '失败量',
			'en': 'Failed'
		},
		'libraryLimit': {
			'cn': '库无量',
			'en': 'Non-hit'
		},
		'successRate': {
			'cn': '成功率',
			'en': 'Succeed ratio'
		},
		'failRate': {
			'cn': '失败率',
			'en': 'Failed ratio'
		},
		'libraryRate': {
			'cn': '库无率',
			'en': 'Non-hit ratio'
		},
		'rate': {
			'cn': '率',
			'en': 'ratio'
		},
		'amount': {
			'cn': '调用量',
			'en': 'Requested'
		},
		'ratio': {
			'cn': '比率',
			'en': 'Ratio'
		}
	};
	return params[field][lang];
};

export default {
	common
};
