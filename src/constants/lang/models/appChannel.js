import {getLanguage} from '../../../app';

const common = (field) => {
	let lang = getLanguage();
	let params = {
		'all': {
			'cn': '全部',
			'en': 'All'
		},
		'failedCount': {
			'cn': '失败数量',
			'en': 'Failed'
		},
		'queryCount': {
			'cn': '查询数量',
			'en': 'Requested'
		},
		'cacheCount': {
			'cn': '缓存数量',
			'en': 'Cached'
		},
		'businessChannel': {
			'cn': '业务系统',
			'en': 'System'
		},
		'amount': {
			'cn': '调用量',
			'en': 'Amount'
		}
	};
	return params[field][lang];
};

export default {
	common
};
