import I18N from '@/utils/I18N';
const conditionTypeMap = {
    '&&': I18N.constants.common.manZuSuoYouTiao,
    '||': I18N.constants.common.manZuRenYiTiao,
    '!&&': I18N.constants.common.tiaoJianJunBuMan
};

const FieldType = [
    { name: 'string', value: 1 },
    { name: 'int', value: 2 },
    { name: 'double', value: 3 },
    { name: 'date', value: 4 },
    { name: 'boolean', value: 5 }
];

const gutterSpan = 10;

export const TYPE_NUM_MAP = {
    '2': {
        type: 'INT',
        displayName: I18N.constants.common.zhengShu,
        color: '#5262C7'
    },
    '3': {
        type: 'DOUBLE',
        displayName: I18N.constants.common.xiaoShu,
        color: '#00D2C2'
    },
    '1': {
        type: 'STRING',
        displayName: I18N.constants.common.ziFu,
        color: '#826AF9'
    },
    '5': {
        type: 'ENUM',
        displayName: I18N.constants.common.meiJu,
        color: '#00C5DC'
    },
    '6': {
        type: 'BOOLEAN',
        displayName: I18N.constants.common.buEr,
        color: '#4A9AF7'
    },
    '4': {
        type: 'DATETIME',
        displayName: I18N.constants.common.riQi,
        color: '#826AF9'
    },
    7: {
        type: 'JSON',
        displayName: 'JSON',
        color: '#1E9493'
    },
    ARRAY: {
        type: 'ARRAY',
        displayName: I18N.constants.common.shuZu,
        color: '#00D2C2'
    }
};

export const TYPE_MAP = {
    INT: {
        displayName: I18N.cascadertag.index.zhengShu,
        color: '#5262C7'
    },
    DOUBLE: {
        displayName: I18N.cascadertag.index.xiaoShu,
        color: '#00D2C2'
    },
    STRING: {
        displayName: I18N.components.templatemodal.ziFuChuan,
        color: '#826AF9'
    },
    ENUM: {
        displayName: I18N.cascadertag.index.meiJu,
        color: '#00C5DC'
    },
    BOOLEAN: {
        displayName: I18N.cascadertag.index.buEr,
        color: '#4A9AF7'
    },
    DATETIME: {
        displayName: I18N.cascadertag.index.riQi,
        color: '#826AF9'
    },
    INTEGER: {
        displayName: I18N.cascadertag.index.zhengShu,
        color: '#5262C7'
    },
    FLOAT: {
        displayName: I18N.cascadertag.index.xiaoShu,
        color: '#00D2C2'
    },
    BOOL: {
        displayName: I18N.cascadertag.index.buEr,
        color: '#4A9AF7'
    },
    DATE: {
        displayName: I18N.cascadertag.index.riQi,
        color: '#826AF9'
    },
    ARRAY: {
        displayName: I18N.cascadertag.index.shuZu,
        color: '#00D2C2'
    }
};

export { conditionTypeMap, gutterSpan, FieldType };
