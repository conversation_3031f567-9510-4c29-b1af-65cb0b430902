import I18N from '@/utils/I18N';
import moment from 'moment';
import CommonConstants from './common';

export const typeMap = {
	'INTEGER': {
		'displayName': I18N.constants.index.zhengShu,
		'color': '#5262C7'
	},
	'FLOAT': {
		'displayName': I18N.constants.index.fuDian,
		'color': '#00D2C2'
	},
	'STRING': {
		'displayName': I18N.constants.index.ziFu,
		'color': '#826AF9'
	},
	'ENUM': {
		'displayName': I18N.constants.index.meiJu,
		'color': '#00C5DC'
	},
	'BOOLEAN': {
		'displayName': I18N.constants.index.buEr,
		'color': '#4A9AF7'
	},
	'DATE': {
		'displayName': I18N.constants.index.shiJian,
		'color': '#826AF9'
	},
	'OBJECT': {
		'displayName': I18N.constants.index.duiXiang,
		'color': '#4A9AF7'
	},
	'ARRAY': {
		'displayName': I18N.constants.index.shuZu,
		'color': '#00C5DC'
	}
};

const DateRanges = {
    [I18N.constants.index.jinRi]: [moment(), moment()],
    [I18N.constants.index.zuoRi]: [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
    [I18N.constants.index.zuiJinRi3]: [moment().subtract(6, 'days'), moment()],
    [I18N.constants.index.zuiJinRi2]: [moment().subtract(29, 'days'), moment()],
    [I18N.constants.index.zuiJinRi]: [moment().subtract(89, 'days'), moment()]
};

const TYPE_MAP = {
    INT: {
        displayName: I18N.constants.index.zhengShu,
        icon: 'integer',
        color: '#5262C7'
    },
    DOUBLE: {
        displayName: I18N.constants.index.xiaoShu,
        icon: 'float',
        color: '#00D2C2'
    },
    STRING: {
        displayName: I18N.constants.index.ziFu,
        icon: 'string',
        color: '#826AF9'
    },
    ENUM: {
        displayName: I18N.constants.index.meiJu,
        icon: 'enum',
        color: '#00C5DC'
    },
    BOOLEAN: {
        displayName: I18N.constants.index.buEr,
        icon: 'boolean',
        color: '#4A9AF7'
    },
    DATETIME: {
        displayName: I18N.constants.index.riQi,
        icon: 'calendar-4',
        color: '#826AF9'
    },
    INTEGER: {
        displayName: I18N.constants.index.zhengShu,
        icon: 'integer',
        color: '#5262C7'
    },
    FLOAT: {
        displayName: I18N.constants.index.xiaoShu,
        icon: 'float',
        color: '#00D2C2'
    },
    BOOL: {
        displayName: I18N.constants.index.buEr,
        icon: 'boolean',
        color: '#4A9AF7'
    },
    DATE: {
        displayName: I18N.constants.index.riQi,
        icon: 'calendar-4',
        color: '#826AF9'
    },
    ARRAY: {
        displayName: I18N.constants.index.shuZu,
        icon: 'array',
        color: '#00D2C2'
    }
};

{
    /* 数据类型(1字符型/2整型/3小数型/4日期型/5枚举/6布尔) */
}

const DATA_TYPE_MAP = {
    1: {
        displayName: I18N.constants.index.ziFu,
        icon: 'string',
        color: '#826AF9',
        type: 'STRING',
        dataType: 1
    },
    2: {
        displayName: I18N.constants.index.zhengShu,
        icon: 'integer',
        color: '#5262C7',
        type: 'INT',
        dataType: 2
    },
    3: {
        displayName: I18N.constants.index.xiaoShu,
        icon: 'float',
        color: '#00D2C2',
        type: 'DOUBLE',
        dataType: 3
    },
    4: {
        displayName: I18N.constants.index.riQi,
        icon: 'calendar-4',
        color: '#826AF9',
        type: 'DATETIME',
        dataType: 4
    },
    5: {
        displayName: I18N.constants.index.meiJu,
        icon: 'enum',
        color: '#00C5DC',
        type: 'ENUM',
        dataType: 5
    },
    6: {
        displayName: I18N.constants.index.buEr,
        icon: 'boolean',
        color: '#4A9AF7',
        type: 'BOOLEAN',
        dataType: 6
    }
};

export { CommonConstants, DateRanges, TYPE_MAP, DATA_TYPE_MAP };

const STRINGoperator = {
    equals: I18N.constants.index.dengYu,
    notEquals: I18N.constants.index.buDengYu,
    includes: I18N.constants.index.baoHan,
    notIncludes: I18N.constants.index.buBaoHan,
    prefix: I18N.constants.index.qianZhui,
    suffix: I18N.constants.index.houZhui,
    isEmpty: I18N.constants.index.weiKong,
    notEmpty: I18N.constants.index.buWeiKong
};
const INToperator = {
    equals: I18N.constants.index.dengYu,
    notEquals: I18N.constants.index.buDengYu,
    '>': I18N.constants.index.daYu,
    '>=': I18N.constants.index.daYuDengYu,
    '<': I18N.constants.index.xiaoYu,
    '<=': I18N.constants.index.xiaoYuDengYu,
    isEmpty: I18N.constants.index.weiKong,
    notEmpty: I18N.constants.index.buWeiKong
};
const BOOLEANoperator = {
    equals: I18N.constants.index.dengYu,
    notEquals: I18N.constants.index.buDengYu,
    isEmpty: I18N.constants.index.weiKong,
    notEmpty: I18N.constants.index.buWeiKong
};
const OTHERoperator = {
    equals: I18N.constants.index.dengYu,
    notEquals: I18N.constants.index.buDengYu,
    '>': I18N.constants.index.daYu,
    '>=': I18N.constants.index.daYuDengYu,
    '<': I18N.constants.index.xiaoYu,
    '<=': I18N.constants.index.xiaoYuDengYu,
    includes: I18N.constants.index.baoHan,
    notIncludes: I18N.constants.index.buBaoHan,
    prefix: I18N.constants.index.qianZhui,
    suffix: I18N.constants.index.houZhui,
    isEmpty: I18N.constants.index.weiKong,
    notEmpty: I18N.constants.index.buWeiKong
};
export const operatorMap = {
    VARCHAR: { ...STRINGoperator },
    GRAPHIC: { ...STRINGoperator },
    VARGRAPHIC: { ...STRINGoperator },
    CLOB: { ...STRINGoperator },
    DBCLOB: { ...STRINGoperator },
    STRING: { ...STRINGoperator },
    CHARACTER: { ...STRINGoperator },
    DATETIME: { ...INToperator },
    DATE: { ...INToperator },
    INT: { ...INToperator },
    DOUBLE: { ...INToperator },
    INTEGER: { ...INToperator },
    FLOAT: { ...INToperator },
    DECIMAL: { ...INToperator },
    BINARY: { ...INToperator },
    TIME: { ...INToperator },
    TIMESTAMP: { ...INToperator },
    SMALLINT: { ...INToperator },
    BIGINT: { ...INToperator },
    BOOLEAN: { ...BOOLEANoperator },
    BOOL: { ...BOOLEANoperator },
    ENUM: { ...BOOLEANoperator },
    ENUMS: { ...BOOLEANoperator },
    ARRAY: { ...BOOLEANoperator },
    OTHER: { ...OTHERoperator }
};
export const booleanList = ['true', 'false'];

export const logicEnum = [
    { label: I18N.constants.index.qie, value: 'AND' },
    { label: I18N.constants.index.huo, value: 'OR' }
];
export const logicEnumNew = [
    { label: I18N.constants.index.manZuYiXiaSuo, value: 'AND' },
    { label: I18N.constants.index.manZuYiXiaRen, value: 'OR' }
];
