// 首页图片
export const mainImages = {
	'logo': require('../sources/images/common/model-logo.svg'),
	'favicon': require('../sources/images/common/favicon.png')
};

// 主题图片
export const themeImages = {
	'themeSymbol1': require('../sources/images/theme/symbol/theme1.svg'),
	'themeSymbol2': require('../sources/images/theme/symbol/theme2.svg')
};

// 其他图片
export const otherImages = {
	'key': require('../sources/images/key.png'),
	'resetKey': require('../sources/images/reset-key.png'),
	'noData': require('../sources/images/common/no-data.svg')
};

// 数据服务大盘
export const dashboardImages = {
	'db1': require('../sources/images/db1.png'),
	'db2': require('../sources/images/db2.png'),
	'db3': require('../sources/images/db3.png'),
	'db4': require('../sources/images/db4.png')
};

// 大盘
export const chartImages = {
	'heart1': require('../sources/images/data/heart1.png'),
	'heart2': require('../sources/images/data/heart2.png'),
	'heart3': require('../sources/images/data/heart3.png'),
	'tag': require('../sources/images/data/tag.png'),
	'failed': require('../sources/images/data/failed.png'),
	'success': require('../sources/images/data/success.png'),
	'chade': require('../sources/images/data/chade.png'),
	'cache': require('../sources/images/data/cache.png'),
	'header': require('../sources/images/data/header.png'),
	'arrowUp': require('../sources/images/data/arrow-up.png'),
	'arrowDown': require('../sources/images/data/arrow-down.png'),
	'gray': require('../sources/images/data/gray.png')
};
