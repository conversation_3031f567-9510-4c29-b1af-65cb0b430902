import I18N, { createOtp } from '@/utils/I18N';
import dynamic from 'dva/dynamic';
import { Redirect, Route, Router, Switch } from 'dva/router';
import { get } from 'lodash';
import { ConfigProvider, Exception, PageLoading, renderEmpty } from 'tntd';
import enUS from 'tntd/es/locale/en_US';
import zhCN from 'tntd/es/locale/zh_CN';

import { flatten } from '@/utils/utils';
import config from './common/config';
import Layout from './Layout';

const { routerPrefix } = config;

// 设置默认的加载组件
dynamic.setDefaultLoadingComponent(() => {
    return <PageLoading />;
});

const getExceptionRouters = (app) => [
    {
        name: '403',
        path: '/exception/403',
        component: () => <Exception type="403" />
    },
    {
        name: '404',
        path: '/exception/404',
        component: () => <Exception type="404" />
    },
    {
        name: '500',
        path: '/exception/500',
        component: () => <Exception type="500" />
    }
];

// 抽象化菜单配置
const getNavList = (app) => {
    let navList = {};
    navList.dashboardChildren = [
        {
            name: I18N.src.router.diaoYongDaPan,
            enName: 'Consumer Data',
            icon: 'list',
            path: '/dashboard/appChannel',
            component: dynamic({
                app,
                component: () => import('./routes/Dashboard/ConsumerData')
            })
        },
        {
            name: I18N.src.router.shuJuYuanDaPan,
            enName: 'Provider Data',
            icon: 'list',
            path: '/dashboard/dataService',
            component: dynamic({
                app,
                component: () => import('./routes/Dashboard/ProviderData')
            })
        },
        {
            name: I18N.src.router.zhiShiGaiLan,
            enName: 'Overview Knowledge',
            icon: 'list',
            path: '/dashboard/overviewKnowledge',
            component: dynamic({
                app,
                component: () => import('./routes/Dashboard/OverviewKnowledge')
            })
        }
    ];
    navList.supplierManagementChildren = [
        {
            name: I18N.src.router.heTongGuanLi,
            enName: 'Contract List',
            icon: 'list',
            path: '/supplierManagement/supplierList/contractList',
            component: dynamic({
                app,
                component: () => import('./routes/SupplierManagement/ContractList')
            })
        },
        {
            name: I18N.src.router.heZuoFangGuanLi,
            enName: 'Supplier List',
            icon: 'list',
            path: '/supplierManagement/supplierList',
            component: dynamic({
                app,
                component: () => import('./routes/SupplierManagement/SupplierList')
            })
        },
        {
            name: I18N.src.router.sanFangFuWuJie,
            enName: 'DataService List',
            icon: 'list',
            path: '/supplierManagement/dataServiceList/addModify',
            exact: true,
            // notRender: true,
            component: dynamic({
                app,
                component: () => import('./routes/SupplierManagement/DataServiceList/Inner/AddModify')
            })
        },
        {
            name: I18N.src.router.sanFangFuWuJie,
            enName: 'DataService List',
            icon: 'list',
            path: '/supplierManagement/dataServiceList/addModify/sync',
            exact: true,
            // notRender: true,
            component: dynamic({
                app,
                component: () => import('./routes/SupplierManagement/DataServiceList/Inner/AddModify/SyncService')
            })
        },
        {
            name: '三方服务接口管理-详情',
            enName: 'DataService Detail',
            icon: 'list',
            path: '/supplierManagement/dataServiceList/detail',
            exact: true,
            component: dynamic({
                app,
                component: () => import('./routes/SupplierManagement/DataServiceList/Inner/Detail')
            })
        },
        {
            name: I18N.src.router.mOCKPei,
            enName: 'Mock Config',
            icon: 'list',
            path: '/supplierManagement/dataServiceList/mockConfig',
            // notRender: true,
            component: dynamic({
                app,
                component: () => import('./routes/SupplierManagement/DataServiceList/Inner/Detail/MockConfig')
            })
        },
        {
            name: I18N.components.classifyconfig.baoWenGuanLi,
            enName: 'DataService List',
            icon: 'list',
            path: '/supplierManagement/dataServiceList/parameterList',
            exact: true,
            component: dynamic({
                app,
                component: () => import('./routes/Parameter/List')
            })
        },
        {
            name: I18N.src.router.shuJuYuanFuWu,
            enName: 'DataService List',
            icon: 'list',
            path: '/supplierManagement/dataServiceList',
            component: dynamic({
                app,
                component: () => import('./routes/SupplierManagement/DataServiceList')
            })
        },
        {
            name: I18N.src.router.eTLChuLi,
            enName: 'ETL Management',
            icon: 'list',
            exact: false,
            path: '/supplierManagement/etl',
            component: dynamic({
                app,
                component: () => import('./routes/SupplierManagement/Etl/index')
            })
        },
        {
            name: I18N.src.router.fuWuJieKouGuan,
            enName: 'Service interface management',
            icon: 'list',
            exact: false,
            path: '/interface/management',
            component: dynamic({ app, component: () => import('./routes/InterfaceManagement/index') })
        }
    ];
    navList.accountManagementChildren = [
        {
            name: I18N.src.router.diaoYongFangJiFei,
            enName: 'Internal',
            icon: 'list',
            path: '/accountManagement/internal',
            component: dynamic({
                app,
                component: () => import('./routes/AccountManagement/Internal')
            })
        },
        {
            name: I18N.src.router.shuJuYuanJiFei,
            enName: 'External',
            icon: 'list',
            path: '/accountManagement/external',
            component: dynamic({
                app,
                component: () => import('./routes/AccountManagement/External')
            })
        },
        {
            name: I18N.src.router.heTongJiFei,
            enName: 'ContractBillingDetail',
            icon: 'list',
            path: '/accountManagement/contractBillingDetail',
            component: dynamic({
                app,
                component: () => import('./routes/AccountManagement/ContractBillingDetail')
            })
        }
    ];
    navList.appServiceCenterChildren = [
        {
            name: I18N.src.router.diaoYongFangFuWu3,
            enName: 'AppServiceList',
            icon: 'list',
            path: '/appServiceCenter/appServiceList',
            component: dynamic({
                app,
                component: () => import('./routes/AppServiceCenter/AppServiceList')
            })
        },
        {
            name: I18N.src.router.diaoYongFangFuWu2,
            enName: 'AppServiceList',
            icon: 'list',
            exact: true,
            path: '/appServiceCenter/serviceGroup/detail',
            component: dynamic({
                app,
                component: () => import('./routes/AppServiceCenter/ServiceGroup/AddModify')
            })
        },
        {
            name: I18N.src.router.diaoYongFangFuWu,
            enName: 'AppServiceList',
            icon: 'list',
            path: '/appServiceCenter/serviceGroup',
            component: dynamic({
                app,
                component: () => import('./routes/AppServiceCenter/ServiceGroup')
            })
        }
    ];
    navList.dataManagementChildren = [
        {
            name: I18N.src.router.shuJuYuanDiaoYong,
            enName: 'ThreeCallDetail',
            icon: 'list',
            path: '/dataManagement/threeCallDetail',
            component: dynamic({
                app,
                component: () => import('./routes/DataManagement/ThreeCallDetail')
            })
        },
        {
            name: I18N.src.router.diaoYongFangDiaoYong,
            enName: 'BusinessChannel',
            icon: 'list',
            path: '/dataManagement/businessChannel',
            component: dynamic({
                app,
                component: () => import('./routes/DataManagement/BusinessChannel')
            })
        },
        {
            name: I18N.src.router.shouGongChaXun,
            enName: 'PageQuery',
            icon: 'list',
            path: '/dataManagement/pageQuery',
            component: dynamic({
                app,
                component: () => import('./routes/DataManagement/PageQuery')
            })
        }
    ];
    navList.monitorWarningChildren = [
        {
            name: I18N.src.router.shuJuYuanYiChang,
            enName: 'ThreeDataMonitor',
            icon: 'list',
            path: '/monitorWarning/threeDataMonitor',
            component: dynamic({
                app,
                component: () => import('./routes/MonitorWarning/ThreeDataMonitor')
            })
        },
        {
            name: I18N.src.router.shuJuYuanZongLiu,
            enName: 'ThreeDataRemainWarn',
            icon: 'list',
            path: '/monitorWarning/threeDataRemainWarn',
            component: dynamic({
                app,
                component: () => import('./routes/MonitorWarning/ThreeDataRemainWarn')
            })
        },
        {
            name: I18N.src.router.shuJuYuanQiXian,
            enName: 'SupplierWarning',
            icon: 'list',
            path: '/monitorWarning/supplierWarning',
            component: dynamic({
                app,
                component: () => import('./routes/MonitorWarning/SupplierWarning')
            })
        }
    ];
    navList.batchCall = [
        {
            name: I18N.src.router.renWuXinZeng,
            enName: 'CostAnalysis',
            icon: 'list',
            path: '/batchCall/taskList/add',
            component: dynamic({
                app,
                component: () => import('./routes/BatchCall/TaskList/AddModify')
            })
        },
        {
            name: I18N.src.router.piLiangDiaoYongRen,
            enName: 'CostAnalysis',
            icon: 'list',
            path: '/batchCall/taskList',
            component: dynamic({
                app,
                component: () => import('./routes/BatchCall/TaskList')
            })
        }
    ];

    navList.workflow = [
        {
            name: I18N.src.router.gongZuoLiu,
            enName: 'work flow',
            icon: 'deal-log',
            exact: false,
            path: '/workflow/arrange',
            component: dynamic({ app, component: () => import('./routes/WorkFlow') })
        },
        {
            name: I18N.src.router.liuChengMoBan,
            enName: 'work flow',
            icon: 'deal-log',
            exact: false,
            path: '/workflow/sub',
            component: dynamic({ app, component: () => import('./routes/WorkFlowSub') })
        },
        {
            name: I18N.src.router.hanShuKu,
            enName: 'Workflow template',
            icon: 'workflow',
            path: '/formula',
            component: dynamic({
                app,
                component: () => import('./routes/Formula')
            })
        }
    ];

    navList.callerMonitoringChildren = [
        {
            name: I18N.src.router.fuWuJieKouTiao,
            enName: 'ServiceInterfaceCallDetail',
            icon: 'list',
            path: '/callerMonitoring/serviceInterfaceCallDetail',
            component: dynamic({
                app,
                component: () => import('./routes/CallerMonitoring/ServiceInterfaceCallDetail')
            })
        }
    ];
    return navList;
};

export default ({ history, app, actions }) => {
    const navlist = Object.values(getNavList(app));
    const normalRoutes = flatten(navlist);
    const navs = [...normalRoutes, ...getExceptionRouters(app)].map((item) => ({
        ...item,
        path: `${routerPrefix}${item.path}`
    }));

    return (
        <ConfigProvider
            locale={createOtp({
                cn: zhCN,
                en: enUS
            })}
            renderEmpty={renderEmpty}>
            <Router history={history}>
                <Layout history={history} navs={navs} actions={actions}>
                    <Switch>
                        {navs.map(({ exact = false, path, component }) => {
                            return <Route exact={exact} key={path} path={path} component={component} />;
                        })}
                        <Redirect exact from="/" to={`${routerPrefix}`} />
                        <Redirect exact from={`${routerPrefix}`} to={get(navs, '0.path')} />
                        <Redirect exact to={`${routerPrefix}/exception/404`} />
                    </Switch>
                </Layout>
            </Router>
        </ConfigProvider>
    );
};
