import config from '@/common/config';
import { getLang } from '@/utils/I18N';
import { findMenuInfoByPath, isNewTarget, openNewTarget } from '@/utils/openWeb';
import { MultiUser, NoOperate } from '@tntd/user-status-modal';
import { connect } from 'dva';
import { matchPath, withRouter } from 'dva/router';
import EventEmitter from 'eventemitter3';
import { cloneDeep, get } from 'lodash';
import { useEffect } from 'react';
import { AuthContext, DevelopmentLogin, TntdLayout, PageLoading } from 'tntd';
import Cookies from 'universal-cookie';
import { traverseTree } from './utils/utils';

const { HeaderActionItem } = TntdLayout;
const { routerPrefix } = config;

window.eventEmitter = new EventEmitter();

const Shell = ({ globalStore = {}, dispatch, history, children, actions }) => {
    const {
        currentUser,
        currentOrgCode,
        currentApp,
        appList,
        customTree = {},
        showOrg,
        orgList,
        multiUserModal,
        allMapReady,
        menuTreeReady,
        showOrgApp,
        orgAppList,
        showApp
    } = globalStore;
    const { menuTree = [], name, enName, logo, extendMap = {} } = customTree || {};
    const lang = getLang();
    // 机构格式转换
    let orgTree = null;
    if (showOrg && orgList?.length) {
        orgTree = traverseTree(cloneDeep(orgList), (item = {}) => {
            item.value = item?.uuid;
            item.key = item?.uuid;
        });
        if (orgTree?.length) {
            orgTree = orgTree[0];
        }
    }

    // 获取菜单key
    const getSelecteMenuKey = () => {
        const { subMenu } = findMenuInfoByPath(menuTree, location.pathname);
        return get(subMenu, 'code');
    };

    // 机构回调 获取机构及用户权限下的应用列表
    const onOrgChange = (curOrgTree) => {
        dispatch({
            type: 'global/getAppAndNetWorkByOrgId',
            actions,
            payload: {
                curOrgTree
            }
        });
        window.eventEmitter.emit('appChange', true);
    };

    // 应用切换
    const onAppChange = (currentApp) => {
        const curApp = {
            dName: currentApp.name,
            name: currentApp.key
        };
        window.eventEmitter.emit('appChange', true);
        dispatch({
            type: 'global/setAttrValue',
            payload: {
                currentApp: curApp
            }
        });
    };

    // 语言切换
    const onLanguageChange = (language) => {
        let { personalMode } = globalStore;
        personalMode.lang = language;
        dispatch({
            type: 'global/setAttrValue',
            payload: {
                personalMode
            }
        });
        localStorage.setItem('lang', language);
        const cookies = new Cookies();
        cookies.set('lang', language, { path: '/' });
    };

    const onMenuChange = ({ data }) => {
        if (data.path.startsWith(routerPrefix)) {
            window.eventEmitter.emit('menuClick', true);
        }
    };

    // 退出登录
    const onLogout = () => {
        dispatch({
            type: 'login/logout'
        });
    };

    // 缺省空白页面
    const isEmptyLayout = [].find((path) => matchPath(location.pathname, { path }));

    // 初始化
    useEffect(() => {
        // 如果没有csrf则默认跳转到登录页面
        if (!sessionStorage.getItem('_csrf_') && process.env.SYS_ENV !== 'development') {
            // 如果sessionStorage不存在
            const { origin, pathname, search } = window.location || {};
            const callbackUrl = origin + pathname + encodeURIComponent(search);
            window.location.href = '/user/login?callbackUrl=' + callbackUrl;
        }
        // 加载接口
        dispatch({
            type: 'global/getUserInfo',
            actions,
            dispatch
        });
        dispatch({
            type: 'global/getUserMenuTree',
            actions
        });
        dispatch({
            type: 'global/getAllMap'
        });
        dispatch({
            type: 'global/commonDict'
        });
        dispatch({
            type: 'global/getAllAppList'
        });
        dispatch({
            type: 'global/getAllOrgList'
        });
        dispatch({
            type: 'global/getAllThreeService'
        });
        dispatch({
            type: 'global/getAllProvider'
        });
        dispatch({
            type: 'global/getAllFieldList'
        });
        dispatch({
            type: 'global/getWorkflowConfig'
        });

        const { CURRENT_APP_CHANGE, MENU_CHANGE, LANGUAGE_CHANGE, ON_LOGOUT, CURRENT_ORG_CHANGE } = actions ? actions.EVENTS_ENUM : {};

        if (actions) {
            actions.on(CURRENT_APP_CHANGE, onAppChange);
            actions.on(MENU_CHANGE, onMenuChange);
            actions.on(LANGUAGE_CHANGE, onLanguageChange);
            actions.on(ON_LOGOUT, onLogout);
            actions.on(CURRENT_ORG_CHANGE, onOrgChange);
        }

        return () => {
            if (actions) {
                actions.off(CURRENT_APP_CHANGE, onAppChange);
                actions.off(MENU_CHANGE, onMenuChange);
                actions.off(LANGUAGE_CHANGE, onLanguageChange);
                actions.off(ON_LOGOUT, onLogout);
                actions.off(CURRENT_ORG_CHANGE, onOrgChange);
            }
        };
    }, []);

    const userStatusModal = () => {
        return (
            <>
                {/* 长时间登陆无任何操作，弹框提示并跳转登陆页 */}
                <NoOperate
                    lang={lang}
                    noOperateTime={1800000}
                    modalShowEvent={() => {
                        // 弹窗回调
                        dispatch({
                            type: 'login/signOut'
                        });
                    }}
                    modalCloseEvent={() => {
                        // 点击弹窗确定回调
                        dispatch({
                            type: 'login/goLogin'
                        });
                    }}
                />

                {/* 多终端登录 */}
                <MultiUser
                    lang={lang}
                    showModal={multiUserModal}
                    modalShowEvent={() => {
                        // 弹窗回调
                        dispatch({
                            type: 'login/signOut'
                        });
                    }}
                    modalCloseEvent={() => {
                        // 点击弹窗确定回调
                        dispatch({
                            type: 'login/goLogin'
                        });
                        dispatch({
                            type: 'global/setAttrValue',
                            payload: {
                                multiUserModal: false
                            }
                        });
                    }}
                />
            </>
        );
    };

    if (actions) {
        return (
            <>
                {allMapReady && menuTreeReady ? (
                    <div className="subapp-wrap" key={`${currentOrgCode}_${currentApp.name}`}>
                        {children}
                        {userStatusModal()}
                    </div>
                ) : (
                    <PageLoading />
                )}
            </>
        );
    }

    return (
        <TntdLayout
            key={`${currentOrgCode}_${currentApp.name}`}
            className="tianzuo-layout"
            appKey="handle"
            name={name}
            enName={enName}
            logo={
                logo ? (
                    <img className="logo" style={{ opacity: logo && logo.indexOf('white') ? 0.85 : 1 }} src={logo} alt="logo" />
                ) : (
                    <img
                        className="logo"
                        style={{ opacity: logo && logo.indexOf('white') ? 0.85 : 1 }}
                        src={`${process.env.PATH}/logo/logo11-white.svg`}
                        alt="logo"
                    />
                )
            }
            isEmptyLayout={window.isInLightBox || isEmptyLayout}
            userInfo={currentUser}
            menus={menuTree}
            orgList={showOrg && orgTree}
            onOrgChange={onOrgChange}
            orgAppShow={showOrgApp}
            orgAppList={orgAppList}
            appList={showApp ? appList : null}
            selectedMenuKey={getSelecteMenuKey()}
            changeRouter={(path) => history.push(path)}
            extendMap={extendMap}
            onMenuChange={onMenuChange}
            onLogout={onLogout}
            onAppChange={!window.lightBoxActions && onAppChange}
            // 语言切换
            onLanguageChange={onLanguageChange}
            // 开发模式增加登录
            extralHeaderActions={[
                process.env.SYS_ENV === 'development' && (
                    <HeaderActionItem key="help" onClick={() => {}}>
                        <DevelopmentLogin
                            signIn={(params) =>
                                dispatch({
                                    type: 'login/mockLogin',
                                    payload: params
                                })
                            }
                        />
                    </HeaderActionItem>
                )
            ]}
            config={{
                language: false,
                application: false,
                theme: true,
                avatar: true
            }}
            onBeforeMenuChange={({ data }) => {
                const { path } = data || {};
                if (path && isNewTarget(path)) {
                    openNewTarget({
                        path,
                        userInfo: currentUser
                    });
                    return false;
                }
                return true;
            }}
            onMenuSelect={({ data }) => {
                const { path } = data || {};
                if (path && isNewTarget(path)) {
                    return;
                }
                const inApp = path.startsWith(routerPrefix);
                if (inApp) {
                    window.eventEmitter.emit('menuClick', true);
                    history.push(path);
                } else {
                    window.location.href = path;
                }
            }}
            // 系统之间切换
            onApplicationChange={(appKey) => {
                window.location.href = `/${appKey}`;
            }}>
            <AuthContext.Consumer>
                {(auth) => {
                    window.auth = auth;
                    return allMapReady && menuTreeReady ? children : <PageLoading />;
                }}
            </AuthContext.Consumer>
            {userStatusModal()}
        </TntdLayout>
    );
};

export default withRouter(
    connect((state) => ({
        globalStore: state.global,
        loginStore: state.login
    }))(Shell)
);
