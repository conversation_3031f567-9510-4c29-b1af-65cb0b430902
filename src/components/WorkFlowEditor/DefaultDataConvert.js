import I18N from '@/utils/I18N';
import { message, Modal } from 'tntd';
import { numAdd } from '@/utils/utils';
import otp from './otp';
const DefaultConvert = {
    // 增加节点错误
    addNodeError(node) {
        node.data.className = 'error';
        node.addClass('error');
    },
    rmNodeError(node) {
        if (node.hasClass('error')) {
            node.data.className = '';
            node.removeClass('error');
        }
    },
    // 解析数据
    convert(data) {
        const res = {
            nodes: [],
            lines: []
        };
        const nodesMap = {};
        const { flowNodeDefinitions = [], flowLineDefinitions = [] } = data;
        res.nodes = flowNodeDefinitions.map((item) => {
            nodesMap[item.id] = item;
            item.attributes = item.attributes || {};
            item.incomingFields = item.incomingFields || [];
            item.outgoingFields = item.outgoingFields || [];
            const node = {
                uuid: item.id,
                x: item.x,
                y: item.y,
                type: item.nodeType,
                name: item.name,
                data: {}
            };
            switch (item.nodeType) {
                case 'StartFlowNode':
                    node.name = otp.StartFlowNodeName;
                    break;
                case 'EndFlowNode':
                    node.name = otp.EndFlowNodeName;
                    break;
                case 'FunctionServiceNode':
                    node.data = {
                        code: item.attributes.functionUuid,
                        name: item.attributes.functionName,
                        inputParams: item.incomingFields.map((each) => {
                            return {
                                fieldName: each.fieldName,
                                field: each.mappingName
                            };
                        }),
                        outputParams: item.outgoingFields.map((each) => {
                            return {
                                fieldName: each.fieldName,
                                field: each.mappingName
                            };
                        })
                    };

                    break;

                case 'ExclusiveGateway':
                    node.data.type = item.attributes.type;
                    if (node.data.type === 'start') {
                        node.name = otp.ExclusiveGatewayStartName;
                    } else {
                        node.name = otp.ExclusiveGatewayEndName;
                    }
                    break;

                case 'ParallelGateway':
                    node.data.type = item.attributes.type;
                    if (node.data.type === 'start') {
                        node.name = otp.ParallelGatewayStartName;
                    } else {
                        node.name = otp.ParallelGatewayEndName;
                    }
                    break;

                case 'SuspendFlowNode':
                    node.name = item.name || I18N.workfloweditor.defaultdataconvert.jiXuBuChong;
                    node.data = {
                        serviceCode: item.attributes.serviceCode
                    };
                    break;

                case 'FeatureServiceNode': {
                    const [incomingFields, outgoingFields] = [[], []];
                    item?.incomingFields?.map((income) => {
                        incomingFields.push({
                            displayName: income.displayName,
                            field: income.fieldName,
                            serviceParam: income.mappingName
                        });
                    });
                    item?.outgoingFields?.map((out) => {
                        outgoingFields.push({
                            displayName: out.displayName,
                            field: out.fieldName,
                            serviceParam: out.mappingName
                        });
                    });
                    node.name = item.attributes.thirdServiceName;

                    const { originInput = {}, originOutput = {} } = item?.attributes || {};
                    const originInputTemp = Object.keys(originInput)?.map((oI) => ({
                        serviceParam: oI,
                        field: originInput[oI]
                    }));
                    const originOutputTemp = Object.keys(originOutput)?.map((oU) => ({
                        serviceParam: oU,
                        field: originOutput[oU]
                    }));
                    node.data = {
                        ...item.attributes,
                        originInput: originInputTemp,
                        originOutput: originOutputTemp,
                        incomingFields,
                        outgoingFields
                    };
                    break;
                }
                case 'SubDecisionFlowNode': {
                    node.data = {
                        flowLineDefinitions: item.flowLineDefinitions,
                        flowNodeDefinitions: item.flowNodeDefinitions,
                        name: item.name
                    };
                    break;
                }

                case 'RouteServiceNode': {
                    node.name = item.name || I18N.workfloweditor.defaultdataconvert.zhiNengLuYou;
                    node.data.name = node.name;
                    break;
                }
                case 'DataTableServiceNode': {
                    node.data = item.attributes;
                    node.incomingFields = item?.incomingFields;
                    node.outgoingFields = item?.outgoingFields;
                    node.name = item.name;

                    break;
                }
                default:
                    node.data = { ...item.attributes };
                    break;
            }
            return node;
        });
        res.lines = flowLineDefinitions.map((item) => {
            item.attributes = item.attributes || {};
            const line = {
                uuid: item.id,
                fromPoint: item.fromPoint,
                toPoint: item.toPoint,
                data: {},
                from: item.sourceNodeId,
                to: item.targetNodeId
            };
            switch (item.lineType) {
                case 'ExclusiveConditionLine':
                    line.data = {
                        isDefault: item.attributes.isDefault,
                        priority: item.attributes.priority,
                        condition: item.attributes.condition ? JSON.parse(item.attributes.condition || '[]') : undefined,
                        conditionName: item.name
                    };
                    let isExclusiveComplete = true;
                    if (item.attributes.isDefault) {
                        if (!item.name) {
                            isExclusiveComplete = false;
                        }
                    } else {
                        if (!item.attributes.condition) {
                            isExclusiveComplete = false;
                        }
                    }
                    line.label = item.name;
                    line.required = true;
                    line.className = isExclusiveComplete ? '' : 'red-line';
                    break;
                case 'RouteConditionLine':
                    line.data = {
                        ratio: item.attributes.ratio,
                        conditionName: item.name
                    };
                    const isRouteComplete = item.attributes.ratio || item.attributes.ratio === 0;
                    line.label = item.name;
                    line.required = true;
                    line.className = isRouteComplete ? '' : 'red-line';
                    break;
                default:
                    break;
            }
            return line;
        });

        return res;
    },
    // 构造数据
    format(data, editor, noMessage = false) {
        const res = {
            flowNodeDefinitions: [],
            flowLineDefinitions: []
        };
        const {
            graph: { node, line }
        } = editor;
        const { nodes = [], lines } = data;
        let errorMsgList = [];
        const nodesTypeMap = {};
        nodes.map((item, i) => {
            const { toLines, fromLines } = node.nodes[item.uuid];
            const nodeData = item.data || {};
            const data = {
                x: item.x,
                y: item.y,
                id: item.uuid,
                name: item.name,
                nodeType: item.type,
                attributes: { ...(nodeData || {}) },
                incomingFields: [],
                outgoingFields: []
            };
            const checkInOut = (str) => {
                if (!fromLines.size && !toLines.size) {
                    errorMsgList.push(
                        <p key={`7-${i}`}>
                            [{str}
                            {I18N.workfloweditor.defaultdataconvert.queShaoShuRuShu}
                        </p>
                    );
                } else if (!fromLines.size) {
                    errorMsgList.push(
                        <p key={`7-${i}`}>
                            [{str}
                            {I18N.workfloweditor.defaultdataconvert.queShaoShuRuLiu}
                        </p>
                    );
                } else if (!toLines.size) {
                    errorMsgList.push(
                        <p key={`7-${i}`}>
                            [{str}
                            {I18N.workfloweditor.defaultdataconvert.queShaoShuChuLiu}
                        </p>
                    );
                } else {
                    return true;
                }
                return false;
            };
            switch (item.type) {
                // 开始节点
                case 'StartFlowNode': {
                    if (toLines.size < 1) {
                        // this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(<p key={`start-${i}`}>[{I18N.workfloweditor.defaultdataconvert.kaiShiQueShaoShu}</p>);
                    }
                    if (nodesTypeMap['StartFlowNode']) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(<p key={`start-${i}`}>{I18N.workfloweditor.defaultdataconvert.kaiShiKaiShiJie}</p>);
                    }
                    nodesTypeMap['StartFlowNode'] = true;
                    break;
                }

                // 结束节点
                case 'EndFlowNode': {
                    if (fromLines.size < 1) {
                        // this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(<p key={`end-${i}`}>{I18N.workfloweditor.defaultdataconvert.jieShuQueShaoShu}</p>);
                    }
                    break;
                }
                //函数节点
                case 'FunctionServiceNode':
                    checkInOut(I18N.workfloweditor.defaultdataconvert.jiaoBenHanShu);
                    if (!nodeData.code) {
                        errorMsgList.push(<p key={i}>{I18N.workfloweditor.defaultdataconvert.hanShuWeiSheZhi}</p>);
                    }
                    data.attributes = {
                        functionUuid: nodeData.code,
                        functionName: nodeData.name
                    };
                    data.incomingFields = nodeData.inputParams?.map((item) => {
                        return {
                            fieldName: item.fieldName,
                            mappingName: item.field
                        };
                    });
                    data.outgoingFields = nodeData.outputParams?.map((item) => {
                        return {
                            fieldName: item.fieldName,
                            mappingName: item.field
                        };
                    });
                    break;

                // 判断
                case 'ExclusiveGateway': {
                    checkInOut(I18N.workfloweditor.defaultdataconvert.panDuan);
                    nodeData.type = nodeData.type !== 'end' ? 'start' : 'end';
                    const lines = nodeData.type !== 'end' ? toLines : fromLines;
                    if (lines.size < 2) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(
                            <p key={`exclusive-${i}`}>
                                [{item.name}
                                {I18N.workfloweditor.defaultdataconvert.zhiShaoPeiZhiTiao2}
                                {nodeData.type === 'start'
                                    ? I18N.workfloweditor.defaultdataconvert.shuChu
                                    : I18N.workfloweditor.defaultdataconvert.shuRu}
                                {I18N.workfloweditor.defaultdataconvert.xian}
                            </p>
                        );
                    }
                    if (nodeData.type === 'start') {
                        let defaultNum = 0;
                        let flag = true;
                        lines.forEach((item) => {
                            const processFlowCondition = line.lines[item].data?.data || {};
                            processFlowCondition.isDefault && defaultNum++;
                            if (!processFlowCondition?.conditionName) {
                                flag = false;
                            }
                        });
                        if (!flag) {
                            this.addNodeError(node.nodes[item.uuid]);
                            errorMsgList.push(<p key={`exclusive-${i}`}>{I18N.workfloweditor.defaultdataconvert.panDuanKaiShiTiao}</p>);
                        }
                        if (defaultNum !== 1) {
                            this.addNodeError(node.nodes[item.uuid]);
                            errorMsgList.push(
                                <p key={`exclusive-${i}`}>
                                    [{item.name}
                                    {I18N.workfloweditor.defaultdataconvert.xuYaoQieZhiNeng}
                                </p>
                            );
                        }
                    }
                    data.attributes.type = nodeData.type;
                    break;
                }

                // 并行
                case 'ParallelGateway': {
                    checkInOut(I18N.workfloweditor.defaultdataconvert.bingXing2);
                    nodeData.type = nodeData.type !== 'end' ? 'start' : 'end';
                    const paraLines = nodeData.type === 'end' ? fromLines : toLines;
                    if (paraLines.size < 2) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(
                            <p key={`parallel-${i}`}>
                                {I18N.workfloweditor.defaultdataconvert.bingXing}
                                {nodeData.type === 'end'
                                    ? I18N.workfloweditor.defaultdataconvert.jieShu
                                    : I18N.workfloweditor.defaultdataconvert.kaiShi}
                                {I18N.workfloweditor.defaultdataconvert.zhiShaoPeiZhiTiao2}
                                {nodeData.type === 'start'
                                    ? I18N.workfloweditor.defaultdataconvert.shuChu
                                    : I18N.workfloweditor.defaultdataconvert.shuRu}
                                {I18N.workfloweditor.defaultdataconvert.xian}
                            </p>
                        );
                    }

                    if (paraLines.size > 5) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(
                            <p key={`parallel-${i}`}>
                                {I18N.workfloweditor.defaultdataconvert.bingXing}
                                {nodeData.type === 'end'
                                    ? I18N.workfloweditor.defaultdataconvert.jieShu
                                    : I18N.workfloweditor.defaultdataconvert.kaiShi}
                                {I18N.workfloweditor.defaultdataconvert.zuiDuoPeiZhi}
                                {5}
                                {I18N.workfloweditor.defaultdataconvert.tiao}
                                {nodeData.type === 'start'
                                    ? I18N.workfloweditor.defaultdataconvert.shuChu
                                    : I18N.workfloweditor.defaultdataconvert.shuRu}
                                {I18N.workfloweditor.defaultdataconvert.xian}
                            </p>
                        );
                    }

                    if (nodeData.type === 'end') {
                        if (toLines.size > 1) {
                            this.addNodeError(node.nodes[item.uuid]);
                            errorMsgList.push(
                                <p key={`parallel-${i}`}>
                                    [{item.name}
                                    {I18N.workfloweditor.defaultdataconvert.zhiNengPeiZhiYi}
                                </p>
                            );
                        }
                    }
                    data.attributes.type = nodeData.type;
                    break;
                }

                // API服务
                case 'FeatureServiceNode': {
                    checkInOut(I18N.workfloweditor.defaultdataconvert.aPIJieKou2);
                    const {
                        incomingFields,
                        outgoingFields,
                        originInput: originInputTemp,
                        originOutput: originOutputTemp,
                        ...rest
                    } = nodeData || {};
                    if (!nodeData?.thirdServiceCode) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(<p key={`feature-${i}`}>{I18N.workfloweditor.defaultdataconvert.aPIJieKou}</p>);
                    }
                    // 原始映射关系
                    const [originInput, originOutput] = [{}, {}];
                    originInputTemp?.forEach((v) => {
                        originInput[v?.serviceParam] = v?.field;
                    });
                    originOutputTemp?.forEach((v) => {
                        originOutput[v?.serviceParam] = v?.field;
                    });
                    data.attributes = {
                        ...rest,
                        originInput,
                        originOutput
                    };

                    // 重新映射关系
                    data.incomingFields = incomingFields
                        ?.filter((item) => item.field && item.serviceParam && item.type !== 'constant')
                        ?.map((item) => {
                            return {
                                displayName: item.displayName,
                                fieldName: item.field,
                                mappingName: item.serviceParam
                            };
                        });
                    data.outgoingFields = outgoingFields?.map((item) => {
                        return {
                            displayName: item.displayName,
                            fieldName: item.field,
                            mappingName: item.serviceParam
                        };
                    });

                    break;
                }

                // 智能路由
                case 'RouteServiceNode': {
                    checkInOut(I18N.workfloweditor.defaultdataconvert.zhiNengLuYou);
                    const lines = toLines;
                    if (!nodeData?.name) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(<p key={`feature-${i}`}>{I18N.workfloweditor.defaultdataconvert.zhiNengLuYouWei}</p>);
                    }

                    if (lines.size < 2) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(
                            <p key={`exclusive-${i}`}>
                                [{item.name}
                                {I18N.workfloweditor.defaultdataconvert.zhiShaoPeiZhiTiao}
                            </p>
                        );
                    }
                    let [nameFlag, ratioFlag, total] = [true, true, 0];
                    lines.forEach((item) => {
                        const processFlowCondition = line.lines[item].data?.data || {};
                        if (!processFlowCondition?.conditionName) {
                            nameFlag = false;
                        }
                        if (!processFlowCondition?.ratio && processFlowCondition?.ratio !== 0) {
                            ratioFlag = false;
                        }
                        total = numAdd(total, processFlowCondition?.ratio);
                    });
                    if (!nameFlag) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(
                            <p key={`exclusive-${i}`}>
                                [{item.name}
                                {I18N.workfloweditor.defaultdataconvert.tiaoJianPeiZhiWei}
                            </p>
                        );
                    }
                    if (!ratioFlag) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(
                            <p key={`exclusive-${i}`}>
                                [{item.name}
                                {I18N.workfloweditor.defaultdataconvert.fenLiuBiLiWei}
                            </p>
                        );
                    }
                    if (total !== 100) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(
                            <p key={`exclusive-${i}`}>
                                [{item.name}
                                {I18N.workfloweditor.defaultdataconvert.fenLiuBiLiLei}
                            </p>
                        );
                    }
                    break;
                }

                // 继续补充
                case 'SuspendFlowNode': {
                    checkInOut(I18N.workfloweditor.defaultdataconvert.jiXuBuChong);
                    if (!nodeData?.serviceCode) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(<p key={`suspend-${i}`}>{I18N.workfloweditor.defaultdataconvert.jiXuBuChongWei}</p>);
                    }
                    data.attributes = {
                        serviceCode: nodeData?.serviceCode
                    };
                    break;
                }

                // 电邮预警
                case 'MailServiceNode': {
                    checkInOut(I18N.workfloweditor.defaultdataconvert.dianYouYuJing);

                    if (!nodeData?.mailSubject) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(<p key={`suspend-${i}`}>{I18N.workfloweditor.defaultdataconvert.dianYouYuJingWei3}</p>);
                    }
                    if (!nodeData?.mailToAddress) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(<p key={`suspend-${i}`}>{I18N.workfloweditor.defaultdataconvert.dianYouYuJingWei2}</p>);
                    }
                    if (!nodeData?.mailContent) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(<p key={`suspend-${i}`}>{I18N.workfloweditor.defaultdataconvert.dianYouYuJingWei}</p>);
                    }
                    data.attributes = {
                        mailSubject: nodeData?.mailSubject,
                        mailToAddress: nodeData?.mailToAddress,
                        mailContent: nodeData?.mailContent
                    };
                    break;
                }

                // 流程模板
                case 'SubDecisionFlowNode': {
                    checkInOut(I18N.workfloweditor.defaultdataconvert.liuChengMuBan);
                    if (!nodeData?.name) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(<p key={`suspend-${i}`}>{I18N.workfloweditor.defaultdataconvert.liuChengMuBanWei}</p>);
                    }
                    data.flowLineDefinitions = nodeData?.flowLineDefinitions;
                    data.flowNodeDefinitions = nodeData?.flowNodeDefinitions;

                    break;
                }

                // 数据表
                case 'DataTableServiceNode': {
                    checkInOut(I18N.constants.index.shuJuBiao);
                    if (!data?.name) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(<p key={`suspend-${i}`}>{I18N.workfloweditor.defaultdataconvert.shuJuBiaoMingCheng}</p>);
                    }
                    data.attributes = nodeData;
                    if (!data.attributes.dataSourceType) {
                        this.addNodeError(node.nodes[item.uuid]);
                        errorMsgList.push(<p key={`suspend-${i}`}>{I18N.workfloweditor.defaultdataconvert.shuJuYuanLeiXing}</p>);
                    }
                    console.log('data.attributes: ', data.attributes);
                    if (data.attributes.type === 'filterSet') {
                        if (!data.attributes.dataTable) {
                            errorMsgList.push(<p key={`suspend-${i}`}>{I18N.workfloweditor.defaultdataconvert.shuJuBiaoBuNeng}</p>);
                        }
                    } else if (data.attributes.type === 'sqlSet') {
                        const ifNull = data.attributes.sqlParams?.some((v) => !v.name || v.name === '' || !v.value || v.value === '');
                        if (ifNull) {
                            this.addNodeError(node.nodes[item.uuid]);
                            errorMsgList.push(<p key={`suspend-${i}`}>{I18N.workfloweditor.defaultdataconvert.canShuPeiZhiGe}</p>);
                        }
                    }

                    data.incomingFields = item.incomingFields?.map((item) => {
                        return {
                            fieldName: item.mappingName,
                            mappingName: item.mappingName
                        };
                    });
                    data.outgoingFields = item.outgoingFields?.map((item) => {
                        return {
                            fieldName: item.fieldName,
                            mappingName: item.mappingName
                        };
                    });
                }
            }
            res.flowNodeDefinitions.push(data);
        });

        res.flowLineDefinitions = lines.map((item) => {
            const lineData = item?.data || {};
            const fromNode = node.nodes[item.from]?.data;

            const data = {
                id: item.uuid,
                fromPoint: item.fromPoint,
                toPoint: item.toPoint,
                attributes: { ...(lineData || {}) },
                sourceNodeId: item.from,
                targetNodeId: item.to
            };
            switch (fromNode.type) {
                case 'ExclusiveGateway': {
                    if (fromNode.data.type === 'start') {
                        data.lineType = 'ExclusiveConditionLine';
                        data.attributes.isDefault = lineData.isDefault;
                        if (!lineData.isDefault) {
                            data.attributes.priority = lineData.priority;
                            data.attributes.condition = lineData.condition ? JSON.stringify(lineData.condition) : null;
                        }
                        data.name = lineData.conditionName;
                    }
                    break;
                }
                case 'RouteServiceNode': {
                    data.lineType = 'RouteConditionLine';
                    data.attributes.ratio = lineData.ratio;
                    data.name = lineData.conditionName;
                    break;
                }

                default:
                    break;
            }
            return data;
        });

        if (!noMessage && errorMsgList.length > 0) {
            const errorMsgListMap = {};
            errorMsgList = errorMsgList.filter((item) => {
                if (errorMsgListMap[item.props.children]) {
                    return false;
                }
                errorMsgListMap[item.props.children] = true;
                return true;
            });
            Modal.warning({
                zIndex: 1100,
                title: I18N.workfloweditor.defaultdataconvert.peiZhiBuHeFa, //
                content: <div>{errorMsgList}</div>
            });
            return false;
        }
        if (!noMessage && res.flowNodeDefinitions.length === 0) {
            message.warn(I18N.workfloweditor.defaultdataconvert.peiZhiBuNengWei);
            return false;
        }
        this.res = res;
        return res;
    }
};
export default DefaultConvert;
