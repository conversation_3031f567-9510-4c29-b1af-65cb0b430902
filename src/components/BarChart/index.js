/*
 * @Author: liu<PERSON>
 * @CreatDate: 2018-08-28 15:47:11
 * @Describe: 柱状图表组件 - 采用echarts第三方库 - macarons主题
 */

import { PureComponent } from 'react';
// 加载echarts，注意引入文件的路径
import Echarts from 'echarts/lib/echarts';
// 再引入你需要使用的图表类型，标题，提示信息等
import 'echarts/lib/chart/bar';
import 'echarts/lib/component/tooltip';

import '../Theme/macarons';

export default class BarChart extends PureComponent {

	state = {
		defaultHeight: 300
	};
	constructor(props) {
		super(props);
		this.draw = this.draw.bind(this);
	}

	componentDidMount() {
		const { idName, chartData } = this.props;
		this.draw(idName, chartData);
	}

	componentDidUpdate(prevProps) {
		let preChartData = prevProps.chartData;
		let nextChartData = this.props.chartData;
		if (preChartData !== nextChartData) {
			// 检测到数据改变，重绘图表
			const { idName, chartData } = this.props;
			this.draw(idName, chartData);
		}
	}

	draw(idName, chartData) {
		if (!chartData) return;
		const dom = document.getElementById(idName);
		const myChart = Echarts.init(dom, 'macarons');
		const option = {
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					type: 'shadow'
				}
			},
			grid: {
				left: 50,
				right: 130,
				bottom: 20,
				top: 40,
				containLabel: true
			},
			xAxis: {
				type: 'category',
				name: chartData.xAxisName ? chartData.xAxisName : null,
				data: chartData.xAxisData ? chartData.xAxisData : [],
				axisPointer: {
					type: 'shadow'
				},
				axisLine: {
					lineStyle: {
						color: '#666'
					}
				},
				axisTick: {
					show: false
				},
				axisLabel: {
					// interval: chartData.xAxis.rotate ? 0 : "auto",
					rotate: chartData.xAxisRotate ? chartData.xAxisRotate : 0,
					// formatter: chartData.xAxisRotate ? (value, index) => {
					// 	let val;
					// 	if (index === 0) {
					// 		if (value.length < 10) {
					// 			val = value;
					// 		} else {
					// 			val = `${value.substr(0, 10)}...`;
					// 		}
					// 	} else if (value.length < 10) {
					// 		val = value;
					// 	} else {
					// 		val = `${value.substr(0, 10)}...`;
					// 	}
					// 	return val;
					// } : null,
					formatter: (value) => {
						let text = value;
						if (value.length > 10) {
							text = `${value.substr(0, 10)}...`;
						}
						return text;
					}
				}
			},
			yAxis: {
				type: 'value',
				name: chartData.yAxisName ? chartData.yAxisName : null,
				axisLine: {
					show: false,
					lineStyle: {
						color: '#666'
					}
				},
				axisTick: {
					show: false
				},
				splitArea: {
					areaStyle: {
						color: ['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0)']
					}
				},
				splitLine: {
					lineStyle: {
						type: 'solid'
					}
				}
			},
			series: [{
				data: chartData.seriesData ? chartData.seriesData : [],
				type: 'bar',
				barMaxWidth: 40,
				itemStyle: {
					barBorderRadius: 4
				}
			}]
		};
		if (option && typeof option === 'object') {
			myChart.setOption(option, true);
			window.addEventListener('resize', () => {
				myChart.resize();
			});
		}
	}

	render() {
		const { defaultHeight } = this.state;
		const { height, idName } = this.props;
		const realHeight = height ? `${height}px` : `${defaultHeight}px`;

		return (
			<div id={idName} style={{ height: realHeight }}></div>
		);
	}
}
