/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-08-13 10:44:32
 * @Describe: 计算公式测试弹框
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import { Modal, Input, Tooltip, InputNumber, Select, DatePicker, message, Button } from 'tntd';
import { formulaAPI } from '@/services';
import DragModalTitle from '@/components/DragModalTitle';
import CommonTable from '../Table';

import './index.less';

const Option = Select.Option;

class TestModal extends PureComponent {
    state = {
        data: [],
        loading: false,
        resultData: [],
        showResultData: false
    };

    componentDidMount() {
        this.getData();
        if (document.querySelector('.m-formula-test-modal')) {
            document.querySelector('.m-formula-test-modal').parentElement.className = 'ant-modal-wrap ant-modal-wrap-test';
        }
    }

    componentDidUpdate(preProps) {
        const preShow = preProps.visible;
        const nextShow = this.props.visible;
        if (preShow !== nextShow && nextShow) {
            this.getData();
            if (document.querySelector('.m-formula-test-modal')) {
                document.querySelector('.m-formula-test-modal').parentElement.className = 'ant-modal-wrap ant-modal-wrap-test';
            }
        }
    }

    getData = () => {
        const { data } = this.props;
        this.setState({ data });
    };

    handleOk = () => {
        const { data } = this.state;
        const { code, type = 1 } = this.props;
        let content = [];

        data.forEach((item) => {
            content.push({
                paramName: item.name,
                paramValue: item.value || item.value === 0 ? item.value : ''
            });
        });
        const params = {
            script: code,
            content: JSON.stringify(content),
            type
        };
        this.setState({ loading: true });
        formulaAPI
            .testFormulaInvoke(params)
            .then((res) => {
                this.setState({ loading: false });
                if (!res) return;
                if (res && res.success) {
                    this.setState({
                        resultData: res.data ? res.data : [],
                        showResultData: true
                    });
                } else {
                    message.error(res?.message || res?.msg);
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    handleCancel = () => {
        this.props.onCancel();
        this.setState({
            resultData: [],
            data: [],
            showResultData: false
        });
    };

    changeField(e, type, field, index) {
        const { data } = this.state;
        let copyData = cloneDeep(data);
        let val;

        if (type === 'input') val = e.target.value;
        if (type === 'select') val = e;
        if (type === 'number') val = e;
        if (type === 'datePicker') val = e;
        copyData[index][field] = val;

        this.setState({ data: copyData });
    }

    render() {
        const { data, loading, resultData, showResultData } = this.state;
        const { visible } = this.props;

        const footerDom = [
            <Button onClick={this.handleCancel} key="cancel">
                {/* 关闭 */}
                {I18N.testmodal.index.guanBi}
            </Button>,
            <Button type="primary" onClick={this.handleOk} key="ok" loading={loading}>
                {/* 执行 */}
                {I18N.testmodal.index.zhiXing}
            </Button>
        ];
        const title = (
            <DragModalTitle
                title={I18N.testmodal.index.ceShi} // 测试
                modalClass="m-formula-test-modal"
            />
        );

        return (
            <Modal
                zIndex={1003}
                mask={false}
                maskClosable={false}
                className="m-formula-test-modal"
                width={770}
                title={title}
                footer={footerDom}
                visible={visible}
                onCancel={this.handleCancel}>
                {data && data.length > 0 && (
                    <div className="clearfix">
                        {data.map((item, index) => {
                            return (
                                <div className="box" key={index}>
                                    <span className="label">
                                        <Tooltip title={item.displayName} placement="left">
                                            {item.displayName}
                                        </Tooltip>
                                    </span>
                                    {/* 数据类型(1字符型/2整型/3小数型/4日期型/5枚举/6布尔) */}
                                    {item.dataType === 1 && (
                                        <Input
                                            className="u-input"
                                            value={item.value}
                                            onChange={(e) => this.changeField(e, 'input', 'value', index)}
                                        />
                                    )}
                                    {item.dataType === 2 && (
                                        <InputNumber
                                            precision={0}
                                            className="u-input"
                                            value={item.value}
                                            onChange={(e) => this.changeField(e, 'number', 'value', index)}
                                        />
                                    )}
                                    {item.dataType === 3 && (
                                        <InputNumber
                                            className="u-input"
                                            value={item.value}
                                            onChange={(e) => this.changeField(e, 'number', 'value', index)}
                                        />
                                    )}
                                    {item.dataType === 4 && (
                                        <DatePicker
                                            format="YYYY-MM-DD HH:mm:ss"
                                            className="u-input"
                                            allowClear={false}
                                            showTime
                                            value={item.value ? moment(item.value) : undefined}
                                            onChange={(date, dateString) => this.changeField(dateString, 'datePicker', 'value', index)}
                                        />
                                    )}
                                    {item.dataType === 5 && (
                                        <Select
                                            dropdownMatchSelectWidth={false}
                                            value={item.value}
                                            className="u-input"
                                            onChange={(e) => this.changeField(e, 'select', 'value', index)}>
                                            {item.extend &&
                                                item.extend.map((ssItem, ssIndex) => {
                                                    return (
                                                        <Option value={ssItem.value} key={ssIndex}>
                                                            {ssItem.description}
                                                        </Option>
                                                    );
                                                })}
                                        </Select>
                                    )}
                                    {item.dataType === 6 && (
                                        <Select
                                            dropdownMatchSelectWidth={false}
                                            value={item.value}
                                            className="u-input"
                                            onChange={(e) => this.changeField(e, 'select', 'value', index)}>
                                            {/* <Option value="true">True</Option> */}
                                            {/* <Option value="false">False</Option> */}
                                            <Option value="true">{I18N.testmodal.index.shiTRUE}</Option>
                                            <Option value="false">{I18N.testmodal.index.fouFALS}</Option>
                                        </Select>
                                    )}
                                </div>
                            );
                        })}
                    </div>
                )}
                {data && data.length === 0 && (
                    <div>
                        {/* 无参数，直接点击执行 */}
                        {I18N.testmodal.index.wuCanShuZhiJie}
                    </div>
                )}
                {resultData && showResultData && (
                    <div className="result clearfix">
                        <div className="title">
                            {/* 结果 */}
                            {I18N.testmodal.index.jieGuo}
                        </div>
                        <CommonTable data={resultData} />
                    </div>
                )}
            </Modal>
        );
    }
}

export default TestModal;
