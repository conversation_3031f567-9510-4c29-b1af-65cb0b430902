/*
 * @Describe: 决策补充弹框
 */
import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { Select, Form, Modal, Button, Icon, Table, Tooltip, Ellipsis } from 'tntd';
import TooltipSelect from '@tntd/tooltip-select';
import DataConvert from '@/components/WorkFlowEditor/DefaultDataConvert';
import { sliceName } from '@/components/WorkFlowEditor';
import service from '../../service';
import otp from '../../otp';

const Option = Select.Option;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 19 }
    }
};

export default Form.create({ name: 'suspend-node' })((props) => {
    const { form, onCancel, dialogShowInfo, editor, disabled, data = {} } = props;
    const { getFieldDecorator, validateFields, setFieldsValue, resetFields, getFieldValue } = form;
    const { orgCode, appCode } = data;
    const { type, nodeId } = dialogShowInfo || {};
    const visible = type === 'SuspendFlowNode';

    const [supplementList, setSupplementList] = useState([]);
    const [curSupplement, setCurSupplement] = useState();
    const serviceCode = getFieldValue('serviceCode');

    useEffect(() => {
        if (visible) {
            initData();
            return () => {
                resetFields();
                setSupplementList([]);
                setCurSupplement();
            };
        }
    }, [visible]);

    useEffect(() => {
        if (serviceCode && supplementList?.length) {
            const supplement = supplementList?.find((v) => v.name === serviceCode);
            supplement && setCurSupplement(supplement);
        }
    }, [serviceCode, supplementList]);

    const initData = async () => {
        const { data } = editor.schema.data.nodesMap[nodeId] || {};
        const supplementListRes = await service.supplementList({ orgCode, appCode });
        setSupplementList(supplementListRes?.data || []);

        resetFields();
        setFieldsValue(data || {});
    };
    // 提交
    const commitHandle = () => {
        validateFields((errors, data) => {
            if (!errors) {
                const name = curSupplement?.displayName;
                editor.schema.data.nodesMap[nodeId].data = {
                    ...data,
                    inputMapping: curSupplement?.inputMapping
                };
                editor.schema.data.nodesMap[nodeId].name = name;
                editor.graph.node.nodes[nodeId].shape.select('text.flow-txt-node').node.innerHTML = sliceName(name);
                DataConvert.rmNodeError(editor.graph.node.nodes[nodeId]);
                onCancel();
            }
        });
    };

    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.suspendflownode.index.quXiao}
        </Button>,
        <Button type="primary" onClick={commitHandle} key="ok">
            {I18N.suspendflownode.index.queDing}
        </Button>
    ];

    const footerCancelDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.suspendflownode.index.guanBi}
        </Button>
    ];

    const columns = [
        {
            title: I18N.suspendflownode.index.fuWuRuCan,
            dataIndex: 'serviceParam',
            ellipsis: true,
            width: 150,
            render: (txt) => {
                return <Ellipsis title={txt} />;
            }
        },
        {
            title: <Tooltip title={I18N.suspendflownode.index.fuZhiFangXiang}>{I18N.suspendflownode.index.fuZhiFangXiang}</Tooltip>,
            dataIndex: 'direction',
            ellipsis: true,
            width: 80,
            render: () => {
                return <Icon type="arrow-right" style={{ color: '#3484F4' }} />;
            }
        },
        {
            title: I18N.suspendflownode.index.yaoSuMing,
            dataIndex: 'displayName',
            ellipsis: true,
            width: 150,
            render: (txt, record) => {
                return <Ellipsis title={txt} prefix={record?.mustInput && <b style={{ color: '#f00', marginRight: '2px' }}>*</b>} />;
            }
        },
        {
            title: I18N.suspendflownode.index.yaoSuBiaoZhi,
            dataIndex: 'eleName',
            ellipsis: true,
            width: 150,
            render: (txt) => {
                return <Ellipsis title={txt} />;
            }
        }
    ];

    return (
        <Modal
            title={I18N.suspendflownode.index.jiXuBuChong}
            width={840}
            visible={visible}
            maskClosable={false}
            zIndex={1002}
            onCancel={onCancel}
            footer={disabled ? footerCancelDom : footerDom}
            destroyOnClose>
            <Form {...otp.suspendFlowNode.formItemLayout}>
                <Form.Item label={I18N.suspendflownode.index.xuanZeJueCeBu}>
                    {getFieldDecorator('serviceCode', {
                        rules: [
                            {
                                required: true,
                                message: I18N.suspendflownode.index.qingXuanZeJueCe
                            }
                        ]
                    })(
                        <TooltipSelect
                            placeholder={I18N.suspendflownode.index.qingXuanZeJueCe}
                            isVirtual
                            optionFilterProp="children"
                            showSearch
                            style={{ width: '380px' }}
                            disabled={disabled}>
                            {supplementList?.map((item, index) => {
                                return (
                                    <Option key={item.name} value={item.name}>
                                        {item.displayName}
                                    </Option>
                                );
                            })}
                        </TooltipSelect>
                    )}
                </Form.Item>
                <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }} className="mt10">
                    <Table columns={columns} dataSource={curSupplement?.inputMapping || []} rowKey="serviceParam" size="small" />
                </Form.Item>
            </Form>
        </Modal>
    );
});
