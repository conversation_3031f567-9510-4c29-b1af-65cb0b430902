/*
 * @CreatDate: 2021-04-20 15:32:33
 * @Describe: 函数库弹框
 */

import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Select, message, Modal, Button, Icon, Tooltip, Tag, Ellipsis } from 'tntd';
import './index.less';
import TooltipSelect from '@tntd/tooltip-select';
import { cloneDeep } from 'lodash';
import IndicatorsCascader from '@/components/IndicatorsCascader';
import { getUrl, getHeader } from '@/services/common';
import request from '@/utils/request';
import DialogTable from '@/components/DialogTable';
import DataConvert from '@/components/WorkFlowEditor/DefaultDataConvert';
import { useGetRuleField } from '@/routes/InterfaceManagement/hook';
import { sliceName } from '@/components/WorkFlowEditor';

const Option = Select.Option;

const ModalDialog = (props) => {
    const { disabled, editor, dialogShowInfo, onCancel, data = {} } = props;

    const { type, nodeId } = dialogShowInfo || {};

    const { orgCode, appCode } = data;
    const visible = type === 'FunctionServiceNode';

    const ruleField = useGetRuleField();
    const ruleFieldList = ruleField?.ruleFieldList;
    const [item, setItem] = useState({});
    const [inputParams, setInputParams] = useState([]);
    const [outputParams, setOutputParams] = useState([]);
    const [functionList, setFunctionList] = useState([]);
    useEffect(() => {
        if (visible) {
            const data = editor.schema.data.nodesMap[nodeId].data || {};
            const { inputParams = [], outputParams = [] } = data;
            setItem({
                code: data.code,
                name: data.name
            });
            request(getUrl('/bridgeApi/workflow/formula/select', { orgCode, appCode }), {}, true).then((res) => {
                var arr = cloneDeep(res?.data);
                //优先取现有的，没有取画布的，如果中文名没匹配到就整条不展示
                if (inputParams?.length > 0 && data.name) {
                    let curInputParams = [];
                    inputParams?.map((item) => {
                        let obj = res?.data.find((item1) => item1.code === data.code);
                        item.fieldDisplayName = obj?.input?.find((item2) => item2.field === item.field)?.fieldDisplayName;
                        if (item.fieldDisplayName) {
                            curInputParams.push(item);
                        }
                    });
                    setInputParams(curInputParams);
                } else {
                    if (data?.inputParams?.length > 0) {
                        let curInputParams = [];
                        data.inputParams?.map((item) => {
                            let obj = res?.data.find((item1) => item1.code === data.code);
                            item.fieldDisplayName = obj?.input?.find((item2) => item2.field === item.field)?.fieldDisplayName;
                            if (item.fieldDisplayName) {
                                curInputParams.push(item);
                            }
                        });
                        setInputParams(curInputParams);
                    }
                }
                if (outputParams?.length > 0 && data.name) {
                    let curOutputParams = [];
                    outputParams?.map((item) => {
                        let obj = res?.data.find((item1) => item1.code === data.code);
                        item.fieldDisplayName = obj?.output?.find((item2) => item2.field === item.field)?.fieldDisplayName;
                        if (item.fieldDisplayName) {
                            curOutputParams.push(item);
                        }
                    });
                    setOutputParams(curOutputParams);
                } else {
                    if (data?.outputParams?.length > 0) {
                        let curOutputParams = [];
                        data.outputParams?.map((item) => {
                            let obj = res?.data.find((item1) => item1.code === data.code);
                            item.fieldDisplayName = obj?.output?.find((item2) => item2.field === item.field)?.fieldDisplayName;
                            if (item.fieldDisplayName) {
                                curOutputParams.push(item);
                            }
                        });
                        setOutputParams(curOutputParams);
                    }
                }
                arr.map((item) => {
                    if (item?.input?.length > 0) {
                        item.input.map((item1) => {
                            item1.fieldName = item1.field;
                        });
                    }
                });
                arr.map((item) => {
                    if (item?.output?.length > 0) {
                        item.output.map((item1) => {
                            item1.fieldName = item1.field;
                        });
                    }
                });

                setFunctionList(arr);
                const now = arr.find((item) => item.code === data.code) || {};
                if (data?.inputParams?.length === 0) {
                    setInputParams(now.input);
                }
                // if (outputParams?.length > 0) {
                // 	setOutputParams(outputParams);
                // }
                setItem({
                    code: now.code,
                    name: now.name
                });
            });
        }
    }, [visible]);
    // useEffect(() => {
    //     if (ruleFieldList?.length > 0 && salaxyFieldList?.length > 0) {
    //         setList(ruleFieldList.concat(salaxyFieldList));
    //     } else if (ruleFieldList?.length > 0 && salaxyFieldList?.length === 0) {
    //         setList(ruleFieldList);
    //     }
    // }, [ruleFieldList, salaxyFieldList]);

    // 关闭
    const closeHandle = () => {
        onCancel();
    };

    // 提交
    const commitHandle = () => {
        if (!item.code) return message.warning(I18N.functionservicenode.index.qingXuanZeHanShu2);
        const { editor, workflowSetStore } = props;

        const name = item.name;
        editor.schema.data.nodesMap[nodeId].name = name;
        editor.schema.data.nodesMap[nodeId].data = {
            inputParams: cloneDeep(inputParams),
            outputParams: cloneDeep(outputParams),
            ...item
        };
        editor.schema.data.nodesMap[nodeId].name = name;
        editor.graph.node.nodes[nodeId].shape.select('text.flow-txt-node').node.innerHTML = sliceName(name);
        DataConvert.rmNodeError(editor.graph.node.nodes[nodeId]);
        onCancel();
    };

    const changeField = (e) => {
        const obj = functionList.find((k) => k.code === e);
        setItem(obj);
        setInputParams(cloneDeep(obj.input));
        setOutputParams(cloneDeep(obj.output));
    };

    const footerDom = [
        <Button onClick={closeHandle} key="cancel">
            {I18N.functionservicenode.index.quXiao}
        </Button>,
        <Button type="primary" onClick={commitHandle} key="ok">
            {I18N.functionservicenode.index.queDing}
        </Button>
    ];

    const columns = [
        {
            title: I18N.functionservicenode.index.shuRuZiDuan,
            dataIndex: 'fieldName',
            key: 'fieldName',
            width: '250px',
            render: (text, record) => {
                return (
                    <IndicatorsCascader
                        disabled={disabled}
                        style={{ width: '230px' }}
                        options={ruleFieldList || []}
                        fieldNames={{ label: 'dName', value: 'name', children: 'data' }}
                        value={text}
                        placeholder={I18N.functionservicenode.index.qingXuanZeShuChu2}
                        onChange={(value) => {
                            let arr = inputParams;
                            arr.find((item) => item.fieldDisplayName === record.fieldDisplayName).fieldName = value;
                            setInputParams([...arr]);
                        }}
                        showSearch
                    />
                );
                // let fieldsOption = [];
                // if (list.length > 0) {
                //     list.map((item, index) => {
                //         //指标有应用概念，不展示没有被当前策略应用所授权的指标
                //         if (item?.apps && !item?.apps.includes(appCode)) {
                //             return;
                //         }
                //         if (orgCode && item.orgs && Array.isArray(item.orgs) && !item.orgs.includes(orgCode)) {
                //             return;
                //         }
                //         fieldsOption.push(
                //             <VirtualSelect.Option value={item.name} key={index} disabled={inputMap[item.name]}>
                //                 <TdTag data={item} showSourceName={false} />
                //                 {item.name?.split('_')[0] === 'salaxyzb' && '[实时]'}
                //                 {item.name?.split('_')[0] === 'offlinezb' && '[离线]'}
                //                 {item.dName}【{item.name}】
                //             </VirtualSelect.Option>
                //         );
                //     });
                // }
                // return (
                //     <TooltipSelect
                //         style={{ width: '230px' }}
                //         isVirtual={true}
                //         showSearch
                //         placeholder="请选择输出字段/指标"
                //         dropdownMatchSelectWidth={false}
                //         maxWidth={600}
                //         filterOption={(input, option) => option?.props?.children?.join()?.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                //         // style={{ width: '330px' }}
                //         disabled={disabled}
                //         value={text}
                //         onChange={(value) => {
                //             let arr = inputParams;
                //             arr.find((item) => item.fieldDisplayName === record.fieldDisplayName).fieldName = value;
                //             setInputParams([...arr]);
                //         }}>
                //         {fieldsOption}
                //     </TooltipSelect>
                // );
            }
        },
        {
            title: I18N.functionservicenode.index.fuZhiFangXiang,
            dataIndex: 'direction',
            key: 'direction',
            align: 'center',
            render: () => {
                return <Icon type="arrow-right" style={{ color: '#3484F4' }} />;
            }
        },
        {
            title: I18N.functionservicenode.index.hanShuRuCanMing,
            dataIndex: 'fieldDisplayName',
            render: (text) => {
                return <Ellipsis title={text} widthLimit={150} />;
            }
        },
        {
            title: I18N.functionservicenode.index.hanShuRuCanBiao,
            dataIndex: 'field',
            render: (text) => {
                return <Ellipsis title={text} widthLimit={150} />;
            }
        }
    ];

    const columns2 = [
        {
            title: I18N.functionservicenode.index.hanShuChuCan,
            dataIndex: 'fieldDisplayName',
            width: '150px',
            render: (text) => {
                return <Ellipsis title={text} widthLimit={150} />;
            }
        },
        {
            title: I18N.functionservicenode.index.fuZhiFangXiang,
            dataIndex: 'direction',
            key: 'direction',
            align: 'center',
            render: () => {
                return <Icon type="arrow-right" style={{ color: '#3484F4' }} />;
            }
        },
        {
            title: I18N.functionservicenode.index.shuChuZiDuan,
            dataIndex: 'fieldName',
            width: '300px',
            render: (text, record) => {
                // let fieldsOption = [];

                // if (ruleFieldList.length > 0) {
                //     ruleFieldList.map((item, index) => {
                //         fieldsOption.push(
                //             <VirtualSelect.Option value={item.name} key={index} disabled={outputMap[item.name]}>
                //                 <TdTag data={item} showSourceName={false} />
                //                 {item.dName}【{item.name}】
                //             </VirtualSelect.Option>
                //         );
                //     });
                // }

                return (
                    <IndicatorsCascader
                        disabled={disabled}
                        style={{ width: '280px' }}
                        options={ruleFieldList || []}
                        fieldNames={{ label: 'dName', value: 'name', children: 'data' }}
                        value={text}
                        placeholder={I18N.functionservicenode.index.qingXuanZeShuChu}
                        onChange={(value) => {
                            let arr = outputParams;
                            arr.find((item) => item.fieldDisplayName === record.fieldDisplayName).fieldName = value;
                            setOutputParams([...arr]);
                        }}
                        showSearch
                    />
                    // <TooltipSelect
                    //     style={{ width: '280px' }}
                    //     isVirtual={true}
                    //     showSearch
                    //     placeholder="请选择输出字段"
                    //     dropdownMatchSelectWidth={false}
                    //     maxWidth={600}
                    //     filterOption={(input, option) => option?.props?.children?.join()?.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    //     // style={{ width: '330px' }}
                    //     disabled={disabled}
                    //     value={text}
                    //     onChange={(value) => {
                    //         let arr = outputParams;
                    //         arr.find((item) => item.fieldDisplayName === record.fieldDisplayName).fieldName = value;
                    //         setOutputParams([...arr]);
                    //     }}>
                    //     {fieldsOption}
                    // </TooltipSelect>
                );
            }
        }
    ];

    const inputMap = {};
    inputParams?.forEach((item) => {
        inputMap[item.fieldName] = true;
    });
    const outputMap = {};
    outputParams?.forEach((item) => {
        outputMap[item.fieldName] = true;
    });

    return (
        <Modal
            title={I18N.functionservicenode.index.hanShuPeiZhi}
            width={740}
            visible={visible}
            maskClosable={false}
            zIndex={1002}
            onCancel={closeHandle}
            footer={disabled ? null : footerDom}>
            <div className="lb-modal-box">
                <span className="label" style={{ marginRight: '10px' }}>
                    <b style={{ color: 'red' }}>*</b>
                    {I18N.functionservicenode.index.xuanZeHanShu}
                </span>
                <TooltipSelect
                    showSearch
                    style={{ width: 300 }}
                    placeholder={I18N.functionservicenode.index.qingXuanZeHanShu}
                    dropdownMatchSelectWidth={false}
                    dropdownStyle={{ width: 350 }}
                    value={item.code}
                    onChange={changeField}
                    disabled={disabled}
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                        (Array.isArray(option.props.children) ? option.props.children.join('') : option.props.children)
                            .toLowerCase()
                            .indexOf(input.toLowerCase()) >= 0
                    }>
                    {functionList.map((item, index) => (
                        <Option key={index} value={item.code}>
                            {item?.allVersions.find((item1) => item1?.version === item.curOnlineVersion).status === 3 && (
                                <Tooltip title={I18N.functionservicenode.index.daoRuDaiTiJiao} placement="top">
                                    <img
                                        src={require('@/components/TdTag/imgs/import_export.svg')}
                                        className="tag-img"
                                        style={{ marginRight: '3px' }}
                                    />
                                </Tooltip>
                            )}
                            {item.name}
                        </Option>
                    ))}
                </TooltipSelect>
                {item?.description && (
                    <Tooltip title={I18N.functionservicenode.index.hanShuMiaoShu + item?.description}>
                        <Icon className="ml10" type="info-circle" />
                    </Tooltip>
                )}
            </div>
            {item.code && (
                <>
                    <DialogTable columns={columns} dataSource={inputParams} rowKey="field" loading={false} width={600} />
                    <DialogTable columns={columns2} dataSource={outputParams} rowKey="field" loading={false} width={600} />
                </>
            )}
        </Modal>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(ModalDialog);
