// API接口服务
import I18N from '@/utils/I18N';
import { useEffect, useState, useRef } from 'react';
import { Modal, Button, Select, Spin, Form, Checkbox, message } from 'tntd';
import TooltipSelect from '@tntd/tooltip-select';
import DataConvert from '@/components/WorkFlowEditor/DefaultDataConvert';
import { sliceName } from '@/components/WorkFlowEditor';
import InMapTable from './InMapTable';
import OutMapTable from './OutMapTable';
import ExternalIndicator from './ExternalIndicator';
import { cloneDeep } from 'lodash';
import service from '../../service';
import './index.less';
import otp from './otp';

const { Option } = Select;

export default Form.create({ name: 'feature-service-node' })((props) => {
    const { form, onCancel, dialogShowInfo, editor, disabled, ruleField, data } = props;
    const { appCode, orgCode } = data;
    const { getFieldDecorator, validateFields, setFieldsValue, resetFields, getFieldValue } = form;
    const thirdServiceCode = getFieldValue('thirdServiceCode');
    const { type, nodeId } = dialogShowInfo || {};

    const visible = type === 'FeatureServiceNode';

    const inTableRef = useRef();
    const outTableRef = useRef();
    const [apiList, setApiList] = useState([]);
    const [curApiLoad, setCurApiLoad] = useState(false);
    const [curApiService, setCurApiService] = useState();
    //外数指标加工和调用中断异常中断流程选项
    const [curOptions, setCurOptions] = useState([]);
    //外数指标列表
    const [indexList, setIndexList] = useState([]);
    const [outIndicator, setOutIndicator] = useState([]);

    const { inputMapping = [], outputMapping = [] } = curApiService || {};

    useEffect(() => {
        if (visible) {
            initData();
            return () => {
                resetFields();
                setApiList([]);
                setCurApiLoad(false);
                setCurApiService();
            };
        }
    }, [type]);

    const initData = async () => {
        const { data } = editor.schema.data.nodesMap[nodeId] || {};
        const { thirdServiceCode, incomingFields, outgoingFields, featureCodes, exceptionTerminate } = data || {};
        const serviceListRes = await service.apiServiceList({ appCode, orgCode });
        setApiList(serviceListRes?.data || []);

        resetFields();
        let newOptions = [];
        if (featureCodes && featureCodes?.length !== 0) {
            newOptions.push('externalIndicator');
        }
        if (exceptionTerminate) {
            newOptions.push('invokeException');
        }
        setCurOptions(newOptions);
        setFieldsValue(
            {
                thirdServiceCode,
                incomingFields,
                outgoingFields,
                featureCodes
            } || {}
        );
        setOutIndicator(featureCodes || []);
    };

    // 监听三方服务code
    useEffect(() => {
        if (thirdServiceCode) {
            const documentTypeUuid = apiList?.filter((v) => v.channelServiceName === thirdServiceCode)[0].documentTypeUuid;
            setCurApiLoad(true);
            service
                .apiMapping({ channelServiceName: thirdServiceCode })
                .then((res) => {
                    setCurApiService(res?.data || {});
                })
                .finally(() => {
                    setCurApiLoad(false);
                });
            service.getIndexList({ documentTypeUuid, dataSourceServiceCode: thirdServiceCode }).then((res) => {
                if (res?.data.length === 0) {
                    message.info(I18N.featureservicenode.index.dangQianSanFangFu);
                }
                setIndexList(res?.data);
            });
        } else {
            setCurApiService();
        }
    }, [thirdServiceCode]);

    // 监听当前服务对象出入参设置
    useEffect(() => {
        if (curApiService) {
            let [newInputMapping, newOutputMapping] = [inputMapping?.slice(), outputMapping?.slice()];
            const { data } = editor.schema.data.nodesMap[nodeId] || {};

            if (data?.thirdServiceCode === curApiService?.channelServiceName) {
                // 对入参赋值
                const incomingFields = data.incomingFields || [];
                if (incomingFields?.length) {
                    const inputObj = {};
                    incomingFields?.forEach((v) => {
                        if (v?.field) {
                            inputObj[v?.serviceParam] = v;
                        }
                    });
                    newInputMapping?.map((v) => {
                        if (inputObj[v?.serviceParam]?.field) {
                            v.field = inputObj[v?.serviceParam]?.field;
                        }
                    });
                }
                // 对出参赋值
                const outgoingFields = data.outgoingFields || [];
                if (outgoingFields?.length) {
                    const outObj = {};
                    outgoingFields?.forEach((v) => {
                        if (v?.field) {
                            outObj[v?.serviceParam] = v;
                        }
                    });
                    newOutputMapping?.map((v) => {
                        if (outObj[v?.serviceParam]?.field) {
                            v.field = outObj[v?.serviceParam]?.field;
                        }
                    });
                }
            }

            setFieldsValue({
                incomingFields: newInputMapping || [],
                outgoingFields: newOutputMapping || []
            });
        }
    }, [curApiService]);

    const changeOption = (values) => {
        setCurOptions(values);
    };
    const commitModal = () => {
        validateFields((errors, data) => {
            if (!errors) {
                const name = curApiService?.channelServiceDisplayName;
                editor.schema.data.nodesMap[nodeId].data = {
                    ...data,
                    thirdServiceName: name,
                    originInput: inputMapping?.filter((item) => item.field && item.serviceParam && item.type !== 'constant'),
                    originOutput: outputMapping,
                    exceptionTerminate: curOptions.includes('invokeException'),
                    async: apiList?.find((v) => v.channelServiceName === thirdServiceCode)?.async
                };
                if (!curOptions.includes('externalIndicator')) {
                    delete editor.schema.data.nodesMap[nodeId].data.featureCodes;
                }
                editor.schema.data.nodesMap[nodeId].name = name;
                editor.graph.node.nodes[nodeId].shape.select('text.flow-txt-node').node.innerHTML = sliceName(name);
                DataConvert.rmNodeError(editor.graph.node.nodes[nodeId]);
                onCancel();
            }
        });
    };

    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.featureservicenode.index.quXiao}
        </Button>,
        <Button type="primary" onClick={commitModal} key="ok">
            {I18N.featureservicenode.index.queDing}
        </Button>
    ];

    const footerCancelDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.featureservicenode.index.guanBi}
        </Button>
    ];

    return (
        <Modal
            title={I18N.featureservicenode.index.aPIJieKou2}
            visible={visible}
            maskClosable={false}
            zIndex={1002}
            width={1000}
            onCancel={onCancel}
            footer={disabled ? footerCancelDom : footerDom}>
            <Form {...otp.formItemLayout} className="feature-form">
                <div className="feature-form-baseinfo">
                    <div className="feature-form-baseinfo-form">
                        <Form.Item label={I18N.featureservicenode.index.aPIJieKou}>
                            {getFieldDecorator('thirdServiceCode', {
                                rules: [
                                    {
                                        required: true,
                                        message: I18N.featureservicenode.index.qingXuanZeAP
                                    }
                                ]
                            })(
                                <TooltipSelect
                                    placeholder={I18N.featureservicenode.index.qingXuanZeAP}
                                    isVirtual
                                    optionFilterProp="children"
                                    showSearch
                                    style={{ width: '300px' }}
                                    disabled={disabled}>
                                    {apiList?.map((item) => {
                                        return (
                                            <Option key={item.channelServiceName} value={item.channelServiceName}>
                                                {item.channelServiceDisplayName}
                                            </Option>
                                        );
                                    })}
                                </TooltipSelect>
                            )}
                        </Form.Item>
                    </div>
                    <Checkbox.Group onChange={changeOption} className="feature-form-baseinfo-checkbox" value={curOptions}>
                        <Checkbox value="externalIndicator">{I18N.featureservicenode.index.waiShuZhiBiaoJia}</Checkbox>
                        <Checkbox value="invokeException">{I18N.featureservicenode.index.diaoYongYiChangZhong}</Checkbox>
                    </Checkbox.Group>
                </div>
                <Spin className="globalSpin" spinning={curApiLoad}>
                    <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }} className="table-form-tiem">
                        {getFieldDecorator('incomingFields', {
                            rules: [
                                {
                                    validator: (rule, value, callback) => {
                                        const msg = inTableRef.current.checkMapHandle(value);
                                        if (msg) {
                                            callback(msg);
                                        }
                                        callback();
                                    }
                                }
                            ]
                        })(
                            <InMapTable
                                visible={visible}
                                disabled={disabled}
                                initInput={curApiService?.inputMapping || []}
                                ruleField={ruleField}
                                ref={inTableRef}
                            />
                        )}
                    </Form.Item>
                    <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }} className="mt10 table-form-tiem">
                        {getFieldDecorator('outgoingFields', {
                            rules: [
                                {
                                    validator: (rule, value, callback) => {
                                        const msg = outTableRef.current.checkMapHandle(value);
                                        if (msg) {
                                            callback(msg);
                                        }
                                        callback();
                                    }
                                }
                            ]
                        })(
                            <OutMapTable
                                visible={visible}
                                disabled={disabled}
                                initInput={curApiService?.outputMapping || []}
                                ruleField={ruleField}
                                ref={outTableRef}
                            />
                        )}
                    </Form.Item>
                    {curOptions.includes('externalIndicator') && indexList?.length !== 0 && (
                        <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }} className="mt10 table-form-tiem">
                            {getFieldDecorator('featureCodes', {
                                validateTrigger: 'onOk',
                                rules: [
                                    { required: true }
                                    // {
                                    //     validator: (rule, value, callback) => {
                                    //         const msg = outTableRef.current.checkMapHandle(value);
                                    //         if (msg) {
                                    //             callback(msg);
                                    //         }
                                    //         callback();
                                    //     }
                                    // }
                                ]
                            })(<ExternalIndicator indexList={indexList} outIndicator={outIndicator} />)}
                        </Form.Item>
                    )}
                </Spin>
            </Form>
        </Modal>
    );
});
