.feature-form{
    .mt10{
        margin-top: 10px;
    }
    .ant-form-item-label{
        text-align: left;
    }
    .feature-service-table{
        margin-top: 6px;
        .ant-table-small > .ant-table-content > .ant-table-body{
            margin: 0;
        }


    }
    .feature-form-baseinfo {
        display:flex;
        // justify-content: space-around;
        .feature-form-baseinfo-form {
            width:100%;
        }
        .feature-form-baseinfo-checkbox {
            width:100%;
            display:flex;
            justify-content: space-around;
            
        }
        .ant-col {
            width:auto;
        }
    }

    .table-form-tiem{
        .has-error{
            .feature-service-table{
                .tntd-select:not(.tntd-select-customize-input).hasError .tntd-select-selector{
                    border:1px solid #F06555;
                }
                .tntd-select:not(.hasError) .tntd-select-selector{
                    border: 1px solid #C9D2DD;
                }

            }

            .ant-form-explain{
                position: relative;
                top:-20px;
            }
        }
    }
    .box-add {
        // margin: 0px 15px 8px 15px;
        padding: 10px;
        text-align: center;
        background: #ffffff;
        border: 2px dashed #d8dee5;
        border-radius: 2px;
        display: flex;
        justify-content: center;
        align-items: center;

        .box-div {
            cursor: pointer;
        }

        .box-div:hover {
            color: #126bfb;
        }
    }

}
