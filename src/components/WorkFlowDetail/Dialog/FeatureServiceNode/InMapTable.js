import I18N from '@/utils/I18N';
import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Tag, Table, Row, Input, Tooltip, Select, Ellipsis, Icon } from 'tntd';
import IndicatorsCascader from '@/components/IndicatorsCascader';
import TooltipSelect from '@tntd/tooltip-select';
import { useGetGlobalStore } from '@/utils/utils';
import { DATA_TYPE_MAP } from '@/constants';

const Option = Select.Option;

export default forwardRef((props, ref) => {
    const { visible, initInput, value = [], onChange, ruleField, disabled } = props;
    const { ruleFieldList = [] } = ruleField || {};
    const [inputMap, setInputMap] = useState({});
    const [inPageInfo, setInPageInfo] = useState({ page: 1, pageSize: 10 });

    let { allFieldList = [] } = useGetGlobalStore();

    const checkMapHandle = (curV) => {
        if (curV?.length) {
            let emptyMsg = '';
            curV.forEach((v, i) => {
                if (!emptyMsg && !v.field && v.mustInput && v.type !== 'constant') {
                    const curPage = Math.ceil((i + 1) / inPageInfo?.pageSize);
                    emptyMsg = I18N.template(I18N.featureservicenode.inmaptable.ruCanZiDuanDi, {
                        val1: curPage,
                        val2: i + 1 - (curPage - 1) * inPageInfo?.pageSize
                    });
                }
            });
            return emptyMsg;
        }
        return '';
    };

    useImperativeHandle(ref, () => ({
        checkMapHandle
    }));

    const getMapItem = (record) => {
        return initInput?.find((item) => item.serviceParam === record.serviceParam) || {};
    };

    const handleChange = (v, i) => {
        const newValue = value?.slice();
        i = ((inPageInfo?.page || 1) - 1) * (inPageInfo.pageSize || 10) + i;
        newValue[i] = {
            ...newValue[i],
            field: v
        };
        onChange && onChange(newValue);
    };

    useEffect(() => {
        if (visible) {
            if (value?.length) {
                const inputMapTemp = {};
                value?.forEach((v) => {
                    inputMapTemp[v.field] = v;
                });
                setInputMap(inputMapTemp);
            }
        }
    }, [value, visible]);

    return (
        <div className="feature-service-table">
            <Table
                title={() => (
                    <Row>
                        {I18N.featureservicenode.inmaptable.ruCanZiDuan}
                        <Tag color="blue" style={{ marginLeft: '10px' }}>
                            {I18N.featureservicenode.inmaptable.shuRu}
                        </Tag>
                    </Row>
                )}
                size="small"
                dataSource={value}
                pagination={{
                    onChange: (page, pageSize) => {
                        setInPageInfo({ page, pageSize });
                    }
                }}
                columns={[
                    {
                        title: I18N.featureservicenode.inmaptable.aPIJieKou2,
                        dataIndex: 'displayName',
                        width: 300,
                        ellipsis: true,
                        render: (text, record) => {
                            let mustInput = record.mustInput;
                            if (!mustInput) {
                                mustInput = getMapItem(record)?.mustInput;
                            }

                            let str;
                            if (text) {
                                str = text;
                            } else if (initInput?.length && record?.serviceParam) {
                                str = getMapItem(record)?.displayName;
                            }
                            return (
                                <Ellipsis
                                    prefix={
                                        mustInput && (
                                            <b
                                                style={{
                                                    color: '#f00',
                                                    marginRight: '3px'
                                                }}>
                                                *
                                            </b>
                                        )
                                    }
                                    title={str}
                                />
                            );
                        }
                    },
                    {
                        title: I18N.featureservicenode.inmaptable.aPIJieKou,
                        dataIndex: 'serviceParam',
                        key: 'serviceParam',
                        width: 240,
                        ellipsis: true,
                        render: (text) => {
                            return <Ellipsis title={text} />;
                        }
                    },
                    {
                        title: I18N.featureservicenode.inmaptable.canShuLeiXing,
                        dataIndex: 'type',
                        key: 'type',
                        render: (text, record) => {
                            const initItem = getMapItem(record);
                            return initItem?.type === 'constant'
                                ? I18N.featureservicenode.inmaptable.dingZhi
                                : I18N.featureservicenode.inmaptable.bianLiang;
                        }
                    },
                    {
                        title: I18N.featureservicenode.inmaptable.ruCanZiDuanMing,
                        dataIndex: 'field',
                        key: 'field',
                        width: 250,
                        render: (text, record, i) => {
                            const initItem = getMapItem(record);
                            return initItem?.type === 'constant' ? (
                                <Tooltip title={initItem.value}>
                                    <Input
                                        value={initItem.value}
                                        style={{ background: '#eaedf3', cursor: 'notAllowed', color: '#b9bec7' }}
                                    />
                                </Tooltip>
                            ) : (
                                <IndicatorsCascader
                                    parentValue={text}
                                    options={ruleFieldList}
                                    fieldNames={{ label: 'dName', value: 'name', children: 'data' }}
                                    placeholder={I18N.onecondition.index.qingXuanZe}
                                    showSearch
                                    disabled={disabled}
                                    value={text}
                                    style={{ width: '100%', maxWidth: 233 }}
                                    onChange={(e) => {
                                                handleChange(e, i);
                                    }}
                                />
                                // <TooltipSelect
                                //     isVirtual
                                //     className={!text ? 'hasError' : ''}
                                //     style={{ width: '234px' }}
                                //     dropdownMatchSelectWidth={false}
                                //     dropdownStyle={{ width: 350 }}
                                //     showSearch
                                //     disabled={disabled}
                                //     filterOption={(input, option) => option.props.dName.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                                //     value={text}
                                //     placeholder={I18N.featureservicenode.inmaptable.qingXuanZeRuCan}
                                //     onChange={(e) => {
                                //         handleChange(e, i);
                                //     }}>
                                //     {(allFieldList || []).map((i) => {
                                //         const disabled = inputMap?.[i.name] || i.disabled;
                                //         // return { ...i, disabled };
                                //         let obj = DATA_TYPE_MAP[i.dataType];
                                //         return (
                                //             <Option key={i.name} value={i.name} disabled={disabled} dName={i.displayName}>
                                //                 <Icon type={obj?.icon} style={{ color: obj?.color }} />
                                //                 {i.displayName}
                                //             </Option>
                                //         );
                                //     })}
                                // </TooltipSelect>
                            );
                        }
                    }
                ]}
                rowKey="serviceParam"
            />
        </div>
    );
});
