import request from '@/utils/request';
import { getUrl, getHeader } from '@/services/common';

// 获取画布节点配置
const getFlowConfig = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowConfig/node/config', param),
        {
            method: 'GET'
        },
        true
    );
};

// 获取api接口下拉列表
const apiServiceList = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowConfig/dataSourceService/select', param),
        {
            method: 'GET'
        },
        true
    );
};

// 获取api接口出入参配置
const apiMapping = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowConfig/dataSourceService/mapping/select', param),
        {
            method: 'GET'
        },
        true
    );
};

// 获取继续补充服务列表和入参配置
const supplementList = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowConfig/supplement/select', param),
        {
            method: 'GET'
        },
        true
    );
};

// 流程模板下拉接口
const workflowTemplateList = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowTemplate/list', param),
        {
            method: 'GET'
        },
        true
    );
};

// 查看流程模板
const workflowTemplateDetail = async (param) => {
    return request(
        getUrl('/bridgeApi/workflowTemplate/detail', param),
        {
            method: 'GET'
        },
        true
    );
};

// 流程模板预编译
const preBuild = async (params) => {
    return request(
        '/bridgeApi/workflowTemplate/preBuild',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 获取数据源列表
const getAllDataSource = async (params) => {
    return request(
        getUrl('/dmApi/paas/dm/table/getAllDataSource', params),
        {
            method: 'GET'
        },
        true
    );
};

// 获取数据表列表
const getAllRealTables = async (params) => {
    return request(
        getUrl('/dmApi/paas/dm/table/queryTables', params),
        {
            method: 'GET'
        },
        true
    );
};

// 获取数据表解析字段
const getRealTableSchema = async (params) => {
    return request(
        '/dmApi/paas/dm/table/getRealTableSchema',
        {
            method: 'POST',
            body: params,
            headers: {
                ...getHeader(),
                'Content-Type': 'application/json'
            }
        },
        true
    );
};

// getSystemFieldList: {
//     url: '/field/list'
// },

const getSystemFieldList = async (params) => {
    return request(
        getUrl('/dmApi/field/list', params),
        {
            method: 'GET'
        },
        true
    );
};

// 获取条件测试
const getDataTableTest = async (params) => {
    return request(
        '/bridgeApi/dataTable/test',
        {
            method: 'POST',
            body: params,
            headers: {
                ...getHeader(),
                'Content-Type': 'application/json'
            }
        },
        true
    );
};

const getIndexList = async (params) => {
    return request(
        getUrl('/bridgeApi/layout/common/index/captain', params),
        {
            method: 'GET'
        },
        true
    );
};
export default {
    getFlowConfig,
    apiServiceList,
    apiMapping,
    supplementList,
    workflowTemplateList,
    workflowTemplateDetail,
    preBuild,
    getAllRealTables,
    getAllDataSource,
    getRealTableSchema,
    getSystemFieldList,
    getDataTableTest,
    getIndexList
};
