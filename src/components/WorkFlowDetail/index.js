import { useRef, useImperativeHandle, forwardRef } from 'react';
import { Empty } from 'tntd';
import WorkFlowEditor from '@/components/WorkFlowEditor';
import DefaultConvert from '@/components/WorkFlowEditor/DefaultDataConvert';
import ParallelGateway from './Dialog/ParallelGateway';
import ExclusiveGateway from './Dialog/ExclusiveGateway';
import FeatureServiceNode from './Dialog/FeatureServiceNode';
import ExclusiveLine from './Dialog/LinkSetting/ExclusiveLine';
import SuspendFlowNode from './Dialog/SuspendFlowNode';
import ChildFlowNode from './Dialog/ChildFlowNode';
import RouteServiceNode from './Dialog/RouteServiceNode';
import EmailFlowNode from './Dialog/EmailFlowNode';
import RouteServiceNodeLine from './Dialog/LinkSetting/RouteServiceNodeLine';
import DataTableNode from './Dialog/DataTableNode';

import { useGetRuleField, useGetFlowDict } from './hooks/workflow';
import './index.less';
import FunctionServiceNode from './Dialog/FunctionServiceNode';

export default forwardRef((props, ref) => {
    const mmFlow = useRef();
    const { onOk, graphData = {}, type, flowType, className, style = {}, operateGroup = [] } = props;

    const ruleField = useGetRuleField();
    const { flowNodesDict, flowNodesDictLoad } = useGetFlowDict();

    const getGraphData = () => {
        const { schema } = mmFlow?.current?.editor || {};
        const data = schema?.getData?.();
        const graphDataFinally = DefaultConvert.format(data, mmFlow?.current?.editor);
        onOk && onOk({ graphData: graphDataFinally });
        return graphDataFinally;
    };

    useImperativeHandle(ref, () => ({
        getGraphData,
        editor: mmFlow?.current?.editor
    }));

    return (
        <>
            {!flowNodesDictLoad ? (
                <WorkFlowEditor
                    type={type}
                    ref={mmFlow}
                    className={`workflow-editor ${className || ''}`}
                    editorStyle={style}
                    graphData={graphData}
                    flowNodesDict={flowNodesDict?.[flowType || 'default']}
                    dialogDom={[
                        <ExclusiveLine {...props} key="exclusive-line" ruleField={ruleField} />,
                        <RouteServiceNodeLine {...props} key="route-service-line" />,
                        <FeatureServiceNode
                            {...props}
                            key="feature-service"
                            ruleField={{
                                ...ruleField,
                                ruleFieldList: ruleField?.ruleFieldList?.filter((item) => item?.sourceKey !== 'FIELD_SCRIPT')
                            }}
                        />,
                        <ExclusiveGateway {...props} key="exclusive" />,
                        <SuspendFlowNode {...props} key="suspend" />,
                        <ChildFlowNode {...props} key="child" />,
                        <ParallelGateway {...props} key="parallel" />,
                        <RouteServiceNode {...props} key="route-service" />,
                        <EmailFlowNode {...props} key="email-node" />,
                        <FunctionServiceNode {...props} key="function-node" />,
                        <DataTableNode {...props} key="dataTable-node" />
                    ]}
                    operateGroup={operateGroup}
                />
            ) : (
                <Empty />
            )}
        </>
    );
});
