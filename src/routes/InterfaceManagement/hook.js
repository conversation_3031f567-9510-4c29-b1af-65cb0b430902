import { useState, useEffect } from 'react';
import baseApi from '@/services/base';

/**
 * 获取系统字段
 * @returns
 */
export const useGetRuleField = () => {
    const [field, setField] = useState([]);
    useEffect(() => {
        baseApi.getVariableList().then((res) => {
            if (res.success) {
                // 将二维数组改为一维数组
                const { ruleFieldMap, ruleFieldList } =
                    res?.data?.reduce(
                        (total, item) => {
                            const { data, name: sourceKey, dName: sourceName, bizType } = item || {};
                            if (Array.isArray(data)) {
                                data.forEach((i) => {
                                    const { name, ...rest } = i;
                                    if (bizType === 'field') {
                                        total['ruleFieldMap'][name] = { ...rest, name, sourceName, sourceKey, bizType };
                                        total['ruleFieldList'].push({
                                            ...rest,
                                            name, 
                                            sourceName,
                                            sourceKey,
                                            bizType
                                        });
                                    }
                                    if (bizType === 'index') {
                                        total['ruleFieldMap'][name] = { ...rest, name, sourceName, sourceKey, bizType };
                                        total['ruleFieldList'].push({
                                            ...rest,
                                            name,
                                            sourceName,
                                            sourceKey,
                                            bizType
                                        });
                                    }
                                });
                            } else {
                                if (bizType === 'field') {
                                    total['ruleFieldList'].push({
                                        sourceKey,
                                        sourceName,
                                        bizType
                                    });
                                }
                            }
                            return total;
                        },
                        { ruleFieldMap: {}, ruleFieldList: [] }
                    ) || {};
                setField({
                    ruleFieldMap,
                    ruleFieldList
                });
            }
        });
    }, []);

    return field;
};
