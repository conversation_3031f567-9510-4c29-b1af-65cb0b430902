/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-04-22 17:59:52
 * @Describe: 测试弹框组件
 */

import I18N from '@/utils/I18N';
import React, { PureComponent } from 'react';
import { Button, Input, Drawer, InputNumber, Select, DatePicker, Tooltip, Alert, TreeSelect, Ellipsis, TntdVirtualTreeSelect } from 'tntd';
import moment from 'moment';
// import history from '@/utils/history';
import { cloneDeep } from 'lodash';
import './index.less';

const Option = Select.Option;

class TestServiceModal extends PureComponent {
    state = {
        data: null
    };

    componentDidMount() {
        const { data } = this.props;
        this.setState({ data });
    }

    componentDidUpdate(preProps) {
        // const { data = [] } = this.props;
        // const copyData = cloneDeep(data);
        // const preVisible = preProps.visible;
        // const nextVisible = this.props.visible;
        // if (preVisible !== nextVisible && nextVisible) {
        // 	copyData.forEach(item => {
        // 		if (item.type === "variable") {
        // 			item.eleValue = item.eleValue ?? item.serviceParam;
        // 		}
        // 	});
        // 	console.log(copyData);
        // 	this.setState({ data: copyData });
        // 	// this.setState({
        // 	// 	data: this.props.data
        // 	// });
        // }
        const preVisible = preProps.visible;
        const nextVisible = this.props.visible;
        if (preVisible !== nextVisible && nextVisible) {
            this.setState({
                data: this.props.data
            });
        }
    }

    changeHandle = (e, index, type) => {
        const { data } = this.state;
        const copyData = cloneDeep(data);

        let value = null;
        if (type === 'input') {
            value = e.target.value;
        } else if (type === 'number') {
            value = e;
        } else if (type === 'select') {
            value = e;
        } else if (type === 'date' && e) {
            value = e.format('YYYY-MM-DD HH:mm:ss');
        }
        copyData[index].eleValue = value;
        this.setState({ data: copyData });
    };

    resetParams() {
        const { data } = this.state;
        const copyData = cloneDeep(data);
        copyData.forEach((item) => {
            if (item.type === 'variable') {
                item.eleValue = null;
            }
        });
        this.setState({ data: copyData });
    }

    render() {
        const { data } = this.state;
        const { visible, title, loading, errorMsg, name, testDrawerChildData, orgAll = [], mockTip, appList } = this.props;
        return (
            <Drawer
                className="m-service-testdrawer"
                title={<Ellipsis title={I18N.template(I18N.testservicemodal.index.tITLE, { val1: title })} widthLimit={450} />}
                width={550}
                closable={true}
                visible={visible}
                onClose={() => this.props.onClose()}>
                <div className="title">{I18N.testservicemodal.index.shuRuZiDuan}</div>
                <div className="warp">
                    {errorMsg && <Alert style={{ marginTop: '20px' }} message="Error" description={errorMsg} type="error" showIcon />}
                    {mockTip && (
                        <Alert
                            message={
                                <span>
                                    {I18N.testservicemodal.index.benCiCeShiYi}
                                    {mockTip?.map((v, index) => {
                                        return (
                                            <>
                                                {index === 0 ? '' : '、'}
                                                <span
                                                    className="error-item"
                                                    onClick={() => {
                                                        this.props.history.push(
                                                            `/handle/supplierManagement/dataServiceList?displayName=${v}`
                                                        );
                                                    }}>
                                                    {v}
                                                </span>
                                            </>
                                        );
                                    })}
                                </span>
                            }
                            type="warning"
                            showIcon
                        />
                    )}
                    {data &&
                        data.map((item, index) => {
                            const { field } = item || {};
                            let isOrg = false;
                            let isApp = false;
                            if (field === 'S_S_ORGCODE') {
                                isOrg = true;
                            }
                            if (field === 'S_S_APPCODE') {
                                isApp = true;
                            }
                            return (
                                <div className="box" key={index}>
                                    <span className="label">
                                        {item.mustInput ? <b>*</b> : ''}
                                        <Tooltip title={item.displayName} placement="left">
                                            {item.displayName}
                                        </Tooltip>
                                    </span>
                                    {/* 数据类型(1字符型/2整型/3小数型/4日期型/5枚举/6布尔) */}
                                    <span className="content">
                                        {!isOrg && !isApp && (
                                            <>
                                                {(item.dataType === 1 || item.dataType === 6) && !item.extend && (
                                                    <Input
                                                        placeholder={I18N.testservicemodal.index.qingShuRu}
                                                        disabled={item.type === 'constant'}
                                                        value={item.type === 'constant' ? item.serviceParam : item.eleValue}
                                                        onChange={(e) => this.changeHandle(e, index, 'input')}
                                                    />
                                                )}
                                                {(item.dataType === 5 || item.extend) && (
                                                    <Select
                                                        placeholder={I18N.testservicemodal.index.qingXuanZe}
                                                        allowClear={true}
                                                        disabled={item.type === 'constant'}
                                                        dropdownMatchSelectWidth={false}
                                                        value={
                                                            item.type === 'constant'
                                                                ? item?.serviceParam || undefined
                                                                : item?.eleValue || undefined
                                                        }
                                                        style={{ width: 300 }}
                                                        onChange={(e) => this.changeHandle(e, index, 'select')}
                                                        dropdownAlign={{
                                                            points: ['tr', 'br']
                                                        }}>
                                                        {item.extend &&
                                                            item.extend.map((sItem, sIndex) => {
                                                                return (
                                                                    <Option value={sItem.value} key={sIndex}>
                                                                        {sItem.description}
                                                                    </Option>
                                                                );
                                                            })}
                                                    </Select>
                                                )}
                                                {(item.dataType === 2 || item.dataType === 3) && !item.extend && (
                                                    <InputNumber
                                                        placeholder={I18N.testservicemodal.index.qingShuRu}
                                                        disabled={item.type === 'constant'}
                                                        style={{ width: 230 }}
                                                        value={
                                                            item.type === 'constant'
                                                                ? item?.serviceParam || undefined
                                                                : item?.eleValue || undefined
                                                        }
                                                        onChange={(e) => this.changeHandle(e, index, 'number')}
                                                    />
                                                )}
                                                {item.dataType === 4 && !item.extend && (
                                                    <DatePicker
                                                        format="YYYY-MM-DD HH:mm:ss"
                                                        dropdownClassName="u-datepicker"
                                                        allowClear
                                                        showTime
                                                        style={{ width: 230 }}
                                                        disabled={item.type === 'constant'}
                                                        value={
                                                            item.type === 'constant'
                                                                ? moment(parseInt(item.serviceParam, 10))
                                                                : moment(item.eleValue).isValid()
                                                                ? moment(item.eleValue)
                                                                : null
                                                        }
                                                        onChange={(e) => this.changeHandle(e, index, 'date')}
                                                    />
                                                )}
                                            </>
                                        )}

                                        {isOrg && (
                                            <TntdVirtualTreeSelect
                                                isOrg={true}
                                                showPrefixIcon={true}
                                                style={{ width: '100%' }}
                                                treeData={orgAll}
                                                placeholder={I18N.testservicemodal.index.qingXuanZe}
                                                treeDefaultExpandAll
                                                disabled={item.type === 'constant'}
                                                value={
                                                    item.type === 'constant' ? item?.serviceParam || undefined : item?.eleValue || undefined
                                                }
                                                onChange={(e) => this.changeHandle(e, index, 'select')}
                                                dropdownStyle={{ maxHeight: 400, overflow: 'auto', width: 340 }}
                                                dropdownClassName="test-org-tree-select"
                                                dropdownAlign={{
                                                    points: ['tr', 'br']
                                                }}
                                            />
                                        )}
                                        {isApp && (
                                            <Select
                                                style={{ width: '100%' }}
                                                showSearch
                                                value={
                                                    item.type === 'constant' ? item?.serviceParam || undefined : item?.eleValue || undefined
                                                }
                                                placeholder={I18N.components.indexpackagemodal.qingXuanZeQuDao}
                                                onChange={(e) => this.changeHandle(e, index, 'select')}>
                                                {appList.map((item) => {
                                                    if (item.key) {
                                                        return (
                                                            <Option value={item.key} key={item.key}>
                                                                {item.name}
                                                            </Option>
                                                        );
                                                    }
                                                })}
                                            </Select>
                                        )}
                                    </span>
                                </div>
                            );
                        })}
                </div>
                <div className="test-drawer-footer">
                    {/* 重置 */}
                    <Button onClick={() => this.resetParams()} className="mr10">
                        {I18N.testservicemodal.index.zhongZhi}
                    </Button>
                    {/* 测试 */}
                    <Button type="primary" onClick={() => this.props.onTest(data, name)} className="ml10" loading={loading}>
                        {I18N.testservicemodal.index.ceShi}
                    </Button>
                </div>
                {/* 测试结果 */}
                {testDrawerChildData && (
                    <Drawer
                        title={I18N.testservicemodal.index.ceShiJieGuo}
                        className="m-service-testdrawer"
                        width={450}
                        closable={true}
                        onClose={() => this.props.onChildrenDrawerClose()}
                        visible={!!testDrawerChildData}>
                        <pre style={{ whiteSpace: 'pre-wrap' }}>{JSON.stringify(testDrawerChildData, null, 4)}</pre>
                    </Drawer>
                )}
            </Drawer>
        );
    }
}

export default TestServiceModal;
