import { getUrl, deleteEmptyObjItem, getHeader } from '@/utils/common';
import request, { downloadFileHandle, PostForm } from '@/utils/request';

// 新增服务接口
const interfaceAdd = async (params) => {
    return request(
        '/bridgeApi/service/interface/add',
        {
            method: 'POST',
            // key: 'upload',
            // headers: {
            //     'Content-Type': 'application/json',
            //     ...getHeader()
            // },
            body: params
        },
        true
    );
};

// 更新服务接口
const interfaceUpdate = async (params) => {
    return request(
        '/bridgeApi/service/interface/update ',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 删除服务接口
const interfaceDelete = async (params) => {
    return request(
        '/bridgeApi/service/interface/delete',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 查看服务接口
const interfaceView = async (params) => {
    return request(
        getUrl('/bridgeApi/service/interface', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 修改服务接口状态
const interfaceStatuChange = async (params) => {
    return request(
        '/bridgeApi/service/interface/status',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 下载服务接口文档
const interfaceDocDownload = async (params, name, fileType) => {
    return downloadFileHandle(
        getUrl('/bridgeApi/service/interface/download', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        name,
        fileType,
        null,
        true
    );
};

// 服务接口列表
const interfaceList = async (params) => {
    return request(
        getUrl('/bridgeApi/service/interface/list', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 获取工作流列表
const getWorkFlowList = async (params) => {
    return request(
        getUrl('/bridgeApi/workflow/list', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 获取应用列表
const getAllField = async (params) => {
    return request(
        getUrl('/bridgeApi/systemField/allField', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 获取接口信息
const getInterfaceDetail = async (params) => {
    return request(
        getUrl('/bridgeApi/service/interface/detail', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 获取接口字典
const getInterfaceDict = async (params) => {
    return request(
        getUrl('/bridgeApi/service/interface/dict', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 获取数据源列表
const getServiceList = async (params) => {
    return request(
        getUrl('/bridgeApi/service/interface/listAll', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 测试接口
const apiTest = async (params) => {
    return request(
        '/bridgeApi/service/interface/test',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 导入服务接口
const importInterface = async (params, fileList, file) => {
    return PostForm('/bridgeApi/service/interface/import', 'POST', deleteEmptyObjItem(params), fileList, file, true);
};

// 导出服务接口
const exportRecord = async (params, name, fileType, errorMsg) => {
    return downloadFileHandle(
        getUrl('/bridgeApi/service/interface/export', params),
        {
            method: 'GET'
        },
        name,
        fileType,
        errorMsg,
        true
    );
};

// 授权接口
const authorize = async (params) => {
    return request(
        '/bridgeApi/service/interface/authorization',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

const getAuthorize = async (param) => {
    return request(
        getUrl('/bridgeApi/service/interface/authorizationQuery', param),
        {
            method: 'GET'
        },
        true
    );
};
//查询加密类型
const getEncryption = async (params) => {
    return request(
        getUrl('/bridgeApi/service/interface/encryption/dict', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// Ip黑白名单配置
const putwhitelist = async (params) => {
    return request(
        '/bridgeApi/service/interface/update/roster',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

//获取工作流入参数
const getWorkflowInputs = async (params) => {
    return request(
        getUrl('/bridgeApi/workflowConfig/workflow/params', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

//修改密钥
const updateEncryption = async (params) => {
    return request(
        '/bridgeApi/service/interface/update/encryption',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

//重置密钥
const resetSecret = async (params) => {
    return request(
        '/bridgeApi/service/interface/update/secret',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

export default {
    interfaceAdd,
    interfaceUpdate,
    interfaceDelete,
    interfaceView,
    interfaceStatuChange,
    interfaceDocDownload,
    interfaceList,
    getWorkFlowList,
    getAllField,
    getInterfaceDetail,
    getInterfaceDict,
    getServiceList,
    apiTest,
    importInterface,
    exportRecord,
    authorize,
    getAuthorize,
    getEncryption,
    putwhitelist,
    getWorkflowInputs,
    updateEncryption,
    resetSecret
};
