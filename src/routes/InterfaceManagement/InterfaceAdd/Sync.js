import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { Form, Select, Icon, Input, Button, message, Title, TreeSelect } from 'tntd';
import './index.less';
import service from '../service';

import InputParams from './components/InputParams';
import OutputParams from './components/OutputParams';
import { cloneDeep } from 'lodash';
import { getUrlKey } from '@/utils/utils';

import { useGetRuleField } from '../hook';
import { connect } from 'dva';

const { Option } = Select;

let serviceType1Data = [
    {
        serviceParam: 'serialnum',
        type: 'variable',
        field: 'S_S_SERIALNUM',
        dataType: 1,
        mustInput: true,
        defaultValue: '',
        displayName: I18N.interfaceadd.sync.yeWuLiuShuiHao
    },
    {
        serviceParam: 'orgcode',
        type: 'variable',
        field: 'S_S_ORGCODE',
        dataType: 1,
        mustInput: true,
        defaultValue: '',
        displayName: I18N.interfaceadd.sync.jiGouBiaoZhi
    },
    {
        serviceParam: 'appcode',
        type: 'variable',
        field: 'S_S_APPCODE',
        dataType: 1,
        mustInput: true,
        defaultValue: '',
        displayName: I18N.interfaceadd.sync.quDaoBiaoShi
    },
    {
        dataType: 5,
        defaultValue: '',
        displayName: I18N.interfaceadd.sync.yunXingMoShi,
        field: 'S_E_RUNTYPE',
        mustInput: true,
        serviceParam: 'runtype',
        type: 'variable'
    }
];
let serviceType2Data = [
    {
        serviceParam: 'tokenid',
        type: 'variable',
        field: 'S_S_TOKENID',
        dataType: 1,
        mustInput: true,
        defaultValue: '',
        displayName: I18N.interfaceadd.sync.fengKongWeiYiBiao
    }
];

const SyncAdd = Form.create()((props) => {
    const { form, globalStore } = props;
    const { orgList, appList, currentApp, currentOrgCode } = globalStore;
    const { getFieldDecorator, getFieldsValue, getFieldValue, setFieldsValue, setFields } = form;

    const [initData, setInitData] = useState({});

    let ruleField = useGetRuleField([]);

    let type = getUrlKey('type');
    let disabled = type === 'view';
    let isUpdate = type === 'update';

    let { inputConfig, outputConfig, appType, name, serviceType, sourceName, workflowName, displayName, contentType, appCode, orgCode } =
        getFieldsValue();

    const parseData = (data) => {
        let res = [];
        let arr = data ? JSON.parse(data) : [];
        arr.forEach((i, index) => {
            res.push(i);
        });
        return res;
    };

    useEffect(() => {
        let {
            inputConfig,
            outputConfig,
            appType,
            name,
            serviceType,
            workflowName,
            displayName,
            contentType,
            sourceName,
            appCode,
            orgCode
        } = initData;
        if (!name) return;

        let outputConfigData = parseData(outputConfig);
        setFieldsValue({
            inputConfig: inputConfig ? JSON.parse(inputConfig) : [],
            outputConfig: outputConfigData,
            appType,
            name,
            serviceType,
            workflowName,
            contentType,
            displayName,
            sourceName,
            appCode,
            orgCode
        });
    }, [initData]);

    // 把工作流列表使用useState存储起来
    const [workFlowList, setWorkFlowList] = useState([]);
    // 获取全部字段列表，并把字段列表存储起来
    const [allField, setAllField] = useState([]);

    // 用来过滤的asyncInputConfig数据的搜索值
    const [inputSearch, setInputSearch] = useState('');
    const [outputSearch, setOutputSearch] = useState('');
    const [appTypeList, setAppTypeList] = useState([]);
    const [serviceList, setServiceList] = useState([]);

    // 初始化工作流列表
    useEffect(() => {
        getAllfield();
        getInterfaceDict();
        getServiceList();

        if (['update', 'view'].includes(type)) {
            getInterfaceDetail(getUrlKey('uuid'));
        }
    }, []);

    //工作流变化后自动反显数据
    useEffect(() => {
            const uuid = workFlowList.filter((v) => v.code === workflowName)[0]?.uuid;
            service.getWorkflowInputs({ uuid }).then((res) => {
                let newInputData = cloneDeep(serviceType1Data);
                if (res?.success) {
                    newInputData = newInputData.concat(res?.data?.inputs);
                    let newOutputData = cloneDeep(res?.data?.outputs);
                    newOutputData.map((v,i)=>{
                        v.name = res?.data?.outputs[i].field
                    })
                    setFieldsValue({
                        inputConfig: newInputData,
                        outputConfig: newOutputData,
                        sourceName: undefined
                    });
                }
            });
    }, [workflowName]);
    // 获取接口信息
    const getInterfaceDetail = (uuid) => {
        service.getInterfaceDetail({ uuid }).then((res) => {
            setInitData(res.data || {});
        });
    };

    // 获取接口信息
    const getInterfaceDict = () => {
        service.getInterfaceDict().then((res) => {
            if (res.success) {
                setAppTypeList(res.data.appTypeList || []);
            }
        });
    };

    // 获取接口信息
    const getServiceList = () => {
        service.getServiceList({ serviceType: 1 }).then((res) => {
            if (res.success) {
                setServiceList(res.data || []);
            }
        });
    };

    // 获取全部字段列表
    const getAllfield = () => {
        service.getAllField().then((res) => {
            if (res.success) {
                setAllField(res?.data?.systemField || []);
            }
        });
    };

    // 获取工作流列表
    const getWorkFlowList = (params) => {
        service.getWorkFlowList(params).then((res) => {
            if (res.success) {
                setWorkFlowList(res.data);
            }
        });
    };

    //  格式化服务出参
    const formatOutputConfig = (outputConfig) => {
        let arr = [];
        arr = outputConfig.filter((item) => {
            let flag = !(item.path && item.deep !== 0);
            if (item.deep || delete item.path || delete item.field) {
                delete item.deep;
                delete item.path;
                delete item.field;
            }
            return flag;
        });
        return arr;
    };

    useEffect(()=>{
        if(appCode && orgCode) {
            getWorkFlowList({appCode,orgCode});
        }
    },[appCode,orgCode])
    const onOk = () => {
        form.validateFields((err, values) => {
            if (!err) {
                let {
                    inputConfig,
                    outputConfig,
                    appType,
                    name,
                    serviceType,
                    workflowName,
                    displayName,
                    contentType,
                    sourceName,
                    appCode,
                    orgCode
                } = values;

                // 校验inputConfig是否存在空值
                let inputConfigFlag = inputConfig.some((item) => {
                    return !item.serviceParam || !item.field;
                });

                // 校验outputConfig是否存在空值
                let outputConfigFlag = outputConfig.some((item) => {
                    return !item.serviceParam || !item.name;
                });

                if (inputConfigFlag) {
                    message.warning(I18N.interfaceadd.sync.fuWuRuCanZhong);
                    return;
                }

                if (outputConfigFlag) {
                    message.warning(I18N.interfaceadd.sync.fuWuChuCanZhong);
                    return;
                }
                outputConfig = formatOutputConfig(cloneDeep(outputConfig));

                let params = {
                    inputConfig: JSON.stringify(inputConfig),
                    outputConfig: JSON.stringify(outputConfig),
                    appType,
                    displayName,
                    name,
                    serviceType,
                    workflowName,
                    contentType,
                    sourceName,
                    appCode,
                    orgCode
                };

                let queryName = 'interfaceAdd';
                if (type === 'update') {
                    params.uuid = getUrlKey('uuid');
                    queryName = 'interfaceUpdate';
                }

                service[queryName]({ ...params }).then((res) => {
                    if (res.success) {
                        message.success(I18N.interfaceadd.sync.caoZuoChengGong);
                        props.history.push('/handle/interface/management');
                    } else {
                        message.error(res.message);
                    }
                });
            }
        });
    };

    // 清空服务入参中的serviceParam字段
    const clearInputField = () => {
        let data = cloneDeep(getFieldsValue());
        data.inputConfig.forEach((item) => {
            item.serviceParam = '';
        });
        setFieldsValue({
            inputConfig: data.inputConfig
        });
    };

    //  清空服务入参列表
    const clearInputList = () => {
        let data = getFieldsValue();
        let defaultData = [];
        if (serviceType) {
            defaultData = serviceType === 1 ? serviceType1Data : serviceType2Data;
        }
        data.inputConfig = defaultData;
        setFieldsValue(data);
    };

    //  服务入参搜索
    const searchInput = (e) => {
        setInputSearch(e.target.value);
    };
    //  服务入参搜索
    const searchOutput = (e) => {
        setOutputSearch(e.target.value);
    };

    // 服务入参的标题
    let inputTitle = (
        <>
            <span>{I18N.interfaceadd.sync.fuWuRuCan}</span>
            {type !== 'view' && (
                <div className="input-actions-btn">
                    <Button type="primary" ghost size="small" onClick={clearInputList} disabled={disabled}>
                        {I18N.interfaceadd.sync.qingKongLieBiao}
                    </Button>
                    <Button type="primary" ghost size="small" onClick={clearInputField} disabled={disabled}>
                        {I18N.interfaceadd.sync.qingKongRuCanZi}
                    </Button>
                </div>
            )}
            <Input
                disabled={disabled}
                onChange={searchInput}
                className="search"
                style={{ width: 250 }}
                placeholder={I18N.interfaceadd.sync.qingShuRuZiDuan}
                suffix={<Icon type="search" style={{ color: 'rgba(0,0,0,.45)' }} />}
            />
        </>
    );

    const outputTitle = (
        <>
            <span>{I18N.interfaceadd.sync.fuWuChuCan}</span> {/* 数据模型Select框 */}
            <Input
                disabled={disabled}
                onChange={searchOutput}
                className="search"
                style={{ width: 250 }}
                placeholder={I18N.interfaceadd.sync.qingShuRuZiDuan}
                suffix={<Icon type="search" style={{ color: 'rgba(0,0,0,.45)' }} />}
            />
        </>
    );

    return (
        <div className="sync-interface-add-detail">
            <Title className="title" bold size="small" title={I18N.interfaceadd.sync.fuWuJieKouGuan} />
            <Form>
                <Form.Item label={I18N.interfaceadd.sync.fuWuMingCheng} colon={false}>
                    {getFieldDecorator('displayName', {
                        initialValue: displayName,
                        rules: [
                            { required: true, message: I18N.interfaceadd.sync.qingShuRuFuWu2 },
                            { max: 200, message: I18N.interfaceadd.sync.changDuBuNengChao },
                            {
                                pattern: /^[\w\s\u4E00-\u9FA5\-.]+$/,
                                message: I18N.interfaceadd.sync.fuWuBiaoZhiZhi2
                            }
                        ]
                    })(<Input placeholder={I18N.interfaceadd.sync.qingShuRuFuWu2} disabled={disabled} />)}
                </Form.Item>
                <Form.Item label={I18N.interfaceadd.sync.fuWuBiaoZhi} colon={false}>
                    {getFieldDecorator('name', {
                        initialValue: name,
                        rules: [
                            { required: true, message: I18N.interfaceadd.sync.qingShuRuFuWu },
                            { max: 200, message: I18N.interfaceadd.sync.changDuBuNengChao },
                            {
                                pattern: /^[a-zA-Z0-9_]+$/,
                                message: I18N.interfaceadd.sync.fuWuBiaoZhiZhi
                            }
                        ]
                    })(<Input placeholder={I18N.interfaceadd.sync.qingShuRuFuWu} disabled={disabled || isUpdate} />)}
                </Form.Item>

                <Form.Item label={I18N.interfaceadd.sync.suoShuJiGou} colon={false}>
                    {getFieldDecorator('orgCode', {
                        initialValue: orgCode,
                        rules: [{ required: true, message: I18N.interfaceadd.sync.qingXuanZeSuoShu2 }]
                    })(
                        <TreeSelect
                            placeholder={I18N.interfaceadd.sync.qingXuanZeSuoShu2}
                            treeNodeFilterProp="title"
                            showSearch
                            disabled={disabled || isUpdate}
                            treeData={orgList}
                            treeDefaultExpandAll
                            // onChange={(e) => {
                            //     getAppByOrgId(e);
                            //     form.setFieldsValue({ appCode: undefined });
                            // }}
                            dropdownStyle={{ maxHeight: 300, overflow: 'auto', width: 340 }}
                        />
                    )}
                </Form.Item>
                <Form.Item label={I18N.components.commontable.suoShuQuDao} colon={false}>
                    {getFieldDecorator('appCode', {
                        initialValue: appCode,
                        rules: [{ required: true, message: I18N.interfaceadd.sync.qingXuanZeSuoShu }]
                    })(
                        <Select
                            showSearch
                            disabled={disabled || isUpdate}
                            filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                            placeholder={I18N.interfaceadd.sync.qingXuanZeSuoShu}>
                            {appList.map((item) => {
                                if(item.key) {
                                    return (
                                        <Option value={item.key} key={item.key}>
                                            {item.name}
                                        </Option>
                                    );
                                }
                            })}
                        </Select>
                    )}
                </Form.Item>

                <Form.Item label={I18N.interfaceadd.sync.fuWuLeiXing} colon={false}>
                    {getFieldDecorator('serviceType', {
                        initialValue: (serviceType && Number(serviceType)) || undefined,
                        rules: [{ required: true, message: I18N.interfaceadd.sync.qingXuanZeFuWu2 }]
                    })(
                        <Select
                            placeholder={I18N.interfaceadd.sync.qingXuanZeFuWu2}
                            disabled={disabled || isUpdate}
                            onChange={(val) => {
                                if (val === 1) {
                                    setFieldsValue({
                                        inputConfig: serviceType1Data,
                                        sourceName: undefined
                                    });
                                } else if (val === 2) {
                                    setFieldsValue({
                                        inputConfig: serviceType2Data,
                                        workflowName: undefined
                                    });
                                }
                            }}>
                            <Option key="1" value={1}>
                                {I18N.interfaceadd.sync.shuJuFuWu}
                            </Option>
                            <Option key="2" value={2}>
                                {I18N.interfaceadd.sync.shuJuBuChong}
                            </Option>
                        </Select>
                    )}
                </Form.Item>
                <Form.Item
                    label={I18N.interfaceadd.sync.gongZuoLiu}
                    colon={false}
                    style={{ display: `${serviceType === 1 ? 'block' : 'none'}` }}>
                    {getFieldDecorator('workflowName', {
                        initialValue: workflowName,
                        rules: [{ required: serviceType === 1, message: I18N.interfaceadd.sync.qingXuanZeGongZuo }]
                    })(
                        <Select
                            placeholder={I18N.interfaceadd.sync.qingXuanZeGongZuo}
                            disabled={disabled}
                            optionFilterProp="children"
                            showSearch>
                            {(workFlowList || []).map((item) => (
                                <Option key={item.code} value={item.code}>
                                    {item.displayName}
                                </Option>
                            ))}
                        </Select>
                    )}
                </Form.Item>

                <Form.Item
                    label={I18N.interfaceadd.sync.shuJuFuWu}
                    colon={false}
                    style={{ display: `${serviceType === 2 ? 'block' : 'none'}` }}>
                    {getFieldDecorator('sourceName', {
                        initialValue: sourceName || undefined,
                        rules: [{ required: serviceType === 2, message: I18N.interfaceadd.sync.qingXuanZeShuJu }]
                    })(
                        <Select placeholder={I18N.interfaceadd.sync.qingXuanZeShuJu} disabled={disabled}>
                            {serviceList.map((item) => {
                                return (
                                    <Option key={item.name} value={item.name}>
                                        {item.displayName}
                                    </Option>
                                );
                            })}
                        </Select>
                    )}
                </Form.Item>

                {serviceType !== 2 && (
                    <Form.Item label={I18N.interfaceadd.sync.fuWuChangJing} colon={false}>
                        {getFieldDecorator('appType', {
                            initialValue: appType,
                            rules: [{ required: true, message: I18N.interfaceadd.sync.qingXuanZeFuWu }]
                        })(
                            <Select placeholder={I18N.interfaceadd.sync.qingXuanZeFuWu} disabled={disabled}>
                                {(appTypeList || []).map((item) => (
                                    <Option key={item.name} value={item.name}>
                                        {item.displayName}
                                    </Option>
                                ))}
                            </Select>
                        )}
                    </Form.Item>
                )}

                <Form.Item label={I18N.interfaceadd.sync.diaoYongFangShi} colon={false}>
                    {getFieldDecorator('contentType', {
                        initialValue: contentType || undefined,
                        rules: [{ required: true, message: I18N.interfaceadd.sync.qingXuanZeDiaoYong }]
                    })(
                        <Select placeholder={I18N.interfaceadd.sync.qingXuanZeDiaoYong} disabled={disabled}>
                            <Option key="application/x-www-form-urlencoded" value={'application/x-www-form-urlencoded'}>
                                application/x-www-form-urlencoded
                            </Option>
                            <Option key="application/json" value={'application/json'}>
                                application/json
                            </Option>
                            <Option key="application/xml" value={'application/xml'}>
                                application/xml
                            </Option>
                            <Option key="multipart/form-data" value={'multipart/form-data'}>
                                multipart/form-data
                            </Option>
                        </Select>
                    )}
                </Form.Item>

                <Form.Item label={inputTitle} className="full" colon={false}>
                    {getFieldDecorator('inputConfig', {
                        initialValue: inputConfig,
                        rules: [{ required: true, message: I18N.interfaceadd.sync.shuJuCunZaiKong }]
                    })(
                        <InputParams
                            allFieldList={allField}
                            setFieldsValue={setFieldsValue}
                            setFields={setFields}
                            filterListSearch={inputSearch}
                            disabled={disabled}
                            serviceType={serviceType}
                        />
                    )}
                </Form.Item>
                <Form.Item label={outputTitle} className="full" colon={false}>
                    {getFieldDecorator('outputConfig', {
                        initialValue: outputConfig,
                        rules: [{ required: true, message: I18N.interfaceadd.sync.shuJuCunZaiKong }]
                    })(
                        <OutputParams
                            allFieldList={allField}
                            setFieldsValue={setFieldsValue}
                            setFields={setFields}
                            filterListSearch={outputSearch}
                            setAllField={setAllField}
                            disabled={disabled}
                            serviceType={serviceType}
                            ruleField={ruleField}
                        />
                    )}
                </Form.Item>
            </Form>
            {/*  提交表单数据 */}
            <div className="submit-btn">
                <Button
                    onClick={() => {
                        props.history.push('/handle/interface/management');
                    }}>
                    {I18N.interfaceadd.sync.fanHui}
                </Button>
                {type !== 'view' && (
                    <Button type="primary" onClick={onOk}>
                        {I18N.interfaceadd.sync.tiJiao}
                    </Button>
                )}
            </div>
        </div>
    );
});

export default connect((state) => ({
    globalStore: state.global
}))(SyncAdd);
