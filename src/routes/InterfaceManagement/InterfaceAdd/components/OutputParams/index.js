import I18N from '@/utils/I18N';
import { useState, useEffect, useMemo, memo, useRef } from 'react';
import { Input, Icon, Table, Tooltip, Select } from 'tntd';
import { cloneDeep, forEach, isEqual, set } from 'lodash';
// import IndicatorsCascader from '@/components/IndicatorsCascader';
import TooltipSelect from '@tntd/tooltip-select';

const Option = Select.Option;

let defaultData = {
    serviceParam: undefined, // 入参字段
    name: undefined, //系统字段标识
    displayName: undefined, // 系统字段名称
    dataType: undefined, // 字段数据类型
    modelFieldType: 4 //参数类型(constant常量/variable变量)
};

// TooltipSelect的高阶组件
let MemoSelect = memo(
    (props) => {
        let children = useMemo(() => {
            return (props?.allFieldList || []).map((item) => {
                // 已经选择的需要禁用
                const disabled1 = props?.inputMap?.[item.name] || item.disabled;
                // if(disabled1){
                //     console.log(item,props?.inputMap,props,'props?.allFieldList')
                // }
                return (
                    <Option
                        key={item.name}
                        value={item.name}
                        datatype={item.fieldType.value}
                        displayname={item.displayName}
                        disabled={disabled1}
                        title={`${item.displayName}【${item.name}】`}>
                        <Tooltip>
                            <sup style={{ color: 'blue' }}> {item.fieldType?.displayName} </sup>
                            {item.displayName + '【' + item.name + '】'}
                        </Tooltip>
                    </Option>
                );
            });
        }, [props.value, props.allFieldList, props.inputMap]);
        return <TooltipSelect {...props}>{children}</TooltipSelect>;
    },
    (prevProps, nextProps) => {
        if (
            prevProps.value === nextProps.value &&
            prevProps.disabled === nextProps.disabled &&
            prevProps?.allFieldList.length === nextProps?.allFieldList.length
        ) {
            return true;
        }

        return false;
    }
);

export default (props) => {
    let { value: data = [], onChange, allFieldList, filterListSearch, setAllField, disabled, canAdd = true } = props;

    const [curPage, setCurPage] = useState(1);

    const dataRef = useRef([]);
    const InputMapRef = useRef({});

    dataRef.current = useMemo(() => {
        return data.map((i, index) => {
            return {
                ...i,
                key: index
            };
        });
    }, [data, data.length]);

    const inputMapChange = (val) => {
        // const inputMapTemp = {};
        let temp = val || dataRef.current;
        InputMapRef.current={};
        temp?.forEach((v) => {
            InputMapRef.current[v.name] = v;
        });
        // setInputMap(inputMapTemp);
    };

    useEffect(() => {
        if (dataRef.current.length) {
            inputMapChange();
        }
    }, [dataRef.current]);

    const pageChange = (page) => {
        setCurPage(page);
    };

    const addItem = () => {
        let newData = [...dataRef.current, cloneDeep(defaultData)];
        // 默认跳转到最后一页
        setCurPage(Math.ceil(newData.length / 10));
        onChange && onChange(newData);
    };

    const fieldsChange = (val, type, index, e, data) => {
        // serviceParam默认是字段名4位之后的小写字母，如果是常量，需要手动输入
        let serviceParam = val.slice(4).toLowerCase();

        let dataType = e.props.datatype;
        let displayName = e.props.displayname;

        data[index].name = val;
        data[index].dataType = dataType;
        data[index].displayName = displayName;
        data[index].serviceParam = serviceParam;

        // 如果是常量，需要手动输入
        if (type === 'variable') {
            data[index].serviceParam = serviceParam;
        }
        onChange(data);
    };

    const handleInputChange = (index, e) => {
        dataRef.current[index].serviceParam = e.target.value;
        onChange(dataRef.current);
    };

    const setCurrentPage = (currentValueLength = 0) => {
        let p = Math.ceil(currentValueLength / 10);
        if (p < curPage) {
            setCurPage(p === 0 ? 1 : p);
        }
    };

    // 删除列表项
    const delTableItem = async (row, index) => {
        let newValue = [...dataRef.current];
        newValue.splice(index, 1);
        setCurrentPage(newValue.length);
        onChange(newValue);
        inputMapChange(newValue);
    };

    // // 设置服务入参 - 下拉列表可选项
    // const setInputList = () => {
    //     let arr = cloneDeep(allFieldList);
    //     // 重置
    //     arr.forEach((item) => {
    //         item.disabled = false;
    //     });
    //     value.forEach((item) => {
    //         // 已选字段置灰不能选
    //         arr.forEach((subItem) => {
    //             if (item.name === subItem.name) {
    //                 subItem.disabled = true;
    //             }
    //         });
    //     });
    //     setAllField(arr);
    // };

    const columns = [
        {
            title: I18N.outputparams.index.chuCanZiDuan,
            width: 200,
            dataIndex: 'serviceParam',
            render: (text, row, index) => {
                return (
                    <Input
                        placeholder={I18N.outputparams.index.qingShuRuCanZi}
                        value={text}
                        onChange={(e) => handleInputChange(row.key, e)}
                        disabled={disabled}
                    />
                );
            }
        },
        {
            title: I18N.outputparams.index.fuZhiFangXiang,
            width: 100,
            align: 'center',
            render: () => {
                return <Icon type="arrow-left" />;
            }
        },
        {
            title: <div style={{ textAlign: 'center' }}>{I18N.outputparams.index.xiTongZiDuan}</div>,
            width: 360,
            dataIndex: 'name',
            render: (text, row, index) => {
                let { type } = row;

                return (
                    <MemoSelect
                        isVirtual
                        // isMemo={true}
                        placeholder={I18N.outputparams.index.qingShuRuXiTong}
                        value={text}
                        optionFilterProp="children"
                        filterOption={(input, option) => option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                        showSearch
                        // parentValue={value}
                        onChange={(val, e) => {
                            fieldsChange(val, type, row.key, e, dataRef.current);
                            inputMapChange();
                        }}
                        style={{ width: 300 }}
                        disabled={disabled}
                        allFieldList={allFieldList}
                        inputMap={InputMapRef.current}
                    />
                );
            }
        },
        {
            title: I18N.outputparams.index.caoZuo,
            width: 80,
            dataIndex: 'operation',
            render: (text, row, index) => {
                if (disabled) return null;

                return <Icon type="delete" onClick={() => delTableItem(row, row.key)} />;
            }
        }
    ];

    return (
        <div className="input-params">
            <Table
                size="small"
                indentSize={20}
                childrenColumnName=""
                dataSource={(dataRef.current || []).filter((item) => {
                    // 根据filterListSearch的值过滤,如果是空字符串，就不过滤, 如果是字段名或者字段标识包含filterListSearch的值，就显示
                    if (filterListSearch === '') {
                        return true;
                    }
                    if (
                        item?.name?.includes(filterListSearch) ||
                        item?.serviceParam?.includes(filterListSearch) ||
                        item?.displayName?.indexOf(filterListSearch) > -1
                    ) {
                        return true;
                    }
                    return false;
                })}
                columns={columns}
                rowKey={(row, index) => {
                    return String(index) + row.name || '';
                }}
                pagination={{
                    current: curPage,
                    pageSize: 10,
                    onChange: (page) => {
                        pageChange(page);
                    }
                }}
            />
            {canAdd && !disabled && (
                <div className="add-field-btn" onClick={addItem}>
                    <span>
                        <Icon type="plus" onClick={addItem} /> {I18N.outputparams.index.tianJia}
                    </span>
                </div>
            )}
        </div>
    );
};
