import I18N from '@/utils/I18N';
import { useEffect, useState, useRef, useMemo, memo } from 'react';
import { Input, Icon, Switch, Table, Tooltip, Select } from 'tntd';
import { cloneDeep } from 'lodash';
import TooltipSelect from '@tntd/tooltip-select';
import otp from '../../otp';

const Option = Select.Option;

import './index.less';

let defaultData = {
    serviceParam: undefined, //参数名
    type: 'variable', //参数类型(constant常量/variable变量)
    field: undefined, //系统字段标识
    dataType: null, //字段数据类型
    mustInput: false, //是否必填
    defaultValue: '' //字段为空时使用的默认值
};

// TooltipSelect的高阶组件
let MemoSelect = memo(
    (props) => {
        let children = useMemo(() => {
            return (props?.allFieldList || []).map((item) => {
                // 已经选择的需要禁用
                const disabled1 = props?.inputMap?.[item.name] || item.disabled;
                return (
                    <Option
                        key={item.name}
                        value={item.name}
                        datatype={item.fieldType.value}
                        displayname={item.displayName}
                        disabled={disabled1}
                        title={`${item.displayName}【${item.name}】`}>
                        <Tooltip>
                            <sup style={{ color: 'blue' }}> {item.fieldType?.displayName} </sup>
                            {item.displayName + '【' + item.name + '】'}
                        </Tooltip>
                    </Option>
                );
            });
        }, [props.value, props.allFieldList, props.inputMap]);
        return <TooltipSelect {...props}>{children}</TooltipSelect>;
    },
    (prevProps, nextProps) => {
        if (
            prevProps.value === nextProps.value &&
            prevProps.disabled === nextProps.disabled &&
            prevProps?.allFieldList.length === nextProps?.allFieldList.length
        ) {
            return true;
        }
        console.log('MemoSelect Render', nextProps?.value);
        return false;
    }
);

export default (props) => {
    const {
        value: data = [],
        onChange,
        allFieldList,
        disabled,
        filterListSearch,
        serviceType,
        canAdd = true,
        serviceParamEditable
    } = props;

    const [curPage, setCurPage] = useState(1);

    const dataRef = useRef([]);
    const InputMapRef = useRef({});

    dataRef.current = useMemo(() => {
        return data.map((i, index) => {
            return {
                ...i,
                key: index
            };
        });
    }, [data]);

    const inputMapChange = (val) => {
        // const inputMapTemp = {};
        InputMapRef.current={};
        let temp = val || dataRef.current;
        temp?.forEach((v) => {
            InputMapRef.current[v.field] = v;
        });
        // setInputMap(inputMapTemp);
    };

    useEffect(() => {
        if (dataRef.current.length) {
            inputMapChange();
        }
    }, [dataRef.current]);

    const pageChange = (page) => {
        setCurPage(page);
    };

    const addItem = () => {
        let newData = [...dataRef.current, cloneDeep(defaultData)];
        // 默认跳转到最后一页
        setCurPage(Math.ceil(newData.length / 10));
        onChange && onChange(newData);
    };

    const fieldsChange = (val, type, index, e) => {
        let newData = [...dataRef.current];
        // serviceParam默认是字段名4位之后的小写字母，如果是常量，需要手动输入
        let serviceParam = val.slice(4).toLowerCase();

        let dataType = e.props.datatype;
        let displayName = e.props.displayname;

        newData[index].field = val;
        newData[index].dataType = dataType;
        newData[index].displayName = displayName;
        newData[index].serviceParam = serviceParam;
        // 如果是常量，需要手动输入
        if (type === 'variable') {
            newData[index].serviceParam = serviceParam;
        }
        onChange(newData);
    };

    const handleInputChange = (index, e) => {
        let newData = [...dataRef.current];
        newData[index].serviceParam = e.target.value;
        onChange(newData);
    };

    const defaultValueChange = (index, e) => {
        // 分页确定当前的index
        let newData = [...dataRef.current];
        newData[index].defaultValue = e.target.value;
        onChange(newData);
    };

    const setCurrentPage = (currentValueLength = 0) => {
        let p = Math.ceil(currentValueLength / 10);
        if (p < curPage) {
            setCurPage(p === 0 ? 1 : p);
        }
    };

    const columns = [
        {
            title: I18N.inputparams.index.ruCanZiDuan,
            width: otp.width1,
            dataIndex: 'serviceParam',
            render: (text, row, index) => {
                let { field, type } = row;

                return (
                    <Input
                        placeholder={
                            type === 'variable' ? I18N.inputparams.index.qingShuRuCanZi : I18N.inputparams.index.qingShuRuChangLiang
                        }
                        value={text}
                        onChange={(e) => handleInputChange(row.key, e)}
                        disabled={disabled && !serviceParamEditable}
                    />
                );
            }
        },
        {
            title: I18N.inputparams.index.fuZhiFangXiang,
            width: 100,
            align: 'center',
            render: () => {
                return <Icon type="arrow-right" />;
            }
        },
        {
            title: I18N.inputparams.index.xiTongZiDuan,
            width: 250,
            dataIndex: 'field',
            render: (text, row, index) => {
                let { type, field } = row;
                let canEdit = false;
                if (serviceType === 2) {
                    canEdit = ['S_S_TOKENID'].includes(field);
                } else if (serviceType === 1) {
                    canEdit = ['S_S_ORGCODE', 'S_S_SERIALNUM', 'S_E_RUNTYPE','S_S_APPCODE'].includes(field);
                }
                return (
                    <MemoSelect
                        isVirtual
                        isMemo={true}
                        placeholder={I18N.inputparams.index.qingShuRuXiTong}
                        value={text}
                        optionFilterProp="children"
                        filterOption={(input, option) => option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                        showSearch
                        dropdownMatchSelectWidth={false}
                        dropdownStyle={{ width: 350 }}
                        onChange={(val, e) => {fieldsChange(val, type, row.key, e);inputMapChange()}}
                        style={{ width: 250 }}
                        disabled={disabled || canEdit}
                        allFieldList={allFieldList}
                        inputMap={InputMapRef.current}>
                        {(props.allFieldList || []).map((item) => {
                            // 已经选择的需要禁用
                            let disabled = !!props?.value?.find((v) => v.field === item.name);

                            return (
                                <Option
                                    key={item.name}
                                    value={item.name}
                                    datatype={item.fieldType.value}
                                    displayname={item.displayName}
                                    disabled={disabled}
                                    title={`${item.displayName}【${item.name}】`}>
                                    <Tooltip>
                                        <sup style={{ color: 'blue' }}> {item.fieldType?.displayName} </sup>
                                        {item.displayName + '【' + item.name + '】'}
                                    </Tooltip>
                                </Option>
                            );
                        })}
                    </MemoSelect>
                );
            }
        },
        {
            title: I18N.inputparams.index.shiFouBiTian,
            align: 'center',
            width: 100,
            dataIndex: 'mustInput',
            render: (text, row, index) => {
                let checked = text === true;
                let { field } = row;
                let canEdit = false;
                if (serviceType === 2) {
                    //  服务类型为2时，S_S_TOKENID必填
                    canEdit = ['S_S_TOKENID'].includes(field);
                } else if (serviceType === 1) {
                    // 服务类型为1时，S_S_ORGCODE、S_S_SERIALNUM必填
                    canEdit = ['S_S_ORGCODE', 'S_S_SERIALNUM', 'S_E_RUNTYPE','S_S_APPCODE'].includes(field);
                }
                return (
                    <Switch
                        disabled={disabled || canEdit}
                        checked={checked}
                        checkedChildren={I18N.inputparams.index.shi}
                        unCheckedChildren={I18N.inputparams.index.fou}
                        onChange={(checked) => {
                            let newData = [...dataRef.current];
                            newData[row.key].mustInput = checked;
                            onChange(newData);
                        }}
                    />
                );
            }
        },
        {
            title: I18N.inputparams.index.quZhiFangShi,
            align: 'center',
            width: 100,
            dataIndex: 'type',
            render: (text, row, index) => {
                let checked = text === 'variable';

                let { field } = row;
                let canEdit = false;
                if (serviceType === 2) {
                    canEdit = ['S_S_TOKENID'].includes(field);
                } else if (serviceType === 1) {
                    canEdit = ['S_S_ORGCODE', 'S_S_SERIALNUM', 'S_E_RUNTYPE','S_S_APPCODE'].includes(field);
                }
                return (
                    <Switch
                        disabled={disabled || canEdit}
                        checked={checked}
                        checkedChildren={I18N.inputparams.index.bianLiang}
                        unCheckedChildren={I18N.inputparams.index.changLiang}
                        onChange={(checked) => {
                            let newData = [...dataRef.current];
                            newData[row.key].type = checked ? 'variable' : 'constant';
                            if (checked === false) {
                                newData[row.key].serviceParam = '';
                            }
                            onChange(newData);
                        }}
                    />
                );
            }
        },
        {
            title: I18N.inputparams.index.kongFuZhi,
            width: 100,
            dataIndex: 'defaultValue',
            render: (text, row, index) => {
                let { type, field } = row;
                let disabled = type !== 'variable';

                return <Input value={text} onChange={(e) => defaultValueChange(row.key, e)} disabled={disabled || props.disabled} />;
            }
        },
        {
            title: I18N.inputparams.index.caoZuo,
            width: 80,
            dataIndex: 'operation',
            fixed: 'right',
            render: (...arg) => {
                // let index = arg[2];
                let { field, key } = arg[1];
                let canEdit = field === 'S_S_TOKENID' && serviceType === 2;

                // 服务类型为1时，S_S_ORGCODE、S_S_SERIALNUM不可删除
                let canDelete = false;
                if (serviceType === 2) {
                    canDelete = ['S_S_TOKENID'].includes(field);
                } else if (serviceType === 1) {
                    canDelete = ['S_S_ORGCODE', 'S_S_SERIALNUM', 'S_E_RUNTYPE','S_S_APPCODE'].includes(field);
                }
                return (
                    <>
                        {disabled || canEdit || canDelete ? null : (
                            <Icon
                                type="delete"
                                onClick={() => {
                                    // 删除当前行
                                    let newValue = [...dataRef.current];
                                    newValue.splice(key, 1);

                                    // 如果删除的是最后一页的最后一条数据，就跳转到上一页
                                    setCurrentPage(newValue?.length);
                                    onChange(newValue);
                                    inputMapChange(newValue);
                                }}
                            />
                        )}
                    </>
                );
            }
        }
    ];

    return (
        <div className="input-params">
            <Table
                size="small"
                dataSource={(dataRef.current || []).filter((item) => {
                    // 根据filterListSearch的值过滤,如果是空字符串，就不过滤, 如果是字段名或者字段标识包含filterListSearch的值，就显示
                    if (filterListSearch === '') {
                        return true;
                    }
                    if (
                        item?.field?.includes(filterListSearch) ||
                        item?.serviceParam?.includes(filterListSearch) ||
                        item?.displayName?.indexOf(filterListSearch) > -1
                    ) {
                        return true;
                    }
                    return false;
                })}
                columns={columns}
                rowKey={(row, index) => {
                    return String(index) + row.field;
                }}
                pagination={{
                    current: curPage,
                    pageSize: 10,
                    onChange: pageChange
                }}
                scroll={{ x: 1000 }}
            />
            {canAdd && !disabled && (
                <div className="add-field-btn" onClick={addItem}>
                    <span>
                        <Icon type="plus" onClick={addItem} /> {I18N.inputparams.index.tianJia}
                    </span>
                </div>
            )}
        </div>
    );
};
