/*
 * @Author: liu<PERSON>
 * @CreatDate: 2018-08-28 15:47:11
 * @Describe: 堆叠柱状图表组件 - 采用echarts第三方库 - macarons主题
 */

import { PureComponent } from 'react';
// 加载echarts，注意引入文件的路径
import Echarts from 'echarts/lib/echarts';
// 再引入你需要使用的图表类型，标题，提示信息等
import 'echarts/lib/chart/bar';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/title';

import '../Theme/macarons';

import { appChannelLang } from '@/constants/lang';

export default class StackedBarChart extends PureComponent {

	state = {
		defaultHeight: 300
	};
	constructor(props) {
		super(props);
		this.draw = this.draw.bind(this);
	}

	componentDidMount() {
		const { idName, chartData } = this.props;
		this.draw(idName, chartData);
	}

	componentDidUpdate(prevProps) {
		let preChartData = prevProps.chartData;
		let nextChartData = this.props.chartData;
		if (preChartData !== nextChartData) {
			// 检测到数据改变，重绘图表
			const { idName, chartData } = this.props;
			this.draw(idName, chartData);
		}
	}

	draw(idName, chartData) {
		if (!chartData) return;
		const { type } = this.props;
		const dom = document.getElementById(idName);
		const myChart = Echarts.init(dom, 'macarons');
		const option = {
			tooltip: {
				trigger: 'axis',
				axisPointer: { // 坐标轴指示器，坐标轴触发有效
					type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
				}
			},
			legend: {
				data: chartData.legendData,
				bottom: 0
			},
			color: ['#fc747a', '#3ed19b', '#fcbc5a'],
			grid: {
				left: '3%',
				right: 70,
				bottom: 40,
				top: 30,
				containLabel: true
			},
			xAxis: [
				{
					type: 'category',
					name: appChannelLang.common('businessChannel'), // 业务系统
					data: chartData.xAxisData,
					axisLine: {
						lineStyle: {
							color: '#666'
						}
					},
					axisTick: {
						show: false
					}
				}
			],
			yAxis: [
				{
					type: 'value',
					name: appChannelLang.common('amount'), // 调用量
					axisLine: {
						show: false,
						lineStyle: {
							color: '#666'
						}
					},
					axisTick: {
						show: false
					},
					splitArea: {
						areaStyle: {
							color: ['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0)']
						}
					},
					splitLine: {
						lineStyle: {
							type: 'solid'
						}
					}
				}
			],
			series: chartData.seriesData
		};
		if (option && typeof option === 'object') {
			myChart.setOption(option, true);
			window.addEventListener('resize', () => {
				myChart.resize();
			});
			if (type) {
				myChart.off('click');
				myChart.on('click', (params) => {
					const { list } = chartData;
					let channelCode = null;
					const obj = list.find(item => item.channelName === params.name);
					if (obj) channelCode = obj.channelCode;
					this.props.onClick(params.seriesName, channelCode);
				});
			}
		}
	}

	render() {
		const { defaultHeight } = this.state;
		const { height, idName } = this.props;
		const realHeight = height ? `${height}px` : `${defaultHeight}px`;

		return (
			<div id={idName} style={{ height: realHeight }}></div>
		);
	}
}
