/*
 * @Author: liu<PERSON>
 * @CreatDate: 2018-08-28 15:47:11
 * @Describe: 命中规则图表组件
 */

import { PureComponent } from 'react';
// 加载echarts，注意引入文件的路径
import Echarts from 'echarts/lib/echarts';
// 再引入你需要使用的图表类型，标题，提示信息等
import 'echarts/lib/chart/bar';
import 'echarts/lib/component/tooltip';

export default class HorizontalBar extends PureComponent {

	state = {
		defaultHeight: 300
	};
	constructor(props) {
		super(props);
		this.draw = this.draw.bind(this);
	}

	componentDidMount() {
		const { idName, chartData } = this.props;
		this.draw(idName, chartData);
	}

	componentDidUpdate(prevProps) {
		let preChartData = prevProps.chartData;
		let nextChartData = this.props.chartData;
		if (preChartData !== nextChartData) {
			// 检测到数据改变，重绘图表
			const { idName, chartData } = this.props;
			this.draw(idName, chartData);
		}
	}

	draw(idName, chartData) {
		if (!chartData) return;
		const dom = document.getElementById(idName);
		const myChart = Echarts.init(dom);
		const option = {
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					type: 'shadow'
				}
			},
			color: ['#7cb5ec'],
			grid: {
				left: 250,
				right: 20,
				bottom: 10,
				top: 10,
				containLabel: true
			},
			xAxis: {
				type: 'value',
				// name: "命中次数",
				axisLine: {
					lineStyle: {
						color: '#666'
					}
				},
				splitLine: {
					show: false
				},
				axisTick: {
					show: false
				}
			},
			yAxis: {
				type: 'category',
				// name: "规则名称",
				data: chartData.yAxisData ? chartData.yAxisData : [],
				axisLine: {
					lineStyle: {
						color: '#666'
					}
				},
				splitLine: {
					show: false
				},
				axisTick: {
					show: false
				},
				axisLabel: {
					show: false,
					color: '#666'
				}
			},
			series: [
				{
					// name: "命中次数",
					type: 'bar',
					barMaxWidth: '25px',
					itemStyle: {
						barBorderRadius: 5
					},
					data: chartData.seriesData ? chartData.seriesData : []
				}
			]
		};
		if (option && typeof option === 'object') {
			myChart.setOption(option, true);
			window.addEventListener('resize', () => {
				myChart.resize();
			});
		}
	}

	render() {
		const { defaultHeight } = this.state;
		const { height, idName } = this.props;
		const realHeight = height ? `${height}px` : `${defaultHeight}px`;

		return (
			<div id={idName} style={{ height: realHeight }}></div>
		);
	}
}
