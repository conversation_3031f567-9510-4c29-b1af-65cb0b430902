/*
 * @Author: liu<PERSON>
 * @CreatDate: 2018-08-28 15:47:11
 * @Describe: 柱状折线图表组件 - 采用echarts第三方库 - macarons主题
 */

import { PureComponent } from 'react';
// 加载echarts，注意引入文件的路径
import Echarts from 'echarts/lib/echarts';
// 再引入你需要使用的图表类型，标题，提示信息等
import 'echarts/lib/chart/line';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/tooltip';

import '../Theme/macarons';

import { dataServiceLang } from '@/constants/lang';

export default class LineBarChart extends PureComponent {

	state = {
		defaultHeight: 300
	};
	constructor(props) {
		super(props);
		this.draw = this.draw.bind(this);
	}

	componentDidMount() {
		const { idName, chartData } = this.props;
		this.draw(idName, chartData);
	}

	componentDidUpdate(prevProps) {
		let preChartData = prevProps.chartData;
		let nextChartData = this.props.chartData;
		if (preChartData !== nextChartData) {
			// 检测到数据改变，重绘图表
			const { idName, chartData } = this.props;
			this.draw(idName, chartData);
		}
	}

	draw(idName, chartData) {
		if (!chartData) return;
		const { type } = this.props;
		const dom = document.getElementById(idName);
		const myChart = Echarts.init(dom, 'macarons');
		const option = {
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					type: 'cross',
					crossStyle: {
						color: '#999'
					}
				},
				formatter: function(params) {
					let returnVal = `${params[0].axisValue}<br/>`;
					let list = '';
					params.forEach((item, i) => {
						let isPercent = '';
						const lv = item.seriesName.substr(item.seriesName.length - 1, item.seriesName.length - 1);
						const zb = item.seriesName.substr(item.seriesName.length - 2, item.seriesName.length - 1);
						if (item.seriesName.includes(dataServiceLang.common('rate'))) isPercent = '%';
						if (i === 0) {
							list = `${item.marker}${item.seriesName}：${item.value}${isPercent}`;
						} else {
							list = `${list}<br/>${item.marker}${item.seriesName}：${item.value}${isPercent}`;
						}
					});
					returnVal = returnVal + list;
					return returnVal;
				}
			},
			grid: {
				left: 10,
				right: 0,
				bottom: 40,
				top: 40,
				containLabel: true
			},
			color: ['#3ed19b', '#fc747a', '#fcbc5a', '#1baa8a', '#e82717', '#fccf5c'],
			legend: {
				data: chartData.legendData ? chartData.legendData : [],
				bottom: 0
			},
			xAxis: [
				{
					type: 'category',
					data: chartData.xAxisData ? chartData.xAxisData : [],
					axisPointer: {
						type: 'shadow'
					},
					axisLine: {
						lineStyle: {
							color: '#666'
						}
					},
					axisTick: {
						show: false
					}
				}
			],
			yAxis: [
				{
					type: 'value',
					name: dataServiceLang.common('amount'), // 调用量
					axisLabel: {
						formatter: '{value}'
					},
					axisLine: {
						show: false,
						lineStyle: {
							color: '#666'
						}
					},
					axisTick: {
						show: false
					},
					splitArea: {
						areaStyle: {
							color: ['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0)']
						}
					},
					splitLine: {
						lineStyle: {
							type: 'solid'
						}
					}
				},
				{
					type: 'value',
					name: dataServiceLang.common('ratio'), // 比率
					min: 0,
					max: 100,
					interval: 20,
					axisLabel: {
						formatter: '{value}'
					},
					axisLine: {
						show: false,
						lineStyle: {
							color: '#666'
						}
					},
					axisTick: {
						show: false
					},
					splitArea: {
						areaStyle: {
							color: ['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0)']
						}
					},
					splitLine: {
						lineStyle: {
							type: 'solid'
						}
					}
				}
			],
			series: chartData.seriesData ? chartData.seriesData : []
		};
		if (option && typeof option === 'object') {
			myChart.setOption(option, true);
			window.addEventListener('resize', () => {
				myChart.resize();
			});
			if (type) {
				myChart.off('click');
				myChart.on('click', (params) => {
					this.props.onClick(params.name);
				});
			}
		}
	}

	render() {
		const { defaultHeight } = this.state;
		const { height, idName } = this.props;
		const realHeight = height ? `${height}px` : `${defaultHeight}px`;

		return (
			<div id={idName} style={{ height: realHeight }}></div>
		);
	}
}
