import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { Select, Card, Row, Col, Icon, Radio, Tooltip, Spin, Button, message, Ellipsis, PageContainer } from 'tntd';
import service from './service';
import { dataServiceListAPI } from '@/services';
import { chartImages } from '@/constants/images';
import Pie<PERSON>hart from './PieChart';
import LineBarChart from './LineBarChart';
import LineBarChart2 from './LineBarChart2';
import LineBarChart3 from './LineBarChart3';
import TopTable from '../common/TopTable';
import RangeData from '../common/RangeData';
import CodePieChart from '../common/CodePieChart';
import LineBarTable from './LineBarTable';
import otp from './otp';

import './index.less';

const Option = Select.Option;

const timeMap = {
    '200': {
        minCost: 0,
        maxCost: 200
    },
    '500': {
        minCost: 200,
        maxCost: 500
    },
    '1000': {
        minCost: 500,
        maxCost: 1000
    },
    '5000': {
        minCost: 1000,
        maxCost: 5000
    },
    '5000d': {
        minCost: 5000,
        maxCost: ''
    },
    all: {
        minCost: undefined,
        maxCost: undefined
    }
};

class ConsumerData extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            filter: {},
            threeServiceList: [],
            subfilter: {
                queryType: 1,
                averageTime: 'all',
                topSize: 10,
                minCost: undefined,
                maxCost: undefined
            },
            loading: false
        };
    }

    async componentDidMount() {
        const { dispatch, globalStore } = this.props;
        const appName = globalStore.currentApp.name || 'all';
        const orgCode = globalStore.currentOrgCode;
        dispatch({
            type: 'consumer/getWholeView',
            payload: {
                appCode: appName,
                orgCode
            }
        });
        const { filter } = this.state;
        await this.setState({
            filter: {
                ...filter,
                appCode: appName,
                orgCode
            }
        });
        this.getServiceList(appName, orgCode);
    }

    getServiceList = (currentApp, currentOrgCode) => {
        service
            .interfaceList({
                curPage: 1,
                pageSize: 1000,
                appCode: currentApp,
                orgCode: currentOrgCode
            })
            .then((res) => {
                if (res && res.success) {
                    if (!res.data) return;
                    this.setState({
                        threeServiceList: res.data.contents ? res.data.contents : []
                    });
                } else {
                    message.error(res?.msg);
                }
            });
    };

    filterDetail = (params = {}) => {
        const { dispatch } = this.props;
        let { filter, subfilter, startDate, endDate } = this.state;
        filter.startDate = filter.startDate || startDate;
        filter.endDate = filter.endDate || endDate;
        dispatch({
            type: 'consumer/getInvokeDetailStat',
            payload: {
                ...filter,
                invokeType: 1,
                ...params
            }
        });
        dispatch({
            type: 'consumer/getInvokeStatistic',
            payload: {
                ...filter,
                ...subfilter,
                ...params
            }
        });
        dispatch({
            type: 'consumer/getInvokeTime',
            payload: {
                ...filter,
                ...subfilter,
                ...params
            }
        });
        dispatch({
            type: 'consumer/getInvokeRankStat',
            payload: {
                invokeType: 1,
                ...filter,
                ...params
            }
        });
        dispatch({
            type: 'consumer/getReasonCodeStatistic',
            payload: {
                invokeType: 1,
                ...filter,
                ...params
            }
        });
        dispatch({
            type: 'consumer/getWholeView',
            payload: {
                ...filter,
                ...subfilter,
                ...params
            }
        });
    };

    async componentDidUpdate(preProps) {
        const preLang = preProps.globalStore.personalMode.lang;
        const nextLang = this.props.globalStore.personalMode.lang;
        const appName = preProps.globalStore.currentApp.name;
        const nextAppName = this.props.globalStore.currentApp.name;
        if (preLang !== nextLang || appName !== nextAppName) {
            const { filter } = this.state;
            await this.setState({
                filter: {
                    ...filter,
                    appCode: nextAppName
                }
            });
            const { dispatch } = this.props;
            dispatch({
                type: 'consumer/getWholeView',
                payload: {
                    apCode: nextAppName
                }
            });
            this.filterDetail({ appName: nextAppName });
        }
    }

    handleChange = async (val) => {
        const { filter } = this.state;
        const startTime = moment(val.startTime).format('YYYY/MM/DD');
        const endTime = moment(val.endTime).format('YYYY/MM/DD');
        const startDate = Number(new Date(`${startTime} 00:00:00`));
        const endDate = Number(new Date(`${endTime} 23:59:59`));
        await this.setState({
            filter: {
                ...filter,
                startDate,
                endDate
            }
        });
        this.setState({
            startDate,
            endDate
        });
        await this.filterDetail({
            ...filter,
            startDate,
            endDate
        });
    };

    handleSelectChange = (val) => {
        const { filter } = this.state;
        this.filterDetail({
            ...filter,
            serviceCode: val
        });
        this.setState({
            filter: {
                ...filter,
                serviceCode: val
            }
        });
    };

    handleSelectInvokeFilterChange = (val, key) => {
        const { dispatch } = this.props;
        const { filter, subfilter } = this.state;
        this.setState({
            subfilter: {
                ...subfilter,
                [key]: val
            }
        });
        dispatch({
            type: 'consumer/getInvokeStatistic',
            payload: {
                ...filter,
                ...subfilter,
                [key]: val
            }
        });
        dispatch({
            type: 'consumer/getInvokeTime',
            payload: {
                ...filter,
                ...subfilter,
                [key]: val
            }
        });
        dispatch({
            type: 'consumer/getWholeView',
            payload: {
                ...filter,
                ...subfilter,
                [key]: val
            }
        });
    };

    handleRadioButtonChange = (val) => {
        const value = val.target.value;
        const { dispatch } = this.props;
        const { filter, subfilter } = this.state;

        dispatch({
            type: 'consumer/getFilterInvokeStatistic',
            payload: {
                ...filter,
                ...subfilter,
                ...timeMap[value]
            }
        });

        this.setState({
            subfilter: {
                ...subfilter,
                ...timeMap[value],
                averageTime: value
            }
        });
    };

    // 点击跳转 - 调用方明细
    handleClick = (errorCode) => {
        const { filter } = this.state;
        const { startDate, endDate, serviceCode } = filter;
        const { history } = this.props;
        let path = '/handle/dataManagement/businessChannel';

        window.open(
            `${path}?state=${window.encodeURIComponent(
                JSON.stringify({
                    date: [startDate, endDate],
                    serviceCode,
                    errorCode,
                    returnResult: 'fail'
                })
            )}`,
            '_blank'
        );
    };
    // 点击跳转 - 调用方明细
    goToPage = (invokeSource) => {
        const { filter } = this.state;
        const { startDate, endDate, serviceCode } = filter;
        const { history } = this.props;
        let path = '/handle/dataManagement/businessChannel';
        history.push(path, {
            date: [startDate, endDate],
            serviceCode,
            invokeSource
        });
    };

    // 手动统计
    manual = () => {
        this.setState({ loading: true });
        service
            .statistical()
            .then((res) => {
                if (res && res.success) {
                    setTimeout(() => {
                        const { filter } = this.state;
                        this.filterDetail({
                            ...filter
                        });
                        this.setState({ loading: false });
                    }, 3000);
                } else {
                    message.error(res.msg);
                    this.setState({ loading: false });
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    render() {
        const { store } = this.props;
        const { subfilter, loading, threeServiceList } = this.state;
        const {
            overview,
            detail,
            averageTime,
            invokeStatus,
            sourceType,
            invokeRank,
            codeData,
            overviewLoading,
            invokeStatusLoading,
            averageTimeLoading,
            invokeRankLoading,
            codeDataLoading
        } = store;
        const { changeNum, total, serviceRanks = [], channelRanks = [] } = overview;
        const {
            successCount = 0,
            successRate = 0,
            failCount = 0,
            succAvgCost = 0,
            failAvgCost = 0,
            cacheCount = 0,
            cacheRate = 0,
            statTime = 0,
            failRate = 0
        } = detail;

        const successR = (successRate * 100).toFixed(2);
        const cacheR = (cacheRate * 100).toFixed(2);
        const failR = (failRate * 100).toFixed(2);

        return (
            <div className="p-consumer-data">
                {/* <div className="page-global-header">
                    <div className="left-info">
                        <h2> */}
                {/* 调用方大盘 */}
                {/* {I18N.consumerdata.index.diaoYongFangDaPan} */}
                {/* </h2>
                    </div>
                </div> */}
                <div className="page-global-body">
                    <div className="realtime section">
                        <h2 className="title">
                            {/* 实时概览 */}
                            {I18N.consumerdata.index.shiShiGaiLan}
                        </h2>
                        <div className="container">
                            <div
                                className="header"
                                style={{
                                    backgroundImage: `url(${chartImages.header})`
                                }}>
                                <div className="profile">
                                    {/* 调用方服务总数 */}
                                    {I18N.consumerdata.index.fuWuJieKouZong}
                                    <span className="u-num">{total}</span>
                                    <span className="u-sub">
                                        {/* 个 */}
                                        {I18N.consumerdata.index.ge === 'a' ? null : I18N.consumerdata.index.ge}
                                    </span>
                                    <i className="line" />
                                    {/* 环比昨天 */}
                                    {I18N.consumerdata.index.huanBiZuoTian}
                                    {changeNum >= 0 && <img src={chartImages.arrowUp} className="u-img" />}
                                    {changeNum < 0 && <img src={chartImages.arrowDown} className="u-img" />}
                                    <span className="u-num">{changeNum && Math.abs(changeNum)}</span>
                                    <span className="u-sub">
                                        {/* 个 */}
                                        {I18N.consumerdata.index.ge === 'a' ? null : I18N.consumerdata.index.ge}
                                    </span>
                                </div>
                                <div
                                    className="tag"
                                    style={{
                                        backgroundImage: `url(${chartImages.tag})`
                                    }}>
                                    {/* 生效中 */}
                                    {I18N.consumerdata.index.shengXiaoZhong}
                                </div>
                            </div>
                            <Row>
                                <Col span={14} className="borderR">
                                    <h2 className="title">
                                        {/* 数据源服务接口分布 */}
                                        {I18N.consumerdata.index.fuWuJieKouTiao}
                                    </h2>
                                    <Spin spinning={overviewLoading}>
                                        <LineBarTable serviceRanks={serviceRanks} />
                                        {/* <LineBarChart idName="serviceRanks" chartData={serviceRanks} /> */}
                                    </Spin>
                                </Col>
                                <Col span={10}>
                                    <h2 className="title">{I18N.consumerdata.index.yingYongFenBu}</h2>
                                    <Spin spinning={overviewLoading}>
                                        <PieChart idName="channelRanks" chartData={channelRanks} />
                                    </Spin>
                                </Col>
                            </Row>
                        </div>
                    </div>
                    <div className="section">
                        <h2 className="u-title">
                            {/* 调用统计 */}
                            {I18N.consumerdata.index.diaoYongTongJi}
                            {/* 统计结果有5~10分钟左右的延迟 */}
                            <Tooltip title={I18N.consumerdata.index.tongJiJieGuoYou}>
                                <Icon type="question-circle" style={{ marginLeft: '2px' }} />
                            </Tooltip>
                            <span className="u-time">
                                {/* 上一次统计时间 */}
                                {I18N.consumerdata.index.shangYiCiTongJi}：
                                {statTime ? moment(statTime).format('MM-DD HH:mm:ss') : I18N.consumerdata.index.wu}
                            </span>
                            <Button size="small" onClick={this.manual} loading={loading}>
                                {/* 手动统计 */}
                                {I18N.consumerdata.index.shouDongTongJi}
                            </Button>
                        </h2>
                        <div className="header2">
                            <div style={{ position: 'relative' }} id="property-select1" className="filter-search">
                                <RangeData className="rangedata" handleChange={this.handleChange} />
                                <Select
                                    showSearch
                                    allowClear
                                    getPopupContainer={() => document.getElementById('property-select1')}
                                    style={{ width: otp.selectWidth1 }}
                                    placeholder={I18N.addmodifymodal.index.fuWuJieKou} // 请选择数据源服务名称
                                    optionFilterProp="children"
                                    filterOption={(input, option) => {
                                        return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                                    }}
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    dropdownAlign={{
                                        points: ['tr', 'br'], // 将下拉菜单的右上角对齐到选择器的右下角
                                        offset: [0, 4], // 可以根据需要调整对齐的偏移量
                                        overflow: {
                                            adjustX: true, // 如果菜单超出右边界，自动调整位置
                                            adjustY: true // 如果菜单超出下边界，自动调整位置
                                        }
                                    }}
                                    onChange={this.handleSelectChange}>
                                    {threeServiceList &&
                                        threeServiceList.map((item, index) => {
                                            return (
                                                <Option value={item.name} key={index} title={item.displayName}>
                                                    <Ellipsis title={item.displayName} />
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </div>
                        </div>
                        <div className="filter-results">
                            <div className="item">
                                <p className="title">
                                    {/* 调用总量/次 */}
                                    {I18N.consumerdata.index.diaoYongZongLiangCi}
                                </p>
                                <p className="percent">{detail.total || 0}</p>
                                <p className="pic">
                                    <img src={chartImages.heart1} />
                                </p>
                            </div>
                            <div className="item">
                                <p className="title">
                                    {/* 成功率 */}
                                    {I18N.consumerdata.index.chengGongLu}
                                    {/* 总成功率，包含本地和数据源 */}
                                    <Tooltip title={I18N.consumerdata.index.zongChengGongLuBao}>
                                        <Icon type="question-circle" style={{ marginLeft: '2px' }} />
                                    </Tooltip>
                                </p>
                                <p className="percent">{successR}%</p>
                                <p className="num">
                                    {/* 成功量  */}
                                    {I18N.consumerdata.index.chengGongLiang}
                                    {successCount}
                                </p>
                            </div>
                            <div className="item">
                                <p className="title">
                                    {/* 失败率 */}
                                    {I18N.consumerdata.index.shiBaiLu}
                                </p>
                                <p className="percent">{failR}%</p>
                                <p className="num">
                                    {/* 失败量  */}
                                    {I18N.consumerdata.index.shiBaiLiang}
                                    {failCount}
                                </p>
                            </div>
                            <div className="item">
                                <p className="title">
                                    {/* 成功平均耗时 */}
                                    {I18N.consumerdata.index.chengGongPingJunHao}/ms
                                </p>
                                <p className="percent">{succAvgCost || 0}</p>
                                <p className="pic">
                                    <img src={chartImages.heart2} />
                                </p>
                            </div>
                            <div className="item">
                                <p className="title">
                                    {/* 失败平均耗时 */}
                                    {I18N.consumerdata.index.shiBaiPingJunHao}/ms
                                </p>
                                <p className="percent">{failAvgCost || 0}</p>
                                <p className="pic">
                                    <img src={chartImages.heart3} />
                                </p>
                            </div>
                        </div>
                        <Card
                            style={{ position: 'relative' }}
                            id="property-select2"
                            className="card-container"
                            title={
                                <div className="card-filter">
                                    <div>
                                        {/* 调用状态和耗时统计 */}
                                        {I18N.consumerdata.index.diaoYongZhuangTaiHe}
                                    </div>
                                    <div>
                                        <Select
                                            showSearch
                                            style={{ width: otp.selectWidth2 }}
                                            defaultValue={subfilter.queryType}
                                            getPopupContainer={() => document.getElementById('property-select2')}
                                            optionFilterProp="children"
                                            dropdownMatchSelectWidth={false}
                                            onChange={(val) => {
                                                this.handleSelectInvokeFilterChange(val, 'queryType');
                                            }}>
                                            <Option value={1}>{I18N.consumerdata.index.anYingYongTongJi}</Option>
                                            <Option value={2}>
                                                {/* 按机构统计 */}
                                                {I18N.consumerdata.index.anJiGouTongJi}
                                            </Option>
                                        </Select>
                                        <Select
                                            showSearch
                                            style={{ width: otp.selectWidth3 }}
                                            defaultValue={subfilter.topSize}
                                            getPopupContainer={() => document.getElementById('property-select2')}
                                            optionFilterProp="children"
                                            dropdownMatchSelectWidth={false}
                                            onChange={(val) => {
                                                this.handleSelectInvokeFilterChange(val, 'topSize');
                                            }}>
                                            <Option value={10}>{I18N.consumerdata.index.xianShiDiaoYongLiang}Top10</Option>
                                            <Option value={20}>{I18N.consumerdata.index.xianShiDiaoYongLiang}Top20</Option>
                                            <Option value={30}>{I18N.consumerdata.index.xianShiDiaoYongLiang}Top30</Option>
                                            <Option value={''}>
                                                {/* 显示全部 */}
                                                {I18N.consumerdata.index.xianShiQuanBu}
                                            </Option>
                                        </Select>
                                    </div>
                                </div>
                            }>
                            <div className="pd16">
                                <h2 className="title">
                                    {/* 调用状态 */}
                                    {I18N.consumerdata.index.diaoYongZhuangTai}
                                </h2>
                                <Spin spinning={invokeStatusLoading}>
                                    <LineBarChart2 idName="invokeStatus" chartData={invokeStatus} />
                                </Spin>
                            </div>

                            <div className="divider-line" />
                            <div className="pd16">
                                <div className="average-time">
                                    <h2 className="title">
                                        {/* 平均耗时 */}
                                        {I18N.consumerdata.index.pingJunHaoShi}
                                    </h2>
                                    <div>
                                        <Radio.Group
                                            onChange={this.handleRadioButtonChange}
                                            defaultValue={subfilter.averageTime}
                                            value={subfilter.averageTime}>
                                            <Radio.Button value="200">{I18N.consumerdata.index.xiaoYu}200ms</Radio.Button>
                                            <Radio.Button value="500">200ms-500ms</Radio.Button>
                                            <Radio.Button value="1000">500ms-1s</Radio.Button>
                                            <Radio.Button value="5000">1s-5s</Radio.Button>
                                            <Radio.Button value="5000d">{I18N.consumerdata.index.daYu}5s</Radio.Button>
                                            <Radio.Button value="all">{I18N.consumerdata.index.quanBu}</Radio.Button>
                                        </Radio.Group>
                                    </div>
                                </div>
                                <Spin spinning={averageTimeLoading}>
                                    <LineBarChart3 idName="averageTime" chartData={averageTime} />
                                </Spin>
                            </div>
                            <div className="bottom-chart">
                                <Row gutter={16}>
                                    <Col span={12}>
                                        {/* 调用方服务调用排行 */}
                                        <Card title={I18N.providerdata.index.shuJuYuanFuWu}>
                                            <Spin spinning={invokeRankLoading}>
                                                <TopTable invokeRank={invokeRank} widthR={160} />
                                            </Spin>
                                        </Card>
                                    </Col>
                                    {/* <Col span={8}>
										<Card title={"调用来源类型分布"} hoverable >
											<PieChart2
												idName="sourceType"
												chartData={sourceType}
												onClick={this.goToPage}
											/>
										</Card>
									</Col> */}
                                    <Col span={12}>
                                        {/* 异常状态码分布 */}
                                        <Card title={I18N.consumerdata.index.yiChangZhuangTaiMa}>
                                            <Spin spinning={codeDataLoading}>
                                                <CodePieChart
                                                    idName="codeStatus"
                                                    chartData={codeData}
                                                    onClick={this.handleClick}
                                                    type="click"
                                                />
                                            </Spin>
                                        </Card>
                                    </Col>
                                </Row>
                            </div>
                        </Card>
                    </div>
                </div>
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    store: state.consumer
}))(PageContainer(ConsumerData));
