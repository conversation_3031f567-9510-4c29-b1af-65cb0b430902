import { getUrl, deleteEmptyObjItem } from '@/utils/common';
import request from '@/utils/request';

// 实时概览
const getWholeView = async (params) => {
    return request(
        getUrl('/bridgeApi/statistics/channel/getWholeView', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 调用明细统计
const getInvokeDetailStat = async (params) => {
    return request(
        getUrl('/bridgeApi/statistics/invoke/cost', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 调用状态
const getInvokeStatistic = async (params) => {
    return request(
        getUrl('/bridgeApi/statistics/invoke/cost/channel/status', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 耗时明细
const getInvokeTime = async (params) => {
    return request(
        getUrl('/bridgeApi/statistics/invoke/cost/channel/avg', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};
// 服务调用排行榜
const getInvokeRankStat = async (params) => {
    return request(
        getUrl('/bridgeApi/statistics/datasource/rank', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 调用来源类型分布明细
const getInvokeSourceTypeStat = async (params) => {
    return request(getUrl('/statistics/channel/getInvokeSourceTypeStat', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

// 错误码统计
const getReasonCodeStatistic = async (params) => {
    return request(
        getUrl('/bridgeApi/statistics/reason/code', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 手动统计
const statistical = async () => {
    const params = {
        type: 2
    };
    return request('/statistics/es/statics', {
        method: 'POST',
        body: deleteEmptyObjItem(params)
    });
};

const interfaceList = async (params) => {
    return request(
        getUrl('/bridgeApi/service/interface/list', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};
export default {
    getWholeView,
    getInvokeStatistic,
    getInvokeRankStat,
    getReasonCodeStatistic,
    getInvokeSourceTypeStat,
    getInvokeDetailStat,
    statistical,
    getInvokeTime,
    interfaceList
};
