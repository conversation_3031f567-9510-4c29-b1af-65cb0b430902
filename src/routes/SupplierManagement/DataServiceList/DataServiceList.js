/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 数据服务列表
 */

import I18N from '@/utils/I18N';
import React, { Suspense, Fragment, useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { connect } from 'dva';
import {
    Button,
    Table,
    Pagination,
    message,
    Dropdown,
    Menu,
    Switch,
    Modal,
    Tooltip,
    Icon,
    Badge,
    HandleIcon,
    QueryForm,
    TableContainer
} from 'tntd';
import FilterPanel from './components/FilterPanel';
import { dataServiceListAPI, referenceAPI } from '@/services';
import { ReferenceDrawer, ReferenceCheck, ReferenceOnlineCheck } from '@tddc/reference';
import { checkFunctionHasPermission } from '@/utils/permission';
import NoPermission from '@/components/NoPermission';
import { sortableColumnTitleRenderer } from '@/utils/sortableColumnTitleRenderer';
import { getUrlKey } from '@/utils/utils';
import resetStoreListener from '@/resetStoreListener';
import ImportModal from './Inner/ImportModal';
import Ellipsis from '@/components/TablePage/components/Ellipsis';
import ParamModel from './Inner/ParamsModel';
import './index.less';
import { getDataTypeColor } from './constant';

import { debounce, find } from 'lodash';
const TestModal = React.lazy(() => import('./Inner/TestModal'));
const CopyModal = React.lazy(() => import('./Inner/CopyModal'));

const { confirm } = Modal;
const { Field } = QueryForm;

const DataServiceList = (props) => {
    const { globalStore, dispatch, history } = props;
    const { currentApp } = globalStore;
    const queryFormRef = useRef();

    const [testVisible, setTestVisible] = useState(false);
    const [testId, setTestId] = useState(null);
    const [threeServiceList2, setThreeServiceList2] = useState([]);
    const [testTitle, setTestTitle] = useState(null);
    const [testName, setTestName] = useState('');
    const [testMockFlag, setTestMockFlag] = useState(null);
    const [copyVisible, setCopyVisible] = useState(false);
    const [record, setRecord] = useState(null);
    const [importVisible, setImportVisible] = useState(false);
    const [referenceDrawerData, setReferenceDrawerData] = useState(null);
    const [parameterDrawerData, setParameterDrawerData] = useState(null);
    const [dropdownVisibleRecord, setDropdownVisibleRecord] = useState(null);

    // 过滤面板相关状态
    const [filterPanelVisible, setFilterPanelVisible] = useState(false);

    const { dataServiceListStore } = props;
    const { allMap, providerList, menuTreeReady, appList } = globalStore;
    const { tableList, total, searchParams, loading, structureList, isStructureVisible } = dataServiceListStore;

    const sDisplayName = getUrlKey('displayName');
    const sName = getUrlKey('name');
    const sStatus = getUrlKey('status');

    // useEffect(() => {
    //     const { dispatch } = props;
    //     dispatch({
    //         type: 'dataServiceList/getDatalandAble',
    //         payload: {}
    //     });
    // }, []);
    const isFindFlag = useCallback(
        (uuid) => {
            if (!uuid) return false;
            const isFind = structureList?.filter((item) => item?.dataSourceUuid === uuid);
            if (isFind?.length) {
                return true;
            }
            return false;
        },
        [structureList]
    );

    useEffect(() => {
        if (window.lightBoxActions && !allMap?.isIntegrationTG) {
            window.lightBoxActions.setOrgAppListVisible(true); // 机构下的应用可见
        }
    }, []);

    useEffect(() => {
        if (sDisplayName || sName || sStatus) {
            dispatch({
                type: 'dataServiceList/setAttrValue',
                payload: {
                    searchParams: {
                        displayName: sDisplayName,
                        name: sName,
                        status: sStatus
                    }
                }
            });
            queryFormRef.current.form?.setValues?.({
                ...(queryFormRef.current.form.getValues() || {}),
                displayName: sDisplayName || undefined,
                status: sStatus || undefined,
                name: sName || undefined
            });
        }

        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                searchParams: {
                    displayName: sDisplayName,
                    name: sName,
                    status: sStatus
                }
            }
        });
        if (checkFunctionHasPermission('TZ0202', 'query')) {
            const { dataServiceListStore } = props;
            const { curPage, pageSize } = dataServiceListStore.searchParams;
            search(curPage, pageSize);
            getThreeServiceList();
        }
    }, [currentApp.name]);

    message.config({
        maxCount: 1
    });

    // 查询
    const search = (curPage, pageSize, noLoading) => {
        const { dataServiceListStore } = props;
        const { searchParams } = dataServiceListStore;
        dispatch({
            type: 'dataServiceList/getList',
            payload: {
                curPage: curPage ? curPage : 1,
                pageSize: pageSize ? pageSize : searchParams.pageSize,
                sortField: searchParams.sortField,
                sortRule: searchParams.sortRule,
                noLoading
            }
        });
    };

    // 分页
    const paginationOnChange = (curPage, pageSize) => {
        search(curPage, pageSize);
    };

    // 新增下拉菜单
    const addMenu = () => {
        return (
            <Menu>
                <Menu.Item>
                    <a onClick={() => add('SYNC')}>{I18N.dataservicelist.tongBuJieKou}</a>
                </Menu.Item>
                <Menu.Item>
                    <a onClick={() => add('ASYNC')}>{I18N.dataservicelist.yiBuJieKou}</a>
                </Menu.Item>
            </Menu>
        );
    };

    // 新增
    const add = (dataSourceType = 'SYNC') => {
        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                modalType: 1
            }
        });
        let path = '';
        if (dataSourceType === 'SYNC') {
            path = '/handle/supplierManagement/dataServiceList/addModify/sync';
        } else {
            path = '/handle/supplierManagement/dataServiceList/addModify';
        }
        props.history.push(`${path}?modalType=1&dataSourceType=${dataSourceType}`);
    };

    // 修改
    const modify = (record) => {
        const { uuid, dataSourceType = 'SYNC' } = record;
        setDropdownVisibleRecord(null); // 关闭 Dropdown
        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                modalType: 2
            }
        });
        let path = '';
        if (dataSourceType === 'SYNC') {
            path = '/handle/supplierManagement/dataServiceList/addModify/sync';
        } else {
            path = '/handle/supplierManagement/dataServiceList/addModify';
        }
        history.push(`${path}?modalType=2&uuid=${uuid}&dataSourceType=${dataSourceType}`);
    };

    // 查看
    const look = (record) => {
        const { uuid, dataSourceType = 'SYNC' } = record;
        setDropdownVisibleRecord(null); // 关闭 Dropdown
        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                modalType: 3
            }
        });
        let path = '/handle/supplierManagement/dataServiceList/detail';
        history.push(`${path}?modalType=3&uuid=${uuid}&dataSourceType=${dataSourceType}`);
    };

    // 复制
    const copy = (record) => {
        setCopyVisible(true);
        setRecord(record);
        setDropdownVisibleRecord(null); // 关闭 Dropdown
    };

    // 导出
    const exportData = (record) => {
        const { displayName } = record;
        const params = {
            uuid: record.uuid
        };
        dataServiceListAPI.exportData(params, displayName, 'xlsx');
    };

    const importData = () => {
        updateImportVisible(true);
    };

    // 上下线切换
    const upDownLine = (e, record) => {
        const { account } = globalStore.currentUser;
        const params = {
            uuid: record.uuid,
            status: e ? 1 : 2,
            operator: account
        };

        //上下线校验
        if (params.status === 1) {
            ReferenceOnlineCheck({
                rq: () => {
                    return referenceAPI.onlineValidate({
                        componentType: 'DATASOURCE_SERVICE',
                        componentIds: record.name
                    });
                }
            }).then(() => {
                dataServiceListAPI.setOnline(params).then((res) => {
                    if (res && res.success) {
                        message.success(I18N.dataservicelist.caoZuoChengGong);
                        setDropdownVisibleRecord(null);
                        const { dataServiceListStore } = props;
                        const { searchParams } = dataServiceListStore;
                        const { curPage, pageSize } = searchParams;
                        search(curPage, pageSize, 'noLoading');
                    } else {
                        message.error(res.message);
                    }
                });
            });
        } else {
            ReferenceCheck({
                rq: () => {
                    return referenceAPI.checkComponentReference({
                        componentType: 'DATASOURCE_SERVICE',
                        componentId: record.name
                    });
                }
            }).then(() => {
                dataServiceListAPI.setOnline(params).then((res) => {
                    if (res && res.success) {
                        message.success(I18N.dataservicelist.caoZuoChengGong);
                        setDropdownVisibleRecord(null);
                        const { dataServiceListStore } = props;
                        const { searchParams } = dataServiceListStore;
                        const { curPage, pageSize } = searchParams;
                        search(curPage, pageSize, 'noLoading');
                    } else {
                        message.error(res.message);
                    }
                });
            });
        }
    };

    // 删除
    const deleteData = (record) => {
        setDropdownVisibleRecord(null); // 关闭 Dropdown
        confirm({
            title: I18N.dataservicelist.queDingShanChuCi, // 确定删除此数据?
            onOk: () => {
                ReferenceCheck({
                    strongMsg: I18N.dataservicelist.cunZaiQiangYinYong,
                    rq: () =>
                        referenceAPI.checkComponentReference({
                            componentType: 'DATASOURCE_SERVICE',
                            componentId: record.name
                        })
                }).then(() => {
                    dataServiceListAPI.deleteData({ uuid: record.uuid }).then((res) => {
                        if (res && res.success) {
                            const { dataServiceListStore } = props;
                            const { searchParams, tableList } = dataServiceListStore;
                            let { curPage, pageSize } = searchParams;
                            if (tableList.length === 1 && curPage > 1) {
                                curPage = curPage - 1;
                            }
                            search(curPage, pageSize);
                            getThreeServiceList();
                        } else {
                            message.error(res.message);
                        }
                    });
                });
            }
        });
    };

    // 测试
    const test = (record) => {
        setTestVisible(true);
        setTestId(record.uuid);
        setTestTitle(record.displayName);
        setTestName(record.name);
        setTestMockFlag(record.mockFlag);
        setDropdownVisibleRecord(null); // 关闭 Dropdown
    };

    const updateImportVisible = (visible) => {
        setImportVisible(visible);
    };

    // 关闭弹框
    const handleCancel = () => {
        setTestVisible(false);
        setTestId(null);
        setTestTitle(null);
        setTestName(null);
        setCopyVisible(false);
        setRecord(null);
        setTestMockFlag(null);
    };

    // 防抖查询
    const debouncedSearch = useCallback(
        debounce(() => search(), 300),
        []
    );

    // 改变参数
    const changeField = async (obj) => {
        const newData = {
            curPage: 1,
            pageSize: 9, // 修改为9
            ...obj
        };
        await dispatch({
            type: 'dataServiceList/updateSearchParams',
            payload: newData
        });

        debouncedSearch();
    };

    const getThreeServiceList = () => {
        dataServiceListAPI.getListAll2().then((res) => {
            if (!res) return;
            if (res && !res.success) return message.error(res.message);
            if (!res.data) return;
            let data = [];
            // 过滤掉没有表示的数据
            res.data.contents.forEach((item) => {
                if (item.name) {
                    data.push(item);
                }
            });
            setThreeServiceList2(data || []);
        });
    };

    const handleMock = (record) => {
        const { history } = props;
        setDropdownVisibleRecord(null); // 关闭 Dropdown
        history.push(`/handle/supplierManagement/dataServiceList/mockConfig?uuid=${record.uuid}`);
    };

    const handleTableSort = (pagination, filters, sorter) => {
        const { dataServiceListStore } = props;
        const { searchParams } = dataServiceListStore;
        if (sorter && sorter.order) {
            dispatch({
                type: 'dataServiceList/getList',
                payload: {
                    curPage: 1,
                    pageSize: searchParams.pageSize,
                    sortField: sorter.field,
                    sortRule: sorter.order === 'ascend' ? 'asc' : 'desc'
                }
            });
        } else {
            dispatch({
                type: 'dataServiceList/getList',
                payload: {
                    curPage: 1,
                    pageSize: searchParams.pageSize,
                    sortField: undefined,
                    sortRule: undefined
                }
            });
        }
    };

    const copyModalOk = () => {
        handleCancel();
        search();
        getThreeServiceList();
    };

    const exportRecord = (record) => {
        dataServiceListAPI.exportRecord({ uuid: record.uuid }, record.name, 'tar', I18N.dataservicelist.daoChuShiBai);
    };

    const { partnerId, displayName, dataType, dataSourceType, status, name } = searchParams;

    // 缓存维度
    const cacheOptions = [
        {
            label: I18N.addmodify.cachetime.dangtian,
            value: 'SAME_DAY'
        },
        {
            label: I18N.addmodify.cachetime.shi,
            value: 'HOURS',
            placeholder: I18N.addmodify.cachetime.qingshuruxiaoshi
        },
        {
            label: I18N.addmodify.cachetime.tian,
            value: 'DAYS',
            placeholder: I18N.addmodify.cachetime.qingshurutianshu
        }
    ];

    // 构建操作菜单
    const getOperationMenu = (record) => {
        let flag = false;
        if (
            !checkFunctionHasPermission('TZ0202', 'copy') &&
            !checkFunctionHasPermission('TZ0202', 'export') &&
            !checkFunctionHasPermission('TZ0202', 'delete')
        ) {
            flag = true;
        }
        return (
            <Menu className="operation-dropdown-menu">
                {/* 顶部接口状态切换 */}
                <Menu.Item key="status-switch" disabled style={{ paddingBottom: 8 }}>
                    <div style={{ width: 100, color: '#17233D', marginRight: 8 }}>{I18N.dataservicelist.jieKouZhuangTai}</div>
                    <Switch
                        size="small"
                        checked={record.status === 1}
                        text={record.status === 1 ? I18N.dataservicelist.shangXian : I18N.dataservicelist.xiaXian}
                        onChange={(e) => {
                            if (!checkFunctionHasPermission('TZ0202', 'online')) {
                                return message.info(I18N.dataservicelist.zanWuQuanXian);
                            }
                            upDownLine(e, record);
                        }}
                        style={{ verticalAlign: 'middle' }}
                    />
                </Menu.Item>
                <Menu.Divider />
                {checkFunctionHasPermission('TZ0202', 'look') && (
                    <Menu.Item onClick={() => look(record)}>
                        <span>{I18N.dataservicelist.chaKan}</span>
                    </Menu.Item>
                )}
                {!flag && checkFunctionHasPermission('TZ0202', 'copy') && (
                    <Menu.Item onClick={() => copy(record)}>
                        <span>{I18N.dataservicelist.fuZhi}</span>
                    </Menu.Item>
                )}
                {!flag && (
                    <Menu.Item onClick={() => exportRecord(record)}>
                        <span>{I18N.dataservicelist.daoChu}</span>
                    </Menu.Item>
                )}
                {!flag && (
                    <Menu.Item
                        onClick={() => {
                            setReferenceDrawerData(record);
                            setDropdownVisibleRecord(null); // 关闭 Dropdown
                        }}>
                        <span>{I18N.dataservicelist.yinYongGuanXi}</span>
                    </Menu.Item>
                )}
                {isStructureVisible && (
                    <Menu.Item
                        disabled={!!isFindFlag(record?.uuid)}
                        onClick={() => !isFindFlag(record?.uuid) && setParameterDrawerData(record)}>
                        <span>{I18N.dataservicelist.jieGouHuaPeiZhi}</span>
                    </Menu.Item>
                )}
                {isStructureVisible && (
                    <Menu.Item
                        disabled={!isFindFlag(record?.uuid)}
                        onClick={() => {
                            if (isFindFlag(record?.uuid)) {
                                setDropdownVisibleRecord(null); // 关闭 Dropdown
                                props.history.push(`/handle/supplierManagement/dataServiceList/parameterList?uuid=${record?.uuid}`);
                            }
                        }}>
                        <span>{I18N.components.list.baoWenJieGou}</span>
                    </Menu.Item>
                )}
                {record.status != 1 && !flag && checkFunctionHasPermission('TZ0202', 'delete') && (
                    <Menu.Item onClick={() => deleteData(record)}>
                        <span>{I18N.dataservicelist.shanChu}</span>
                    </Menu.Item>
                )}
            </Menu>
        );
    };

    // 构建卡片视图的每个卡片
    const renderServiceCard = (record) => {
        const avatarText = record?.displayName?.slice(0, 2) || '- -';

        // 检查权限并返回禁用状态
        const checkPermission = (permission) => {
            return !checkFunctionHasPermission('TZ0202', permission);
        };

        return (
            <div className="service-card" key={record.uuid}>
                <div className="card-header">
                    <div className="avatar-container" style={{ backgroundColor: getDataTypeColor(record?.dataType) }}>
                        {avatarText}
                    </div>
                    <div className="header-content">
                        <div className="title-row">
                            <div className="service-name">
                                <Ellipsis placement="top" widthLimit={220} text={record.displayName || '- -'} />
                            </div>
                            <div className="service-status">
                                {record.status === 1 ? (
                                    <Badge color="green" text={I18N.dataservicelist.yiShangXian} />
                                ) : (
                                    <Badge color="red" text={I18N.dataservicelist.xiaXian} />
                                )}
                            </div>
                        </div>
                        <div className="tag-row">
                            {!allMap?.isIntegrationTG && (
                                <span className="service-tag">
                                    {(() => {
                                        let str;
                                        if (allMap && allMap?.serviceTypeList) {
                                            const obj = allMap?.serviceTypeList?.find((item) => item?.dataType === record?.dataType);
                                            if (obj) str = obj?.name;
                                        }
                                        return str || '- -';
                                    })()}
                                </span>
                            )}
                            {record?.dataSourceType === 'ASYNC' && <span className="service-tag">{'异步'}</span>}
                            {record?.mockFlag === 1 && (
                                <span className="service-tag">
                                    {'已开启 Mock'}
                                    <Tooltip placement="top" title={record?.mockType === 2 ? '系统模拟调用' : '接口模拟调用'}>
                                        <Icon type="info-circle" style={{ marginLeft: 4, fontSize: 12 }} />
                                    </Tooltip>
                                </span>
                            )}
                        </div>
                    </div>
                </div>

                <div className="card-content">
                    <div className="info-item">
                        <div className="info-label">{I18N.dataservicelist.heZuoFangMingCheng}</div>
                        <div className="info-value">{record.partnerDisplayName || '- -'}</div>
                    </div>
                    <div className="info-item">
                        <div className="info-label">{I18N.dataservicelist.chuangJianRen}</div>
                        <div className="info-value">{record.creator || '- -'}</div>
                    </div>
                    <div className="info-item create-time-row">
                        <div className="info-label">{I18N.dataservicelist.chuangJianShiJian}</div>
                        <div className="info-value">{record.gmtCreate || '- -'}</div>
                    </div>
                </div>

                {/* 悬浮时显示的底部操作按钮 */}
                <div className="card-actions-container">
                    <div
                        className={`card-action-button ${checkPermission('modify') ? 'disabled' : ''}`}
                        onClick={() => !checkPermission('modify') && modify(record)}>
                        <Icon type="form" style={{ fontSize: 20 }} />
                    </div>
                    <div
                        className={`card-action-button ${checkPermission('test') ? 'disabled' : ''}`}
                        onClick={() => !checkPermission('test') && test(record)}>
                        <Icon type="debug" style={{ fontSize: 20 }} />
                    </div>
                    <div
                        className={`card-action-button ${checkPermission('mockConfig') ? 'disabled' : ''}`}
                        onClick={() => !checkPermission('mockConfig') && handleMock(record)}>
                        <Icon type="mock" style={{ fontSize: 20 }} />
                    </div>
                    <div className={'card-action-button'}>
                        <Dropdown
                            overlay={getOperationMenu(record)}
                            trigger={['click']}
                            placement="bottomRight"
                            visible={dropdownVisibleRecord === record.uuid}
                            onVisibleChange={(v) => setDropdownVisibleRecord(v ? record.uuid : null)}>
                            <Icon type="more" style={{ fontSize: 20 }} />
                        </Dropdown>
                    </div>
                </div>
            </div>
        );
    };

    const onFormChange = async (values, { name }) => {
        // 设置日期模式
        if (name !== 'name') {
            await dispatch({
                type: 'dataServiceList/updateSearchParams',
                payload: {
                    curPage: 1,
                    pageSize: 9, // 修改为9
                    ...values
                }
            });
            debouncedSearch();
        }
    };

    // 处理过滤条件变化
    const handleFilterChange = async (filters) => {
        await dispatch({
            type: 'dataServiceList/updateSearchParams',
            payload: {
                curPage: 1,
                pageSize: 9, // 修改为9
                ...filters
            }
        });
        debouncedSearch();
    };

    return (
        <>
            {menuTreeReady && checkFunctionHasPermission('TZ0202', 'query') && (
                <div className="page-global-body data-source">
                    <div className="page-global-body-queryform">
                        <QueryForm
                            ref={queryFormRef}
                            showSearch={false}
                            initialValues={{
                                partnerId: partnerId || undefined,
                                displayName: displayName || undefined,
                                status: status || undefined,
                                name: name || undefined,
                                dataType: dataType || undefined,
                                dataSourceType: dataSourceType || undefined
                            }}
                            extraActions={
                                <>
                                    <Tooltip exclude title={I18N.dataservicelist.daoRu}>
                                        <Button icon="import" onClick={importData} />
                                    </Tooltip>
                                    {window.auth('TZ0202', 'add') && (
                                        <Dropdown overlay={addMenu}>
                                            <Button type="primary" icon="plus">
                                                {I18N.dataservicelist.xinZeng}
                                            </Button>
                                        </Dropdown>
                                    )}
                                </>
                            }
                            renderActions={() => {
                                return (
                                    <>
                                        <FilterPanel
                                            visible={filterPanelVisible}
                                            onVisibleChange={setFilterPanelVisible}
                                            providerList={providerList}
                                            serviceTypeList={allMap?.serviceTypeList}
                                            onChange={handleFilterChange}
                                            value={{
                                                partnerId: partnerId || undefined,
                                                dataType: dataType ? Number(dataType) : undefined,
                                                dataSourceType: dataSourceType || undefined,
                                                status: status || undefined
                                            }}
                                        />
                                        <Button
                                            type="link"
                                            onClick={() => {
                                                const resetData = {
                                                    partnerId: undefined,
                                                    displayName: undefined,
                                                    name: undefined,
                                                    dataType: undefined,
                                                    dataSourceType: undefined,
                                                    status: undefined
                                                };
                                                queryFormRef.current.form?.setValues?.(resetData);
                                                onFormChange(resetData, {});
                                            }}>
                                            重置
                                        </Button>
                                    </>
                                );
                            }}
                            onSearch={(v) => {
                                changeField({
                                    ...searchParams,
                                    name: v.name
                                });
                            }}
                            onReset={() => {
                                const resetData = {
                                    partnerId: undefined,
                                    displayName: undefined,
                                    name: undefined,
                                    dataType: undefined,
                                    dataSourceType: undefined,
                                    status: undefined
                                };
                                queryFormRef.current.form?.setValues?.(resetData);
                                onFormChange(resetData, {});
                            }}
                            onChange={onFormChange}
                            hasOnChange>
                            <Field
                                type="input"
                                name="name"
                                propsTitle={I18N.dataservicelist.fuWuJieKouBiao}
                                props={{
                                    placeholder: '请搜索数据源服务接口名称/标识',
                                    prefix: <Icon type="search" />,
                                    style: {
                                        width: 270
                                    }
                                }}
                            />
                        </QueryForm>
                    </div>
                    <div className="page-global-body-in">
                        <div className="page-global-body-main">
                            {/* 替换表格为卡片列表 */}
                            {loading ? (
                                <div style={{ textAlign: 'center', padding: '20px' }}>
                                    <Icon type="loading" />
                                </div>
                            ) : (
                                <div className="card-list-container">
                                    {tableList && tableList?.length > 0 ? (
                                        tableList?.map((record) => renderServiceCard(record))
                                    ) : (
                                        <div style={{ width: '100%', textAlign: 'center', padding: '20px' }}>
                                            {I18N.dataservicelist.wuJieGuoShuJu || '暂无数据'}
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                    <div className="page-global-body-pagination" style={{ boxShadow: 'none' }}>
                        <span className="ml20">
                            {/* 共 {total} 条记录 */}
                            {I18N.template(I18N.dataservicelist.tiaoJiLu, { val1: total })}
                        </span>
                        <Pagination
                            showQuickJumper
                            current={searchParams.curPage}
                            pageSize={searchParams.pageSize}
                            total={total}
                            onChange={(curPage, pageSize) => paginationOnChange(curPage, pageSize)}
                        />
                    </div>
                </div>
            )}
            {menuTreeReady && !checkFunctionHasPermission('TZ0202', 'query') && <NoPermission />}
            <Suspense fallback={null}>
                <TestModal
                    mockFlag={testMockFlag}
                    title={testTitle}
                    uuid={testId}
                    name={testName}
                    visible={testVisible}
                    onCancel={handleCancel}
                />
            </Suspense>
            <Suspense fallback={null}>
                <CopyModal record={record} visible={copyVisible} onCancel={handleCancel} onOk={copyModalOk} />
            </Suspense>
            <ImportModal
                importVisible={importVisible}
                updateVisible={updateImportVisible}
                query={() => {
                    const { dataServiceListStore } = props;
                    const { searchParams } = dataServiceListStore;
                    const { curPage, pageSize } = searchParams;
                    search(curPage, pageSize, 'noLoading');
                }}
                successMsg={I18N.dataservicelist.daoRuChengGong}
            />
            <ReferenceDrawer
                title={referenceDrawerData ? `${referenceDrawerData?.displayName}【${referenceDrawerData?.name}】` : ''}
                visible={!!referenceDrawerData}
                onClose={() => {
                    setReferenceDrawerData(null);
                }}
                fetchReference={() => {
                    return referenceAPI.getRelationResult({
                        componentType: 'DATASOURCE_SERVICE',
                        componentId: referenceDrawerData.name
                    });
                }}
            />
            <ParamModel
                appList={appList}
                visible={!!parameterDrawerData}
                currentRecord={parameterDrawerData}
                isEdit={true}
                onClose={() => {
                    setParameterDrawerData(null);
                }}
                onOk={() => {
                    search();
                    getThreeServiceList();
                    setParameterDrawerData(null);
                    // props.history.push(`/handle/parameter/list?uuid=${parameterDrawerData?.uuid}`);
                }}
            />
        </>
    );
};
resetStoreListener('dataServiceList');
export default connect((state) => ({
    globalStore: state.global,
    dataServiceListStore: state.dataServiceList
}))(TableContainer(DataServiceList));
