.m-data-service-test{
    .ant-modal-body{
        padding: 0 !important;
    }
    .modalBody {
        padding: 24px 80px;
    }
    .box{
        position: relative;
        .lable{
            display: inline-block;
            margin-bottom: 5px;
            b{
                color: #f00;
                vertical-align: middle;
                margin-right: 3px;
            }
        }
        input{
            margin-bottom: 25px;
        }
        .red-border{
            border: #f5222d solid 1px;
        }
        .tip{
            position: absolute;
            left: 0;
            bottom: 5px;
            font-size: 12px;
            color: #f5222d;
        }
    }
    .btn{
        width: 100%;
        margin-bottom: 20px;
    }
    .result{
        margin-bottom: 16px;
        .result-header{
            position: relative;
            font-size: 17px;
            padding-left: 12px;
            margin-bottom: 10px;
            &::before{
                position: absolute;
                left: 0;
                top: 2px;
                width: 3px;
                height: 20px;
                background: #4b93e5;
                content: "";
            }
        }
        .result-body{
            margin-top: 8px;
            padding: 8px;
            // border: 1px dashed #dcdcdc;
            background: #F8F9FB;
            .ant-row{
                padding: 5px 0;
                border-bottom: 1px #dcdada dashed;
                &:last-child{
                    border: none;
                }
                .ant-col-12{
                    word-break: break-all;
                }
            }
        }
    }
    .mockPrompts {
        height: 40px;
        display: flex;
        border: 1px solid #8CC4FF;
        border-radius: 16px;
        background-color: #E6F4FF;
        padding: 9px 20px;
        margin: 8px 80px 0;
        .titleBody {            
            &::before {
                vertical-align: text-top;
                display: inline-block;
                margin-right: 4px;
                content: '';
                background: url('@/sources/images/common/icon_info.svg') no-repeat center center;
                width: 16px;
                height: 16px;
            }
        }
    }
}