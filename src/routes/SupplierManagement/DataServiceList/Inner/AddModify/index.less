.g-dataservice-addmodify{
    .page-global-header{
        .u-back{
            cursor: pointer;
            .tnt-current-v3 & {
                background-color: transparent;
                color: #8b919e;
                .anticon-left {
                    color: #8b919e;
                }
            }
            &:hover{
                color: #2196F3;
            }
        }
        .u-title{
            margin-left: 20px;
            font-size: 14px;
        }
    }
    .page-global-body-main{
        min-height: 100%;
        background: #FFFFFF;
        box-shadow: 0 0 6px 0 rgba(215,219,243,0.50);
        border-radius: @border-radius-base;
        .tnt-current-v3 & {
            padding: 16px;
        }
        .steps{
            background: #F0F2F6;
            display: flex;
            height: 48px;
            border-radius: @border-radius-base;

            .step{
                flex: 1;
                line-height: 48px;
                padding-left: 40px;
                position: relative;
                border-radius: @border-radius-base;
            }
            .step-active {
                background: #1890ff;
                color: #fff;
                span{
                    position: absolute;
                    right: 0;
                    top: 0;
                    display: inline-block;
                    width: 30px;
                    background: #F0F2F6;
                    height: 100%;
                    i{
                        display: inline-block;
                        width: 0;
                        height: 0;
                        border-width: 24px;
                        border-style: solid;
                        border-color: transparent transparent transparent #1890ff;
                    }
                }
                &.last-step {
                    border-radius: 0 @border-radius-base @border-radius-base 0;
                }
            }
            .last-step span {
				background: #1890ff;
                border-radius: 0 @border-radius-base @border-radius-base 0;
            }
        }
        .wrap-box{
            width: 900px;
            margin: 30px auto;
            .title{
                border-left: 4px solid #1890ff;
                padding-left: 10px;
                font-weight: bold;
                line-height: 14px;
                margin-bottom: 20px;
                margin-top: 30px;
				b {
					color: red;
					vertical-align: middle;
					margin-right: 3px;
					font-weight: 600;
				}
            }
            .box{
                margin-bottom: 20px;
                .u-label{
                    margin-bottom: 2px;
                    display: block;
                    b{
                        color: red;
                        vertical-align: middle;
                        margin-right: 3px;
                    }
                    .anticon-question-circle{
                        margin-left: 5px;
                        cursor: pointer;
                    }
                }
                .u-radio{
                    background: #f0f2f6;
                    width: 100%;
                    display: inline-block;
                    height: 32px;
                    line-height: 32px;
					border-radius: 4px;
					.ant-radio-group {
						height: 100%;
						display: flex;
						justify-content: space-around;
						align-items: center;
						.ant-radio-wrapper{
							font-size: 12px;
						}
						.anticon-question-circle{
							margin-left: 5px;
							cursor: pointer;
							font-size: 14px;
						}
					}
                }
                .u-delay-contract{
                    position: relative;
                    font-size: 12px;
                    color: #f90;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                    display: inline-block;
                    width: 275px;
                    vertical-align: middle;
                    margin-left: 10px;
					margin-top: -4px;
                    padding-right: 20px;
                    .anticon-close-circle{
                        position: absolute;
                        right: 0;
                        top: 0;
                        color: #999;
                        cursor: pointer;
                        &:hover{
                            color: #f00;
                        }
                    }
                }
            }

            .ant-form.ant-form-vertical{
                .ant-row.ant-form-item{
                    width: 48%;
                    display: inline-block;
                    margin-right: 2%;
                }
                .input-config {
                    .ant-row.ant-form-item{
                        width: 100%;
                        display: inline-block;
                    }
                }
                .output-config {
                    .ant-row.ant-form-item{
                        width: 100%;
                        display: inline-block;
                    }
                }

            }
        }
    }
    .m-service-table, .m-service-table2{
        .ant-table-thead{
            background: #fafafa !important;
        }
        .ant-table-body{
            margin: 0 !important;
            .ant-input, .ant-select-selection{
                border: 1px solid #d9d9d9;
                // border-bottom: 1px solid #d9d9d9;
                border-radius: 8px;
                // box-shadow: none;
            }
            .ant-input:link, .ant-input:visited, .ant-input:active, .ant-input:focus{
                border-bottom: 1px solid #1890ff;
            }
        }
        .ant-table-tbody > tr > td{
            border-bottom: none;
        }
        .ant-table-small{
            border-bottom-right-radius: 0px;
            border-bottom-left-radius: 0px;
        }
        .u-operate{
            color: #777;
            display: inline-block;
            margin-top: 2px;
        }
        .ant-table-column-title{
            .anticon-question-circle{
                margin-left: 2px;
                cursor: pointer;
            }
        }
    }
    .m-service-table{
        .ant-table-pagination{
            position: absolute;
            right: 0;
            bottom: -92px;
        }
    }
    .u-add{
        text-align: center;
        border: solid 1px #e8e8e8;
        height: 40px;
        line-height: 40px;
        border-bottom-right-radius: 4px;
        border-bottom-left-radius: 4px;
        border-top: none;
        cursor: pointer;
        color: #777;
        margin-bottom: 45px;
        font-size: 12px;
        i{
            font-size: 16px;
            margin-right: 8px;
            vertical-align: middle;
            margin-top: -4px;
        }
    }
    .m-flow-limit{
        margin-bottom: 10px;
        .ant-select{
            width: 100px;
            margin-right: 10px;
        }
        .ant-input-number{
            margin-right: 10px;
            vertical-align: bottom;
        }
        .anticon-plus-circle, .anticon-delete{
            font-size: 18px;
            cursor: pointer;
            vertical-align: middle;
            margin-top: -2px;
            margin-right: 10px;
            color: #999;
            &:hover{
                color: #2196F3;
            }
        }
    }
}

.u-contract-option{
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 100%;
    display: inline-block;
    vertical-align: middle;
    margin-top: -2px;

	&.u-contract-option-disabled {
		color: rgba(0,0,0,.25);
	}
}

.u-contract-option-detail{
    display: none;
    color: #1890ff;
}

.ant-select-dropdown-menu-item-active{
    .u-contract-option-detail{
        display: inline-block;
    }

}

.highlightInput{
    border-color: red !important;
    .ant-select-selection--single{
    border-color: red !important;
    }
}
.inputHighLight .ant-table {
	border-color: red !important;
}
.inputHighLight
	.ant-spin-nested-loading
	.ant-spin-container
	.ant-table-scroll-position-left {
	border-color: red !important;
}
