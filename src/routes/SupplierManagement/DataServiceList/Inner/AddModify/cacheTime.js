import I18N from '@/utils/I18N';
import { useEffect, useState, Fragment } from 'react';
import { Select, Switch, InputNumber } from 'tntd';
import { cloneDeep } from 'lodash';
// import service from './service';
import './index.less';

const Option = Select.Option;

export default (props) => {
    const { value = {}, onChange, disabled } = props;

    const { cacheOpen = 0, cacheInfrastructureType = 'nosql', cacheUnit = 'SAME_DAY', cacheUnitNumber } = value || {};

    // 当前缓存类型的具体值
    const [curCacheValue, setCurCacheValue] = useState({});

    // 缓存维度
    const cacheOptions = [
        {
            label: I18N.addmodify.cachetime.dangtian,
            value: 'SAME_DAY'
        },
        {
            label: I18N.addmodify.cachetime.shi,
            value: 'HOURS',
            placeholder: I18N.addmodify.cachetime.qingshuruxiaoshi
        },
        {
            label: I18N.addmodify.cachetime.tian,
            value: 'DAYS',
            placeholder: I18N.addmodify.cachetime.qingshurutianshu
        }
    ];

    const setting = (val, field, type) => {
        const newValues = cloneDeep(value || {});
        if (type === 'inputNumber') {
            const reRealNumber1 = /^\d+$/; // 正整数
            if (!val || !reRealNumber1.test(val)) return;
            if (val > 1000) return;
            newValues[field] = val;
        }
        newValues[field] = val;
        onChange(newValues);
    };

    // 选择缓存维度时的回调
    const onSelectChange = (val) => {
        const cur = cacheOptions.find((item) => item.value === val);
        if (cur) {
            setCurCacheValue(cur);
            setting(val, 'cacheUnit', 'select');
        }
    };

    useEffect(() => {
        if (cacheUnit === 'DAYS') {
            // 如果是天则赋初值为7
            setting(7, 'cacheUnitNumber', 'inputNumber');
        } else if (cacheUnit === 'HOURS') {
            setting(1, 'cacheUnitNumber', 'inputNumber');
        }
    }, [cacheUnit]);

    return (
        <div className="cache-time">
            <Switch
                className="u-checked"
                checkedChildren={I18N.addmodify.cachetime.kai} // 开
                unCheckedChildren={I18N.addmodify.cachetime.guan} // 关
                checked={!!cacheOpen}
                disabled={disabled}
                onChange={(e) => {
                    setting(e ? 1 : 0, 'cacheOpen', 'select');
                }}
            />
            <div style={{ verticalAlign: 'middle' }} className={`${cacheOpen === 1 ? 'showSwitch' : 'hiddenSwitch'}`}>
                {cacheUnit !== 'SAME_DAY' && (
                    <InputNumber
                        disabled={disabled}
                        style={{
                            width: '120px',
                            marginLeft: '5px',
                            marginRight: '5px'
                        }}
                        placeholder={curCacheValue.placeholder}
                        min={1}
                        max={1000}
                        step={1}
                        precision={0}
                        value={cacheUnitNumber}
                        onChange={(e) => setting(e, 'cacheUnitNumber', 'inputNumber')}
                    />
                )}
                <Select
                    disabled={disabled}
                    value={cacheUnit}
                    style={{
                        maxWidth: '100px',
                        marginRight: '5px'
                    }}
                    // onChange={(e) => setting(e, 'cacheUnit', 'select')}
                    onChange={(e) => onSelectChange(e)}>
                    {cacheOptions.map((item) => (
                        <Option key={item.value} value={item.value}>
                            {item.label}
                        </Option>
                    ))}
                </Select>
                {/* {
                    cacheUnit === 'DAYS' && (
                        <>
                            <InputNumber
                                disabled={disabled}
                                style={{
                                    width: '65px',
                                    marginLeft: '5px',
                                    marginRight: '5px'
                                }}
                                min={1}
                                max={1000}
                                step={1}
                                precision={0}
                                value={cacheday}
                                onChange={(e) => setting(e, 'cacheday', 'inputNumber')}
                            />{' '}
                            {I18N.addmodify.cachetime.tian}{' '}
                        </>
                    )
                } */}

                <Select
                    disabled={disabled}
                    value={cacheInfrastructureType || 'nosql'}
                    style={{ maxWidth: 160, marginLeft: '5px' }}
                    onChange={(e) => setting(e, 'cacheInfrastructureType', 'select')}>
                    <Option value="nosql">{I18N.addmodify.cachetime.huanCun}</Option>
                    <Option value="elasticsearch">{I18N.addmodify.cachetime.chiJiuHuaKu}</Option>
                </Select>
            </div>
        </div>
    );
};
