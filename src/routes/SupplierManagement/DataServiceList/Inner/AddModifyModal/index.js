/*
 * @Author: l<PERSON><PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: Modal弹框
 */

import I18N from '@/utils/I18N';
import { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import { Modal, Input, Select, message, Button, Steps, DatePicker, Table, Icon, Switch, Checkbox, Tooltip } from 'tntd';
import { dataServiceListAPI, etlAPI } from '@/services';
import './index.less';

const Option = Select.Option;
const { TextArea } = Input;
const Step = Steps.Step;
const modelMsg5 = I18N.addmodifymodal.index.fuWuChuCanBu;
const modelMsg4 = I18N.addmodifymodal.index.fuWuRuCanBu;

class AddModifyModal extends PureComponent {
    state = {
        current: 0,
        inputPage: {
            curPage: 1,
            pageSize: 10
        },
        outputPage: {
            curPage: 1,
            pageSize: 10
        },
        loading: false,
        modifyDisabled: false, // 三方标识是否可填
        etlList: []
    };

    componentDidMount() {
        const { dataServiceListStore } = this.props;
        const { dialogData } = dataServiceListStore;
        const { addEditModalData } = dialogData;
        const { name } = addEditModalData;
        if (name) {
            this.setState({
                modifyDisabled: true
            });
        }
        // 监听
        document.body.addEventListener('mousemove', this.listenner);
        this.getEtlList();
    }

    componentWillUnmount() {
        document.body.removeEventListener('mousemove', this.listenner);
    }

    listenner = (e) => {
        const targetPath = e.path;
        let flag = false;
        targetPath.forEach((item) => {
            if (item.className) {
                if (typeof item.className !== 'string') return;
                if (item.className === 'u-textarea') {
                    flag = true;
                }
            }
        });
        document.body.querySelector('.m-data-service').parentNode.classList.add('u-noscroll');
        if (flag) {
            document.body.querySelector('.m-data-service').parentNode.style.overflow = 'hidden';
        } else {
            document.body.querySelector('.m-data-service').parentNode.style.overflow = 'auto';
        }
    };

    // 点击确定
    handleOk = () => {
        const { dataServiceListStore, globalStore } = this.props;
        const { account } = globalStore.currentUser;
        const { dialogData } = dataServiceListStore;
        const { addEditModalData } = dialogData;
        const { methodType, url, contentType, postEtlHandlerName, preEtlHandlerName, responseType, inputConfig, outputConfig } =
            addEditModalData;
        if (!methodType || !url || !contentType || !postEtlHandlerName || !responseType) {
            return message.warning(I18N.addmodifymodal.index.cunZaiWeiTianBi); // 有必填项未填
        }
        // 校验服务入参是否有未填项
        let flag = false;
        inputConfig.forEach((item) => {
            if (!item.displayName || !item.serviceParam || !item.sendSpace) flag = true;
            if (item.type === 'variable' && !item.dataType) flag = true;
            if (item.type === 'constant' && !item.value) flag = true;
        });
        if (flag) return message.warning(I18N.addmodifymodal.index.fuWuRuCanYou); // 服务入参有未填字段
        // 校验服务出参是否有未填项
        let flag2 = false;
        outputConfig.forEach((item) => {
            if (!item.displayName || !item.serviceParam) flag2 = true;
            if (item.checked && item.includeCheck.checkType === 2 && !item.includeCheck.checkValue) flag2 = true;
        });
        if (flag2) return message.warning(I18N.addmodifymodal.index.fuWuChuCanYou); // 服务出参有未填字段
        // 服务入参 - 查找是否有重名参数标识
        let repeatName = null;
        inputConfig.forEach((item) => {
            const obj = inputConfig.find((subItem) => subItem.serviceParam === item.serviceParam);
            if (obj && item.uuid !== obj.uuid) repeatName = obj.serviceParam;
        });
        if (repeatName) return message.warning(`${modelMsg4}${repeatName}`); // 服务入参不能存在相同参数标识
        // 服务出参 - 查找是否有重名参数标识
        let repeatName2 = null;
        outputConfig.forEach((item) => {
            const obj = outputConfig.find((subItem) => subItem.serviceParam === item.serviceParam);
            if (obj && item.uuid !== obj.uuid) repeatName2 = obj.serviceParam;
        });
        if (repeatName2) return message.warning(`${modelMsg5}${repeatName2}`); // 服务出参不能存在相同参数标识

        let params = cloneDeep(addEditModalData);
        params.inputConfig = JSON.stringify(params.inputConfig);
        params.outputConfig = JSON.stringify(params.outputConfig);
        params.operator = account;
        this.updateData(params);
    };

    // 添加数据
    addData = (params) => {
        this.setState({ loading: true });
        dataServiceListAPI
            .addData(params)
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success && res.data) {
                    const { dispatch } = this.props;
                    dispatch({
                        type: 'dataServiceList/setAttrValue',
                        payload: {
                            dialogData: {
                                addEditModalData: {
                                    uuid: res.data.uuid
                                }
                            }
                        }
                    });
                    this.setState({ current: 1 });
                } else {
                    message.error(res.message);
                }
            })
            .catch((err) => {
                console.log(err);
                this.setState({ loading: false });
            });
    };

    // 更新数据
    updateData = (params) => {
        this.setState({ loading: true });
        dataServiceListAPI
            .updateData(params)
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success) {
                    this.handleCancel();
                    message.success(res.message);
                    const { dispatch, dataServiceListStore } = this.props;
                    const { searchParams, modalType } = dataServiceListStore;
                    const { curPage, pageSize } = searchParams;
                    dispatch({
                        type: 'dataServiceList/getList',
                        payload: {
                            curPage: modalType === 1 ? 1 : curPage,
                            pageSize
                        }
                    });
                    if (modalType === 1) {
                        this.props.onAddData();
                    } else {
                        dispatch({
                            type: 'global/getAllThreeService'
                        });
                    }
                } else {
                    message.error(res.message);
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    // 点击取消
    handleCancel = () => {
        const { dispatch } = this.props;
        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                dialogShow: {
                    addEditModal: false
                }
            }
        });
        setTimeout(() => {
            this.setState({
                current: 0,
                inputPage: {
                    curPage: 1,
                    pageSize: 5
                },
                outputPage: {
                    curPage: 1,
                    pageSize: 5
                }
            });
            dispatch({
                type: 'dataServiceList/setAttrValue',
                payload: {
                    dialogData: {
                        addEditModalData: {
                            uuid: null,
                            partnerId: null, // 供应商名称
                            dataType: null, // 数据类型
                            displayName: null, // 三方服务接口名称
                            name: null, // 三方数据标识
                            purchaseStartTime: null, // 采购开始日期
                            purchaseEndTime: null, // 采购结束日期
                            confidence: null, // 置信度
                            costLevel: null, // 成本等级
                            contractCode: null, // 合同编号
                            chargeType: null, // 计费方式
                            chargeMethod: null, // 计费类型
                            rateLimit: null, // 流量上限（条）
                            price: null, // 价格（元）
                            cacheday: null, // 数据缓存期（天）
                            retry: null, // 数据重试次数
                            timeout: null, // 数据超时时间（ms）
                            creator: null, // 创建人
                            methodType: null, // 接口类型（协议）
                            url: null, // url地址
                            contentType: null, // 调用方式（Method）
                            postEtlHandlerName: null, // 后置ETL处理器
                            preEtlHandlerName: null, // 前置处理器
                            responseType: null, // 返回方式
                            inputTemplate: null, // 输入处理器模板
                            outputTemplate: null, // 输出处理器模板
                            mockConfig: null, // mock测试
                            inputConfig: [], // 服务入参
                            outputConfig: [] // 服务出参
                        }
                    }
                }
            });
        }, 300);
    };

    // 改变参数
    changeField(e, type, field) {
        const { dispatch } = this.props;
        let val = null;
        let obj = {};
        if (type === 'select') val = e;
        if (type === 'input') val = e.target.value;
        if (type === 'datePicker') {
            val = moment(e).valueOf();
            const { purchaseStartTime, purchaseEndTime } = this.props.dataServiceListStore.dialogData.addEditModalData;
            if (field === 'purchaseStartTime' && purchaseEndTime) {
                if (val > purchaseEndTime) return message.warning(I18N.addmodifymodal.index.kaiShiRiQiBu); // 开始日期不能大于结束日期
            }
            if (field === 'purchaseEndTime' && purchaseStartTime) {
                if (val < purchaseStartTime) return message.warning(I18N.addmodifymodal.index.jieShuRiQiBu); // 结束日期不能小于开始日期
            }
        }
        if (field === 'methodType') {
            // 联动清空
            obj['contentType'] = null;
        }
        obj[field] = val ? val : null;

        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        ...obj
                    }
                }
            }
        });
    }

    // 下一步
    next = () => {
        const { dataServiceListStore, dispatch, globalStore } = this.props;
        const { account } = globalStore.currentUser;
        const { dialogData, modalType } = dataServiceListStore;
        const { addEditModalData } = dialogData;
        const {
            partnerId,
            dataType,
            displayName,
            confidence,
            costLevel,
            chargeType,
            chargeMethod,
            price,
            rateLimit,
            purchaseStartTime,
            purchaseEndTime,
            name,
            uuid
        } = addEditModalData;
        // 查看直接跳下一步
        if (modalType === 3) {
            this.setState({ current: 1 });
            return;
        }
        if (
            !partnerId ||
            !dataType ||
            !displayName ||
            !confidence ||
            !costLevel ||
            !chargeType ||
            !chargeMethod ||
            !price ||
            !rateLimit ||
            !purchaseStartTime ||
            !purchaseEndTime ||
            !name
        ) {
            return message.warning(I18N.addmodifymodal.index.cunZaiWeiTianBi); // 有必填项未填
        }
        if (!uuid) {
            // 新增情况下，点击下一步要请求接口
            const params = {
                ...addEditModalData,
                appNames: addEditModalData.appName,
                creator: account
            };
            this.addData(params);
            dispatch({
                type: 'dataServiceList/setAttrValue',
                payload: {
                    dialogData: {
                        addEditModalData: {
                            creator: account
                        }
                    }
                }
            });
        } else {
            // 修改
            this.setState({ current: 1 });
        }
    };

    // 上一步
    up = () => {
        this.setState({ current: 0 });
    };

    // 添加服务入参
    addInputData = () => {
        const { inputPage } = this.state;
        const { dispatch, dataServiceListStore } = this.props;
        const { inputConfig } = dataServiceListStore.dialogData.addEditModalData;
        let copyInputConfig = cloneDeep(inputConfig);
        copyInputConfig.push({
            displayName: null, // 字段名称
            serviceParam: null, // 字段标识
            type: 'variable', // 字段类型(constant常量/variable变量)
            value: null, // 值类型：type=constant时的常量值
            dataType: null, // 值类型(1字符型/2整型/3小数型/4布尔型/5日期型)
            mustInput: false, // 是否必填
            sendSpace: null // 类型
        });
        copyInputConfig.forEach((item, index) => {
            item.uuid = index;
        });
        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        inputConfig: copyInputConfig
                    }
                }
            }
        });
        const curPage = Math.ceil(copyInputConfig.length / inputPage.pageSize);
        this.setState({
            inputPage: {
                curPage,
                pageSize: inputPage.pageSize
            }
        });
    };

    // 删除服务入参
    deleteInput(id) {
        const { dispatch, dataServiceListStore } = this.props;
        const { inputConfig } = dataServiceListStore.dialogData.addEditModalData;
        let copyInputConfig = cloneDeep(inputConfig);
        copyInputConfig.splice(id, 1);
        copyInputConfig.forEach((item, index) => {
            item.uuid = index;
        });
        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        inputConfig: copyInputConfig
                    }
                }
            }
        });
    }

    // 改变服务入参
    changeInputField(e, type, field, uuid) {
        const { dispatch, dataServiceListStore } = this.props;
        const { inputConfig } = dataServiceListStore.dialogData.addEditModalData;
        let copyInputConfig = cloneDeep(inputConfig);
        let val = null;
        if (type === 'select') val = e;
        if (type === 'input') val = e.target.value;
        if (type === 'switch') {
            if (field === 'type') {
                if (e) {
                    val = 'variable';
                    copyInputConfig[uuid]['value'] = null;
                }
                if (!e) {
                    val = 'constant';
                    copyInputConfig[uuid]['dataType'] = null;
                }
            } else {
                val = e;
            }
        }
        copyInputConfig[uuid][field] = val;
        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        inputConfig: copyInputConfig
                    }
                }
            }
        });
    }

    // 服务入参分页
    inputOnChange = (curPage, pageSize) => {
        this.setState({
            inputPage: {
                curPage,
                pageSize
            }
        });
    };

    // 服务出参分页
    outputOnChange = (curPage, pageSize) => {
        this.setState({
            outputPage: {
                curPage,
                pageSize
            }
        });
    };

    // 添加服务出参
    addOutputData = () => {
        const { outputPage } = this.state;
        const { dispatch, dataServiceListStore } = this.props;
        const { outputConfig } = dataServiceListStore.dialogData.addEditModalData;
        let copyOutputConfig = cloneDeep(outputConfig);
        copyOutputConfig.push({
            displayName: null, // 字段名称
            serviceParam: null, // 字段标识
            type: 'variable', // 字段类型(constant常量/variable变量)
            includeCheck: {
                checkType: 1, // 校验类型(1不为空命中/2具体值命中)
                checkValue: null // checkType=2时的值
            },
            checked: false
        });
        copyOutputConfig.forEach((item, index) => {
            item.uuid = index;
        });
        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        outputConfig: copyOutputConfig
                    }
                }
            }
        });
        const curPage = Math.ceil(copyOutputConfig.length / outputPage.pageSize);
        this.setState({
            outputPage: {
                curPage,
                pageSize: outputPage.pageSize
            }
        });
    };

    // 删除服务出参
    deleteOutput(uuid) {
        const { dispatch, dataServiceListStore } = this.props;
        const { outputConfig } = dataServiceListStore.dialogData.addEditModalData;
        let copyOutputConfig = cloneDeep(outputConfig);
        copyOutputConfig.splice(uuid, 1);
        copyOutputConfig.forEach((item, index) => {
            item.uuid = index;
        });
        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        outputConfig: copyOutputConfig
                    }
                }
            }
        });
    }

    // 改变服务出参
    changeOutputField(e, type, field, uuid) {
        const { dispatch, dataServiceListStore } = this.props;
        const { outputConfig } = dataServiceListStore.dialogData.addEditModalData;
        let copyOutputConfig = cloneDeep(outputConfig);
        let val = null;
        if (type === 'select') val = e;
        if (type === 'input') val = e.target.value;
        if (type === 'switch') {
            if (field === 'type') {
                if (e) val = 'variable';
                if (!e) val = 'constant';
            }
        }
        if (type === 'checkbox') {
            val = e.target.checked;
            if (!val) {
                // 重置
                copyOutputConfig[uuid]['includeCheck']['checkType'] = 1;
                copyOutputConfig[uuid]['includeCheck']['checkValue'] = null;
            }
        }
        if (field === 'checkType') {
            if (e === 1) {
                copyOutputConfig[uuid]['includeCheck']['checkValue'] = null;
            }
            copyOutputConfig[uuid]['includeCheck'][field] = val;
        } else if (field === 'checkValue') {
            copyOutputConfig[uuid]['includeCheck'][field] = val;
        } else {
            copyOutputConfig[uuid][field] = val;
        }
        dispatch({
            type: 'dataServiceList/setAttrValue',
            payload: {
                dialogData: {
                    addEditModalData: {
                        outputConfig: copyOutputConfig
                    }
                }
            }
        });
    }

    // 获取ETL处理器列表
    getEtlList = () => {
        this.setState({ etlList: [] });
        etlAPI.getEtlList().then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                this.setState({ etlList: res.data });
            } else {
                message.error(res.message);
            }
        });
    };

    render() {
        const { current, inputPage, outputPage, loading, modifyDisabled, etlList } = this.state;
        const { dataServiceListStore, globalStore, providerList } = this.props;
        const { allMap } = globalStore;
        const { dialogShow, modalType, dialogData } = dataServiceListStore;
        const { addEditModalData } = dialogData;
        const {
            partnerId,
            dataType,
            displayName,
            confidence,
            costLevel,
            contractCode,
            chargeType,
            chargeMethod,
            price,
            rateLimit,
            cacheday,
            retry,
            timeout,
            purchaseStartTime,
            purchaseEndTime,
            name,
            methodType,
            url,
            contentType,
            responseType,
            postEtlHandlerName,
            preEtlHandlerName,
            inputTemplate,
            outputTemplate,
            mockConfig,
            inputConfig,
            outputConfig
        } = addEditModalData;

        let title = I18N.addmodifymodal.index.shuJuFuWuXin; // 数据服务信息
        let disabled = false;
        const add = I18N.addmodifymodal.index.xinZeng;
        const update = I18N.addmodifymodal.index.xiuGai;
        if (modalType === 1) title = `${add}-${title}`; // 新增
        if (modalType === 2) {
            title = `${update}-${title}`; // 修改
        }
        if (modalType === 3) disabled = true;

        // 接口类型选择，联动调用方式list
        let methodList = [];
        if (methodType && allMap && allMap.contentTypeList && allMap.methodTypeList) {
            const obj = allMap.methodTypeList.find((item) => item.code === methodType);
            if (obj) {
                allMap.contentTypeList.forEach((item) => {
                    if (item.type === obj.type) {
                        methodList.push(item);
                    }
                });
            }
        }

        const inputColumns = [
            {
                title: I18N.addmodifymodal.index.canShuMingCheng, // 参数名称
                dataIndex: 'displayName',
                key: 'displayName',
                render: (text, record) => {
                    let dom = (
                        <Input
                            disabled={disabled}
                            value={text}
                            onChange={(e) => this.changeInputField(e, 'input', 'displayName', record.uuid)}
                        />
                    );
                    return dom;
                }
            },
            {
                title: I18N.addmodifymodal.index.canShuBiaoZhi, // 参数标识
                dataIndex: 'serviceParam',
                key: 'serviceParam',
                render: (text, record) => {
                    let dom = (
                        <Input
                            disabled={disabled}
                            value={text}
                            onChange={(e) => this.changeInputField(e, 'input', 'serviceParam', record.uuid)}
                        />
                    );
                    return dom;
                }
            },
            {
                title: I18N.addmodifymodal.index.canShuLeiXing, // 参数类型
                dataIndex: 'type',
                key: 'type',
                width: 80,
                render: (text, record) => {
                    let dom = (
                        <Switch
                            className="u-checked"
                            checkedChildren={I18N.addmodifymodal.index.bianLiang} // 变量
                            unCheckedChildren={I18N.addmodifymodal.index.dingZhi} // 定值
                            checked={text === 'variable'}
                            disabled={disabled}
                            onChange={(e) => this.changeInputField(e, 'switch', 'type', record.uuid)}
                        />
                    );
                    return dom;
                }
            },
            {
                title: I18N.addmodifymodal.index.zhiLeiXing, // 值类型
                dataIndex: 'value',
                key: 'value',
                width: 140,
                render: (text, record) => {
                    let dom = (
                        <Select
                            showSearch
                            style={{ width: '100%' }}
                            disabled={disabled}
                            optionFilterProp="children"
                            value={record.dataType ? record.dataType : undefined}
                            onChange={(e) => this.changeInputField(e, 'select', 'dataType', record.uuid)}>
                            <Option value={1}>
                                {I18N.addmodifymodal.index.ziFuXing}
                                {/* 字符型 */}
                            </Option>
                            <Option value={2}>
                                {I18N.addmodifymodal.index.zhengXing}
                                {/* 整型 */}
                            </Option>
                            <Option value={3}>
                                {I18N.addmodifymodal.index.xiaoShuXing}
                                {/* 小数型 */}
                            </Option>
                            <Option value={4}>
                                {I18N.addmodifymodal.index.buErXing}
                                {/* 布尔型 */}
                            </Option>
                            <Option value={5}>
                                {I18N.addmodifymodal.index.riQiXing}
                                {/* 日期型 */}
                            </Option>
                        </Select>
                    );
                    if (record.type === 'constant') {
                        dom = (
                            <Input
                                disabled={disabled}
                                value={record.value}
                                onChange={(e) => this.changeInputField(e, 'input', 'value', record.uuid)}
                            />
                        );
                    }
                    return dom;
                }
            },
            {
                title: I18N.addmodifymodal.index.biTian, // 是否必填
                dataIndex: 'mustInput',
                key: 'mustInput',
                width: 80,
                render: (text, record) => {
                    let dom = (
                        <Switch
                            checkedChildren={I18N.addmodifymodal.index.shi} // 是
                            unCheckedChildren={I18N.addmodifymodal.index.fou} // 否
                            checked={text}
                            disabled={disabled}
                            onChange={(e) => this.changeInputField(e, 'switch', 'mustInput', record.uuid)}
                        />
                    );
                    return record.type === 'variable' ? dom : '--';
                }
            },
            {
                title: I18N.addmodifymodal.index.leiXing, // 类型
                dataIndex: 'sendSpace',
                key: 'sendSpace',
                width: 140,
                render: (text, record) => {
                    let dom = (
                        <Select
                            showSearch
                            style={{ width: '100%' }}
                            disabled={disabled}
                            optionFilterProp="children"
                            value={text ? text : undefined}
                            onChange={(e) => this.changeInputField(e, 'select', 'sendSpace', record.uuid)}>
                            <Option value="url">url</Option>
                            <Option value="header">header</Option>
                            <Option value="body">body</Option>
                        </Select>
                    );
                    return dom;
                }
            },
            {
                title: I18N.addmodifymodal.index.caoZuo, // 操作
                dataIndex: 'operate',
                key: 'operate',
                width: 60,
                align: 'center',
                render: (text, record) => {
                    let dom = (
                        <span className="u-operate" onClick={() => this.deleteInput(record.uuid)}>
                            <Icon type="delete" />
                        </span>
                    );
                    if (modalType === 3) dom = '--';
                    return dom;
                }
            }
        ];

        let outputColumns = [
            {
                title: I18N.addmodifymodal.index.canShuMingCheng, // 参数名称
                dataIndex: 'displayName',
                key: 'displayName',
                render: (text, record) => {
                    let dom = (
                        <Input
                            disabled={disabled}
                            value={text}
                            onChange={(e) => this.changeOutputField(e, 'input', 'displayName', record.id)}
                        />
                    );
                    return dom;
                }
            },
            {
                title: I18N.addmodifymodal.index.canShuBiaoZhi, // 参数标识
                dataIndex: 'serviceParam',
                key: 'serviceParam',
                render: (text, record) => {
                    let dom = (
                        <Input
                            disabled={disabled}
                            value={text}
                            onChange={(e) => this.changeOutputField(e, 'input', 'serviceParam', record.uuid)}
                        />
                    );
                    return dom;
                }
            },
            {
                title: I18N.addmodifymodal.index.canShuLeiXing, // 参数类型
                dataIndex: 'type',
                key: 'type',
                width: 80,
                render: (text, record) => {
                    let dom = (
                        <Switch
                            className="u-checked"
                            checkedChildren={I18N.addmodifymodal.index.bianLiang} // 变量
                            unCheckedChildren={I18N.addmodifymodal.index.dingZhi} // 定值
                            checked={text === 'variable'}
                            disabled={disabled}
                            onChange={(e) => this.changeOutputField(e, 'switch', 'type', record.uuid)}
                        />
                    );
                    return dom;
                }
            },
            {
                title: I18N.addmodifymodal.index.chaDeZiDuan, // 查得字段
                dataIndex: 'includeCheck',
                key: 'includeCheck',
                width: 150,
                render: (text, record) => {
                    let dom = (
                        <span>
                            <Checkbox
                                disabled={disabled}
                                checked={record.checked}
                                onChange={(e) => this.changeOutputField(e, 'checkbox', 'checked', record.uuid)}
                            />
                            {record.checked && (
                                <Select
                                    showSearch
                                    style={{ width: '80px', margin: '0 10px' }}
                                    disabled={disabled}
                                    optionFilterProp="children"
                                    value={record.includeCheck.checkType ? record.includeCheck.checkType : undefined}
                                    onChange={(e) => this.changeOutputField(e, 'select', 'checkType', record.uuid)}>
                                    <Option value={1}>
                                        {I18N.addmodifymodal.index.feiKong}
                                        {/* 非空 */}
                                    </Option>
                                    <Option value={2}>
                                        {I18N.addmodifymodal.index.quZhi}
                                        {/* 取值 */}
                                    </Option>
                                </Select>
                            )}
                            {record.includeCheck.checkType === 2 && (
                                <Input
                                    disabled={disabled}
                                    style={{ width: '165px' }}
                                    value={record.includeCheck.checkValue}
                                    onChange={(e) => this.changeOutputField(e, 'input', 'checkValue', record.uuid)}
                                />
                            )}
                        </span>
                    );
                    return dom;
                }
            },
            {
                title: I18N.addmodifymodal.index.caoZuo, // 操作
                dataIndex: 'operate',
                key: 'operate',
                width: 60,
                align: 'center',
                render: (text, record) => {
                    let dom = (
                        <span className="u-operate" onClick={() => this.deleteOutput(record.uuid)}>
                            <Icon type="delete" />
                        </span>
                    );
                    if (modalType === 3) dom = '--';
                    return dom;
                }
            }
        ];

        // 如果计费类型是查询，则服务出参无查得字段
        // if (chargeMethod === 1) {
        // 	outputColumns.splice(3, 1);
        // }

        const footer = (
            <div className="footer">
                {current === 0 && (
                    <Fragment>
                        <Button onClick={this.next} loading={loading}>
                            {/* 下一步 */}
                            {I18N.addmodifymodal.index.xiaYiBu}
                        </Button>
                    </Fragment>
                )}
                {current === 1 && (
                    <Fragment>
                        <Button onClick={this.up}>
                            {/* 上一步 */}
                            {I18N.addmodifymodal.index.shangYiBu}
                        </Button>
                        {modalType !== 3 && (
                            <Button type="primary" onClick={this.handleOk} loading={loading}>
                                {/* 保存 */}
                                {I18N.addmodifymodal.index.baoCun}
                            </Button>
                        )}
                    </Fragment>
                )}
            </div>
        );

        return (
            <Modal
                title={title}
                width={1000}
                visible={dialogShow.addEditModal}
                maskClosable={false}
                onOk={this.handleOk}
                onCancel={this.handleCancel}
                footer={footer}
                className="m-data-service">
                <Steps current={current}>
                    {/* 三方数据信息 */}
                    <Step title={I18N.addmodifymodal.index.shuJuYuanFuWu3} />
                    {/* 服务接口 */}
                    <Step title={I18N.addmodifymodal.index.fuWuJieKou} />
                </Steps>
                {current === 0 && (
                    <Fragment>
                        <div className="u-title">
                            {/* 三方数据信息 */}
                            {I18N.addmodifymodal.index.shuJuYuanFuWu3}
                        </div>
                        <div className="modal-box">
                            <span className="u-label">
                                <b>*</b>
                                {/* 供应商名称 */}
                                {I18N.addmodifymodal.index.heZuoFangMingCheng}
                            </span>
                            <span className="u-select">
                                <Select
                                    showSearch
                                    style={{ width: '100%' }}
                                    disabled={disabled}
                                    placeholder={I18N.addmodifymodal.index.qingXuanZeHeZuo} // 请选择供应商合作状态
                                    optionFilterProp="children"
                                    value={partnerId ? partnerId : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'partnerId')}>
                                    {providerList &&
                                        providerList.map((item, index) => {
                                            return (
                                                <Option value={item.name} key={index} disabled={item.status === 2}>
                                                    {item.displayName}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </span>
                            <span className="u-label">
                                <b>*</b>
                                {/* 数据类型 */}
                                {I18N.addmodifymodal.index.shuJuLeiXing}
                            </span>
                            <span className="u-select">
                                <Select
                                    showSearch
                                    style={{ width: '100%' }}
                                    disabled={disabled}
                                    placeholder={I18N.addmodifymodal.index.qingXuanZeShuJu} // 请选择数据类型
                                    optionFilterProp="children"
                                    value={dataType ? dataType : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'type')}>
                                    {allMap &&
                                        allMap.serviceTypeList &&
                                        allMap.serviceTypeList.map((item, index) => {
                                            return (
                                                <Option value={item.dataType} key={index}>
                                                    {item.name}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </span>
                        </div>
                        <div className="modal-box">
                            <span className="u-label">
                                <b>*</b>
                                {/* 三方服务接口名称 */}
                                {I18N.addmodifymodal.index.shuJuYuanFuWu2}
                            </span>
                            <span className="u-input">
                                <Input
                                    placeholder={I18N.addmodifymodal.index.qingTianXieShuJu2} // 请填写三方服务接口名称
                                    disabled={disabled}
                                    value={displayName}
                                    onChange={(e) => this.changeField(e, 'input', 'displayName')}
                                />
                            </span>
                            <span className="u-label">
                                <b>*</b>
                                {/* 三方数据标识 */}
                                {I18N.addmodifymodal.index.shuJuYuanFuWu}
                            </span>
                            <span className="u-input">
                                <Input
                                    placeholder={I18N.addmodifymodal.index.qingTianXieShuJu} // 请填写三方数据标识
                                    disabled={disabled ? disabled : modifyDisabled}
                                    value={name}
                                    onChange={(e) => this.changeField(e, 'input', 'name')}
                                />
                            </span>
                        </div>
                        <div className="modal-box">
                            <span className="u-label">
                                <b>*</b>
                                {/* 置信度 */}
                                {I18N.addmodifymodal.index.zhiXinDu}
                            </span>
                            <span className="u-select">
                                <Select
                                    showSearch
                                    style={{ width: '100%' }}
                                    disabled={disabled}
                                    placeholder={I18N.addmodifymodal.index.qingXuanZeZhiXin} // 请选择置信度
                                    optionFilterProp="children"
                                    value={confidence ? confidence : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'confidence')}>
                                    <Option value={1}>
                                        {I18N.addmodifymodal.index.gao}
                                        {/* 高 */}
                                    </Option>
                                    <Option value={2}>
                                        {I18N.addmodifymodal.index.zhong}
                                        {/* 中 */}
                                    </Option>
                                    <Option value={3}>
                                        {I18N.addmodifymodal.index.di}
                                        {/* 低 */}
                                    </Option>
                                </Select>
                            </span>
                            <span className="u-label">
                                <b>*</b>
                                {/* 成本等级 */}
                                {I18N.addmodifymodal.index.chengBenDengJi}
                            </span>
                            <span className="u-select">
                                <Select
                                    showSearch
                                    style={{ width: '100%' }}
                                    disabled={disabled}
                                    placeholder={I18N.addmodifymodal.index.qingXuanZeChengBen} // 请选择成本等级
                                    optionFilterProp="children"
                                    value={costLevel ? costLevel : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'costLevel')}>
                                    <Option value={1}>
                                        {I18N.addmodifymodal.index.gao}
                                        {/* 高 */}
                                    </Option>
                                    <Option value={2}>
                                        {I18N.addmodifymodal.index.zhong}
                                        {/* 中 */}
                                    </Option>
                                    <Option value={3}>
                                        {I18N.addmodifymodal.index.di}
                                        {/* 低 */}
                                    </Option>
                                </Select>
                            </span>
                        </div>
                        <div className="u-title">
                            {/* 计费管理 */}
                            {I18N.addmodifymodal.index.jiFeiGuanLi}
                        </div>
                        <div className="modal-box">
                            <span className="u-label">
                                <b>*</b>
                                {/* 采购开始日期 */}
                                {I18N.addmodifymodal.index.caiGouKaiShiRi}
                            </span>
                            <span className="u-input">
                                <DatePicker
                                    style={{ width: '100%' }}
                                    allowClear={false}
                                    disabled={disabled}
                                    value={purchaseStartTime ? moment(purchaseStartTime) : undefined}
                                    onChange={(e) => this.changeField(e, 'datePicker', 'purchaseStartTime')}
                                />
                            </span>
                            <span className="u-label">
                                <b>*</b>
                                {/* 采购结束日期 */}
                                {I18N.addmodifymodal.index.caiGouJieShuRi}
                            </span>
                            <span className="u-input">
                                <DatePicker
                                    style={{ width: '100%' }}
                                    allowClear={false}
                                    disabled={disabled}
                                    value={purchaseEndTime ? moment(purchaseEndTime) : undefined}
                                    onChange={(e) => this.changeField(e, 'datePicker', 'purchaseEndTime')}
                                />
                            </span>
                        </div>
                        <div className="modal-box">
                            <span className="u-label">
                                <b>*</b>
                                {/* 计费方式 */}
                                {I18N.addmodifymodal.index.jiFeiFangShi}
                            </span>
                            <span className="u-select">
                                <Select
                                    showSearch
                                    style={{ width: '100%' }}
                                    disabled={disabled}
                                    placeholder={I18N.addmodifymodal.index.qingXuanZeJiFei2} // 请选择计费方式
                                    dropdownMatchSelectWidth={false}
                                    optionFilterProp="children"
                                    value={chargeType ? chargeType : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'chargeType')}>
                                    {allMap &&
                                        allMap.chargeTypeList &&
                                        allMap.chargeTypeList.map((item, index) => {
                                            return (
                                                <Option value={item.code} key={index}>
                                                    {item.name}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </span>
                            <span className="u-label">
                                <b>*</b>
                                {/* 计费类型 */}
                                {I18N.addmodifymodal.index.jiFeiLeiXing}
                            </span>
                            <span className="u-select">
                                <Select
                                    showSearch
                                    style={{ width: '100%' }}
                                    disabled={disabled}
                                    placeholder={I18N.addmodifymodal.index.qingXuanZeJiFei} // 请选择计费类型
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    optionFilterProp="children"
                                    value={chargeMethod ? chargeMethod : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'chargeMethod')}>
                                    {allMap &&
                                        allMap.chargeMethodList &&
                                        allMap.chargeMethodList.map((item, index) => {
                                            return (
                                                <Option value={item.code} key={index}>
                                                    {item.name}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </span>
                        </div>
                        <div className="modal-box">
                            <span className="u-label">
                                <b>*</b>
                                {/* 价格(元) */}
                                {I18N.addmodifymodal.index.jiaGeYuan}
                            </span>
                            <span className="u-input">
                                <Input
                                    placeholder={I18N.addmodifymodal.index.qingShuRu} // 请填写
                                    disabled={disabled}
                                    value={price}
                                    onChange={(e) => this.changeField(e, 'input', 'price')}
                                />
                            </span>
                            <span className="u-label">
                                <b>*</b>
                                {/* 流量上限(条) */}
                                {I18N.addmodifymodal.index.liuLiangShangXianTiao}
                            </span>
                            <span className="u-input">
                                <Input
                                    placeholder={I18N.addmodifymodal.index.qingShuRu} // 请填写
                                    disabled={disabled}
                                    value={rateLimit}
                                    onChange={(e) => this.changeField(e, 'input', 'rateLimit')}
                                />
                            </span>
                        </div>
                        <div className="modal-box">
                            <span className="u-label">
                                {/* 合同编号 */}
                                {I18N.addmodifymodal.index.heTongBianHao}
                            </span>
                            <span className="u-input">
                                <Input
                                    placeholder={I18N.addmodifymodal.index.qingTianXieHeTong} // 请填写合同编号
                                    disabled={disabled}
                                    value={contractCode}
                                    onChange={(e) => this.changeField(e, 'input', 'contractCode')}
                                />
                            </span>
                        </div>
                        <div className="u-title">
                            {/* 数据处理 */}
                            {I18N.addmodifymodal.index.shuJuChuLi}
                        </div>
                        <div className="modal-box">
                            <span className="u-label">
                                {/* 数据缓存期(天) */}
                                {I18N.addmodifymodal.index.shuJuHuanCunQi}
                            </span>
                            <span className="u-input">
                                <Input
                                    placeholder={I18N.addmodifymodal.index.moRenTian} // 默认30天
                                    disabled={disabled}
                                    value={cacheday}
                                    onChange={(e) => this.changeField(e, 'input', 'cacheday')}
                                />
                            </span>
                            <span className="u-label">
                                {/* 数据重试次数 */}
                                {I18N.addmodifymodal.index.shuJuZhongShiCi}
                            </span>
                            <span className="u-input">
                                <Input
                                    placeholder={I18N.addmodifymodal.index.moRenCi} // 默认3次
                                    disabled={disabled}
                                    value={retry}
                                    onChange={(e) => this.changeField(e, 'input', 'retry')}
                                />
                            </span>
                        </div>
                        <div className="modal-box">
                            <span className="u-label">
                                {/* 数据超时时间(ms) */}
                                {I18N.addmodifymodal.index.shuJuChaoShiShi}
                            </span>
                            <span className="u-input">
                                <Input
                                    placeholder={I18N.addmodifymodal.index.moRenMS} // 默认1000ms
                                    disabled={disabled}
                                    value={timeout}
                                    onChange={(e) => this.changeField(e, 'input', 'timeout')}
                                />
                            </span>
                        </div>
                    </Fragment>
                )}
                {current === 1 && (
                    <Fragment>
                        <div className="modal-box mt20">
                            <span className="u-label">
                                <b>*</b>
                                {/* 接口类型(协议) */}
                                {I18N.addmodifymodal.index.jieKouLeiXingXie}
                            </span>
                            <span className="u-select2">
                                <Select
                                    showSearch
                                    style={{ width: '100%' }}
                                    disabled={disabled}
                                    placeholder={I18N.addmodifymodal.index.qingXuanZeJieKou} // 请选择接口类型
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    optionFilterProp="children"
                                    value={methodType ? methodType : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'methodType')}>
                                    {allMap &&
                                        allMap.methodTypeList &&
                                        allMap.methodTypeList.map((item, index) => {
                                            return (
                                                <Option value={item.code} key={index}>
                                                    {item.name}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </span>
                            <span className="u-label2">
                                <b>*</b>
                                {/* url地址 */}
                                {I18N.addmodifymodal.index.uRLDiZhi}
                            </span>
                            <span className="u-select">
                                <Input
                                    placeholder={I18N.addmodifymodal.index.qingTianXieUR} // 请填写url地址
                                    disabled={disabled}
                                    value={url}
                                    onChange={(e) => this.changeField(e, 'input', 'url')}
                                />
                            </span>
                        </div>
                        <div className="modal-box">
                            <span className="u-label">
                                <b>*</b>
                                {/* 调用方式 */}
                                {I18N.addmodifymodal.index.diaoYongFangShi}
                            </span>
                            <span className="u-select2">
                                <Select
                                    showSearch
                                    style={{ width: '100%' }}
                                    disabled={disabled}
                                    placeholder={I18N.addmodifymodal.index.qingXuanZeDiaoYong} // 请选择调用方式
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    optionFilterProp="children"
                                    value={contentType ? contentType : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'contentType')}>
                                    {methodList.map((item, index) => {
                                        return (
                                            <Option value={item.code} key={index}>
                                                {item.code}
                                            </Option>
                                        );
                                    })}
                                </Select>
                            </span>
                            <span className="u-label2">
                                <b>*</b>
                                {/* 返回方式 */}
                                {I18N.addmodifymodal.index.fanHuiFangShi}
                            </span>
                            <span className="u-select">
                                <Select
                                    showSearch
                                    style={{ width: '100%' }}
                                    disabled={disabled}
                                    placeholder={I18N.addmodifymodal.index.qingXuanZeFanHui} // 请选择返回方式
                                    dropdownMatchSelectWidth={false}
                                    optionFilterProp="children"
                                    value={responseType ? responseType : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'responseType')}>
                                    {allMap &&
                                        allMap.responseTypeList &&
                                        allMap.responseTypeList.map((item, index) => {
                                            return (
                                                <Option value={item.dataType} key={index}>
                                                    {item.name}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </span>
                        </div>
                        <div className="modal-box">
                            <span className="u-label">
                                {/* 前置ETL处理器 */}
                                {I18N.addmodifymodal.index.qianZhiETL2}
                            </span>
                            <span className="u-select2">
                                <Select
                                    showSearch
                                    allowClear
                                    style={{ width: '100%' }}
                                    disabled={disabled}
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    placeholder={I18N.addmodifymodal.index.qingXuanZeET} // 请选择ETL处理器
                                    optionFilterProp="children"
                                    value={preEtlHandlerName ? preEtlHandlerName : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'preEtlHandlerName')}
                                    onFocus={this.getEtlList}>
                                    {etlList &&
                                        etlList.map((item, index) => {
                                            let dom = null;
                                            if (item.dataType === 2) {
                                                dom = (
                                                    <Option value={item.name} key={index}>
                                                        {item.displayName}
                                                    </Option>
                                                );
                                            }
                                            return dom;
                                        })}
                                </Select>
                            </span>
                            {/* 前置ETL处理器用于准备服务请求前的数据，如登录校验、入参处理、加解密等 */}
                            <Tooltip title={I18N.addmodifymodal.index.qianZhiETL}>
                                <Icon className="u-icon" type="question-circle" />
                            </Tooltip>
                            <span className="u-label2">
                                <b>*</b>
                                {/* 后置ETL处理器 */}
                                {I18N.addmodifymodal.index.houZhiETL}
                            </span>
                            <span className="u-select">
                                <Select
                                    showSearch
                                    style={{ width: '100%' }}
                                    disabled={disabled}
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    placeholder={I18N.addmodifymodal.index.qingXuanZeET} // 请选择ETL处理器
                                    optionFilterProp="children"
                                    value={postEtlHandlerName ? postEtlHandlerName : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'postEtlHandlerName')}
                                    onFocus={this.getEtlList}>
                                    {etlList &&
                                        etlList.map((item, index) => {
                                            let dom = null;
                                            if (item.dataType === 1 || item.dataType === 3) {
                                                dom = (
                                                    <Option value={item.name} key={index}>
                                                        {item.displayName}
                                                    </Option>
                                                );
                                            }
                                            return dom;
                                        })}
                                </Select>
                            </span>
                            {/* 组装服务返回后的结果，如返回结果解析、加工等 */}
                            <Tooltip title={I18N.addmodifymodal.index.zuZhuangFuWuFan}>
                                <Icon className="u-icon1" type="question-circle" />
                            </Tooltip>
                        </div>
                        {contentType === 'application/json' && (
                            <div className="modal-box">
                                <span className="u-label fl">
                                    {/* 输入处理器模板 */}
                                    {I18N.addmodifymodal.index.shuRuChuLiQi}
                                </span>
                                <span className="u-textarea">
                                    <TextArea
                                        rows={5}
                                        disabled={disabled}
                                        value={inputTemplate}
                                        onChange={(e) => this.changeField(e, 'input', 'inputTemplate')}
                                    />
                                </span>
                            </div>
                        )}
                        {postEtlHandlerName === 'json' && (
                            <div className="modal-box">
                                <span className="u-label fl">
                                    {/* 输出处理器模板 */}
                                    {I18N.addmodifymodal.index.shuChuChuLiQi}
                                </span>
                                <span className="u-textarea">
                                    <TextArea
                                        rows={5}
                                        disabled={disabled}
                                        value={outputTemplate}
                                        onChange={(e) => this.changeField(e, 'input', 'outputTemplate')}
                                    />
                                </span>
                            </div>
                        )}
                        {/* <div className="modal-box">
							<span className="u-label fl">
								{'mock测试'}
							</span>
							<span className="u-textarea">
								<TextArea
									rows={3}
									disabled={disabled}
									value={mockConfig}
									onChange={(e) => this.changeField(e, "input", "mockConfig")}
								/>
							</span>
						</div> */}
                        <div className="modal-box" style={{ padding: '0 8px' }}>
                            <span className="u-label-table">
                                {/* 服务入参 */}
                                {I18N.addmodifymodal.index.fuWuRuCan}
                            </span>
                            <Table
                                size="small"
                                className={modalType === 3 ? 'm-service-table2' : 'm-service-table'}
                                columns={inputColumns}
                                dataSource={inputConfig}
                                rowKey="uuid"
                                pagination={{
                                    current: inputPage.curPage,
                                    pageSize: inputPage.pageSize,
                                    onChange: this.inputOnChange,
                                    showSizeChanger: true,
                                    pageSizeOptions: ['5', '10', '20', '30'],
                                    onShowSizeChange: this.inputOnChange
                                }}
                            />
                            {modalType !== 3 && (
                                <div className="u-add" onClick={this.addInputData}>
                                    <Icon type="plus" />
                                    {/* 添加 */}
                                    {I18N.addmodifymodal.index.xinZeng}
                                </div>
                            )}
                        </div>
                        <div className="modal-box" style={{ padding: '0 8px' }}>
                            <span className="u-label-table">
                                {/* 服务出参 */}
                                {I18N.addmodifymodal.index.fuWuChuCan}
                            </span>
                            <Table
                                size="small"
                                className={modalType === 3 ? 'm-service-table2' : 'm-service-table'}
                                columns={outputColumns}
                                dataSource={outputConfig}
                                rowKey="uuid"
                                pagination={{
                                    current: outputPage.curPage,
                                    pageSize: outputPage.pageSize,
                                    onChange: this.outputOnChange,
                                    showSizeChanger: true,
                                    pageSizeOptions: ['5', '10', '20', '30'],
                                    onShowSizeChange: this.outputOnChange
                                }}
                            />
                            {modalType !== 3 && (
                                <div className="u-add" onClick={this.addOutputData}>
                                    <Icon type="plus" />
                                    {/* 添加 */}
                                    {I18N.addmodifymodal.index.xinZeng}
                                </div>
                            )}
                        </div>
                    </Fragment>
                )}
            </Modal>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    dataServiceListStore: state.dataServiceList
}))(AddModifyModal);
