/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 供应商列表
 */

import I18N from '@/utils/I18N';
import React, { PureComponent, Suspense } from 'react';
import { connect } from 'dva';
import { HandleIcon, Button, Table, Pagination, Popconfirm, Input, message, Tooltip, Select, TableContainer, Ellipsis, Icon } from 'tntd';
import { supplierListAPI, referenceAPI } from '@/services';
import { ReferenceDrawer, ReferenceCheck, ReferenceOnlineCheck } from '@tddc/reference';
import { checkFunctionHasPermission } from '@/utils/permission';
import NoPermission from '@/components/NoPermission';

const { Option } = Select;
const AddModifyModal = React.lazy(() => import('./Inner/AddModifyModal'));

class SupplierList extends PureComponent {
    state = {
        referenceDrawerData: null
    };

    componentDidMount() {
        if (checkFunctionHasPermission('TZ0201', 'query')) {
            this.search();
        }
    }

    componentWillUnmount() {
        this.props.dispatch({
            type: 'supplierList/reset'
        });
    }

    // 查询
    search = (curPage, pageSize) => {
        const { dispatch, supplierListStore } = this.props;
        const { searchParams } = supplierListStore;
        const status = this.props.location.search === '?status=1' ? '1' : undefined;
        if (status) {
            this.changeField(status, 'status');
            this.props.history.push(this.props.location.pathname);
        }
        dispatch({
            type: 'supplierList/getList',
            payload: {
                curPage: curPage ? curPage : 1,
                pageSize: pageSize ? pageSize : searchParams.pageSize
            }
        });
    };

    // 分页
    paginationOnChange(curPage, pageSize) {
        this.search(curPage, pageSize);
    }

    // 新增
    add = () => {
        const { dispatch } = this.props;
        dispatch({
            type: 'supplierList/setAttrValue',
            payload: {
                dialogShow: {
                    addEditModal: true
                },
                modalType: 1
            }
        });
    };

    // 修改
    modify(record) {
        const { displayName, name, status, type, contractCode, creator, gmtCreate, gmtModify, operator } = record;
        const { dispatch } = this.props;
        dispatch({
            type: 'supplierList/setAttrValue',
            payload: {
                dialogShow: {
                    addEditModal: true
                },
                modalType: 2,
                updateId: record.uuid,
                dialogData: {
                    addEditModalData: {
                        displayName, // 供应商名称
                        name, // 供应商标识
                        status, // 供应商合作状态 1正常，2停止
                        type, // 供应商类型
                        contractCode, // 合同号
                        creator, // 创建人
                        gmtCreate, // 创建时间
                        gmtModify, //修改时间
                        operator //修改人
                    }
                }
            }
        });
    }
    // 切换状态
    async changeField(e, field, doSearch) {
        const { dispatch } = this.props;
        let obj = {};
        obj[field] = e || null;
        await dispatch({
            type: 'supplierList/setAttrValue',
            payload: {
                searchParams: {
                    ...obj
                }
            }
        });
        if (doSearch) {
            this.search();
        }
    }
    // 查看
    look(record) {
        const { displayName, name, status, type, contractCode, creator, gmtCreate, gmtModify, operator } = record;
        const { dispatch } = this.props;
        dispatch({
            type: 'supplierList/setAttrValue',
            payload: {
                dialogShow: {
                    addEditModal: true
                },
                modalType: 3,
                dialogData: {
                    addEditModalData: {
                        displayName, // 供应商名称
                        name, // 供应商标识
                        status, // 供应商合作状态 1正常，2停止
                        type, // 供应商类型
                        contractCode, // 合同号
                        creator, // 创建人
                        gmtCreate, // 创建时间
                        gmtModify, //修改时间
                        operator //修改人
                    }
                }
            }
        });
    }

    // 删除
    delete(record) {
        supplierListAPI.deleteData({ uuid: record.uuid }).then((res) => {
            if (res && res.success) {
                message.success(res.message);
                const { dispatch, supplierListStore } = this.props;
                const { searchParams, tableList } = supplierListStore;
                let { curPage, pageSize } = searchParams;
                if (tableList.length === 1 && curPage > 1) curPage = curPage - 1;
                dispatch({
                    type: 'supplierList/getList',
                    payload: {
                        curPage,
                        pageSize
                    }
                });
                dispatch({
                    type: 'global/getAllProvider'
                });
            } else {
                message.error(res.message);
            }
        });
    }

    checkDelete(record) {
        ReferenceCheck({
            strongMsg: I18N.supplierlist.cunZaiQiangYinYong,
            rq: () =>
                referenceAPI.checkComponentReference({
                    componentType: 'DATASOURCE_PARTNER',
                    componentId: record.uuid
                })
        }).then(() => {
            this.delete(record);
        });
    }

    // 合同管理
    goContract(record) {
        const { history } = this.props;
        let path = `/handle/supplierManagement/supplierList/contractList?uuid=${record.uuid}&name=${record.displayName}`;
        history.push(path);
    }

    // 处理快速搜索值
    handleQuickSearchVal(e) {
        const { supplierListStore, dispatch } = this.props;
        let { searchParams } = supplierListStore;
        searchParams.displayName = e.target.value;
        dispatch({
            type: 'supplierList/setAttrValue',
            payload: {
                searchParams
            }
        });
    }

    render() {
        const { supplierListStore, globalStore } = this.props;
        const { referenceDrawerData } = this.state;
        const { allMap } = globalStore;
        const { tableList, searchParams, total, loading, dialogShow } = supplierListStore;
        const { status } = searchParams;

        const columns = [
            {
                title: I18N.supplierlist.heZuoFangMingCheng, // 供应商名称
                dataIndex: 'displayName',
                key: 'displayName',
                width: 190,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 15) {
                        dom = <Tooltip title={text}>{text.substr(0, 15)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.supplierlist.heZuoZhuangTai, // 合作状态
                dataIndex: 'status',
                key: 'status',
                width: 90,
                render: (text) => {
                    const map = {
                        1: I18N.supplierlist.zhengChang,
                        2: I18N.supplierlist.tingYong
                    };
                    return map[text];
                }
            },
            {
                title: I18N.supplierlist.chuangJianShiJian, // 创建时间
                dataIndex: 'gmtCreate',
                key: 'gmtCreate',
                width: 190
            },
            {
                title: I18N.supplierlist.chuangJianRen, // 创建人
                dataIndex: 'creator',
                key: 'creator',
                width: 120,
                render: (text) => {
                    return <Ellipsis title={text} />;
                }
            },
            {
                title: I18N.supplierlist.xiuGaiShiJian, // 修改时间
                dataIndex: 'gmtModify',
                key: 'gmtModify',
                width: 190
            },
            {
                title: I18N.supplierlist.xiuGaiRen, // 修改人
                dataIndex: 'operator',
                key: 'operator',
                width: 120,
                render: (text) => {
                    return <Ellipsis title={text} />;
                }
            },
            {
                title: I18N.supplierlist.caoZuo, // 操作
                dataIndex: 'operate',
                key: 'operate',
                fixed: 'right',
                width: 180,
                render: (text, record) => {
                    let dom = (
                        <HandleIcon>
                            {checkFunctionHasPermission('TZ0201', 'modify') && (
                                <HandleIcon.Item title={I18N.supplierlist.xiuGai}>
                                    <Icon
                                        type="form"
                                        onClick={() => {
                                            if (!checkFunctionHasPermission('TZ0201', 'modify')) {
                                                return message.info(I18N.supplierlist.wuQuanXianCaoZuo); // 暂无权限
                                            }
                                            this.modify(record);
                                        }}
                                    />
                                </HandleIcon.Item>
                            )}
                            {checkFunctionHasPermission('TZ0201', 'look') && (
                                <HandleIcon.Item title={I18N.supplierlist.chaKan}>
                                    <Icon
                                        type="profile"
                                        onClick={() => {
                                            if (!checkFunctionHasPermission('TZ0201', 'look')) {
                                                return message.info(I18N.supplierlist.wuQuanXianCaoZuo); // 暂无权限
                                            }
                                            this.look(record);
                                        }}
                                    />
                                </HandleIcon.Item>
                            )}

                            {!allMap?.isIntegrationTG && checkFunctionHasPermission('TZ0201', 'contract') && (
                                <HandleIcon.Item title={I18N.supplierlist.heTong}>
                                    <Icon
                                        type="contract"
                                        onClick={() => {
                                            if (!checkFunctionHasPermission('TZ0201', 'contract')) {
                                                return message.info(I18N.supplierlist.wuQuanXianCaoZuo); // 暂无权限
                                            }
                                            this.goContract(record);
                                        }}
                                    />
                                </HandleIcon.Item>
                            )}
                            {checkFunctionHasPermission('TZ0201', 'delete') && (
                                <HandleIcon.Item title={I18N.supplierlist.shanChu}>
                                    <Popconfirm
                                        title={I18N.supplierlist.queDingShanChuCi} // 确定删除此数据?
                                        placement="topRight"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                        }}
                                        onConfirm={() => this.checkDelete(record)}>
                                        <Icon type="delete" />
                                    </Popconfirm>
                                </HandleIcon.Item>
                            )}
                            <HandleIcon.Item title={I18N.supplierlist.yinYongGuanXi}>
                                <Icon
                                    type="correlation"
                                    onClick={() => {
                                        this.setState({
                                            referenceDrawerData: record
                                        });
                                    }}
                                />
                            </HandleIcon.Item>
                        </HandleIcon>
                    );
                    return dom;
                }
            }
        ];

        if (!checkFunctionHasPermission('TZ0201', 'query')) {
            return <NoPermission />;
        }

        return (
            <>
                <div className="page-global-body tnt-querylistscene-content">
                    <div className="page-global-search">
                        <div className="item">
                            <Input.Search
                                style={{ width: 200 }}
                                placeholder={I18N.supplierlist.qingShuRuHeZuo} // 请输入供应商名称
                                onChange={(e) => this.handleQuickSearchVal(e)}
                                value={searchParams.displayName}
                                onPressEnter={() => this.search()}
                                onSearch={() => this.search()}
                            />
                        </div>
                        <div className="item">
                            <Select
                                style={{ width: 200 }}
                                showSearch
                                allowClear
                                placeholder={I18N.supplierlist.qingXuanZeHeZuo}
                                dropdownMatchSelectWidth={false}
                                optionFilterProp="children"
                                value={status ? status : undefined}
                                onChange={(e) => {
                                    this.changeField(e, 'status', true);
                                }}>
                                <Option key="1" value="1">
                                    {I18N.supplierlist.zhengChang}
                                </Option>
                                <Option key="2" value="2">
                                    {I18N.supplierlist.tingYong}
                                </Option>
                            </Select>
                        </div>
                        {/* <div className="item">
                            <Button type="primary" onClick={() => this.search()}>
                                {I18N.supplierlist.chaXun}
                            </Button>
                        </div> */}
                        {checkFunctionHasPermission('TZ0201', 'add') && (
                            <div className="item right">
                                <Button type="primary" icon="plus" onClick={this.add}>
                                    {I18N.supplierlist.xinZeng}
                                </Button>
                            </div>
                        )}
                    </div>
                    <div className="page-global-body-in">
                        <div className="page-global-body-main">
                            <Table
                                scroll={{ x: 1850 }}
                                rowKey={(record) => record.uuid}
                                className="table-card-body"
                                columns={columns}
                                dataSource={tableList}
                                pagination={false}
                                loading={loading}
                            />
                            <div className="page-global-body-pagination">
                                <span className="ml20">{I18N.template(I18N.supplierlist.gongTOTA, { val1: total })}</span>
                                <Pagination
                                    showSizeChanger
                                    showQuickJumper
                                    current={searchParams.curPage}
                                    pageSize={searchParams.pageSize}
                                    total={total}
                                    onChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                    onShowSizeChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                />
                            </div>
                        </div>
                    </div>
                </div>
                {dialogShow.addEditModal && (
                    <Suspense fallback={null}>
                        <AddModifyModal />
                    </Suspense>
                )}
                <ReferenceDrawer
                    title={referenceDrawerData ? `${referenceDrawerData?.displayName}` : ''}
                    visible={!!referenceDrawerData}
                    onClose={() => {
                        this.setState({
                            referenceDrawerData: null
                        });
                    }}
                    fetchReference={() => {
                        return referenceAPI.getRelationResult({
                            componentType: 'DATASOURCE_PARTNER',
                            componentId: referenceDrawerData.uuid
                        });
                    }}
                />
            </>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    supplierListStore: state.supplierList
}))(TableContainer(SupplierList));
