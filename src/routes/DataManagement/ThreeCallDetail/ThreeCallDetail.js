/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 三方调用量明细
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import { Table, Pagination, Select, DatePicker, Tooltip, message, Icon, Modal, Input, Ellipsis, TableContainer, HandleIcon } from 'tntd';
import { parse } from 'query-string';
import { isJSON } from '@/utils/isJSON';
import { checkFunctionHasPermission } from '@/utils/permission';
import NoPermission from '@/components/NoPermission';
import { dataServiceListAPI, threeCallDetailAPI, contractListAPI, supplierListAPI } from '@/services';
import { DateRanges } from '@/constants';
import SearchDrawer from './SearchDrawer';

const Option = Select.Option;
const { RangePicker } = DatePicker;
const dateFormat = 'YYYY-MM-DD';
const TextArea = Input.TextArea;
import './index.less';
class ThreeCallDetail extends PureComponent {
    constructor(props) {
        super(props);
        // 外部跳转带参数进入
        const { dispatch, location } = props;
        let { state, search } = location;
        // url 传入状态
        if (search) {
            const parsedSearch = parse(search);

            if (parsedSearch.state) {
                try {
                    state = JSON.parse(parsedSearch.state);
                } catch (error) {
                    console.error(error);
                }
            }
        }

        if (state) {
            let obj = {};
            if (state.serviceCode) obj['serviceCode'] = state.serviceCode; // 三方服务code
            if (state.date) {
                obj['date'] = state.date; // 时间戳
                const st = state.date[0];
                const et = state.date[1];
                if ((et - st) / (1000 * 60 * 60 * 24) > 89) {
                    // 系统一次最多支持查询90天，已自动帮您截取所选范围的近90天
                    message.warning(I18N.threecalldetail.xiTongYiCiZui2);
                    obj['date'] = [moment(et).add(-89, 'days').valueOf(), moment(et).valueOf()];
                }
            }
            if (state.returnResult) obj['returnResult'] = state.returnResult; // 返回结果
            if (state.partnerId) obj['partnerId'] = state.partnerId; // 合作方id
            if (state.contractCode) obj['contractCode'] = state.contractCode; // 合同编号
            if (state.searchResult) obj['searchResult'] = state.searchResult; // 查询结果
            if (state.sequenceId) obj['sequenceId'] = state.sequenceId; // 流水号
            if (state.searchWhere) obj['searchWhere'] = state.searchWhere; // 入参系统字段名称
            if (state.searchValue) obj['searchValue'] = state.searchValue; // 入参系统字段的值
            if (state.errorCode) obj['errorCode'] = state.errorCode; // 错误码
            if (state.serviceType) obj['serviceType'] = state.serviceType; // 数据类型
            if (state.invokeSource) obj['invokeSource'] = state.invokeSource; // 调用数据来源
            if (state.docId) obj['docId'] = state.docId; // 隐藏ID
            dispatch({
                type: 'threeCallDetail/setAttrValue',
                payload: {
                    searchParams: {
                        ...obj
                    }
                }
            });
        }
        this.state = {
            threeServiceList: [],
            partnerList: [],
            contractList: []
        };
    }

    componentDidMount() {
        this.timer = setInterval(() => {
            const { globalStore, location, threeCallDetailStore } = this.props;
            const {
                searchParams: { partnerId }
            } = threeCallDetailStore;
            const { menuTreeReady } = globalStore;
            const { state } = location;
            if (menuTreeReady) {
                clearInterval(this.timer);
                if (checkFunctionHasPermission('TZ0501', 'query')) {
                    this.getPartnerList();
                    if (partnerId) {
                        this.getContractList(partnerId);
                    }
                    this.search();
                    if (state && state.serviceType) {
                        this.getServiceList(state.serviceType);
                    } else {
                        this.getServiceList();
                    }
                }
            }
        }, 100);
    }

    componentDidUpdate(preProps) {
        const { dispatch, globalStore } = this.props;
        const appName = preProps.globalStore.currentApp.name;
        const nextAppName = globalStore.currentApp.name;
        if (appName !== nextAppName) {
            dispatch({
                type: 'threeCallDetail/setAttrValue',
                payload: {
                    searchParams: {
                        serviceCode: null, // 三方服务接口名称
                        returnResult: null, // 返回结果
                        partnerId: null, // 合作方id
                        contractCode: null, // 合同编号
                        searchResult: null, // 查询结果
                        errorCode: null, // 错误码
                        sequenceId: null, // 调用唯一标识码
                        searchWhere: null, // 入参字段
                        searchValue: null, // 入参值
                        serviceType: null, // 数据类型
                        invokeSource: null // 调用数据来源
                    }
                }
            });
            this.getServiceList();
            this.getPartnerList();
            this.search();
        }
    }

    componentWillUnmount() {
        this.clearSearchParam();
    }

    getServiceList = (dataType) => {
        const { currentApp } = this.props.globalStore;
        dataServiceListAPI.getListAll2({ dataType, appNames: currentApp?.name }).then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                this.setState({
                    threeServiceList: res.data.contents ? res.data.contents : []
                });
            } else {
                message.error(res.msg || res?.message);
            }
        });
    };

    // 根据应用获取合作方列表
    getPartnerList = () => {
        const { globalStore } = this.props;
        const { currentApp } = globalStore;
        supplierListAPI.getList({ appNames: currentApp?.name }).then((res) => {
            this.setState({
                partnerList: res?.data?.contents || []
            });
        });
    };

    // 根据合作方获取合同列表
    getContractList = (e) => {
        contractListAPI.getListAll({ providerUuid: e }).then((res) => {
            this.setState({
                contractList: res?.data || []
            });
        });
    };

    // 查询
    search = (curPage, pageSize) => {
        const { dispatch, threeCallDetailStore } = this.props;
        const { searchParams } = threeCallDetailStore;

        dispatch({
            type: 'threeCallDetail/getList',
            payload: {
                curPage: curPage ? curPage : 1,
                pageSize: pageSize ? pageSize : searchParams.pageSize
            }
        });
    };

    // 分页
    paginationOnChange(curPage, pageSize) {
        this.search(curPage, pageSize);
    }

    showModal(record) {
        threeCallDetailAPI
            .getDetail({ sequenceId: record.sequenceId, recordTime: record.recordTime, invokeSequenceId: record.invokeSequenceId })
            .then((res) => {
                if (res && res.success) {
                    var inputParams = '';
                    var outputParams = '';
                    if (res.data && res.data.inputParams) {
                        inputParams = (' ' + res.data.inputParams).slice(1);
                        if (isJSON(inputParams)) {
                            inputParams = JSON.parse(inputParams);
                            inputParams = JSON.stringify(inputParams, null, 2);
                        }
                    }

                    if (res.data && res.data.outputParams) {
                        outputParams = (' ' + res.data.outputParams).slice(1);
                        if (isJSON(outputParams)) {
                            outputParams = JSON.parse(outputParams);
                            outputParams = JSON.stringify(outputParams, null, 2);
                        }
                    }

                    if (res.data && (res.data.inputParams || res.data.outputParams)) {
                        Modal.info({
                            title: I18N.threecalldetail.mingXiXinXi,
                            icon: null,
                            content: (
                                <div className="modal-detail">
                                    <div className="modal-textarea-top modal-textarea">
                                        <span>{I18N.threecalldetail.ruCan}</span>
                                        <TextArea value={inputParams} disabled={true} />
                                    </div>
                                    <div className="modal-textarea">
                                        <span>{I18N.threecalldetail.chuCan}</span>
                                        <TextArea value={outputParams} disabled={true} />
                                    </div>
                                </div>
                            ),
                            width: 500,
                            okText: I18N.threecalldetail.guanBi,
                            onOk() {}
                        });
                    } else {
                        message.warning(I18N.threecalldetail.weiChaDaoXiangGuan);
                    }
                }
            });
    }

    // 改变参数
    changeField(e, type, field) {
        const { dispatch } = this.props;
        let val = null;
        let obj = {};
        if (type === 'datePicker' && e.length > 0) {
            const st = e[0].valueOf();
            const et = e[1].valueOf();
            const rangeDay = (et - st) / 1000 / 60 / 60 / 24;
            if (rangeDay > 89) return message.warning(I18N.threecalldetail.xiTongYiCiZui); // 系统一次最多支持查询90天
            val = [e[0].valueOf(), e[1].valueOf()];
        }
        if (type === 'select') val = e;
        if (type === 'input') val = e.target.value;
        if (!e) val = null;
        if (field === 'serviceType') {
            this.getServiceList(val);
            obj.serviceCode = null;
        }
        obj[field] = val;

        dispatch({
            type: 'threeCallDetail/setAttrValue',
            payload: {
                searchParams: {
                    ...obj
                }
            }
        });
        this.search();
    }

    clearSearchParam = () => {
        const { dispatch } = this.props;
        dispatch({
            type: 'threeCallDetail/setAttrValue',
            payload: {
                searchParams: {
                    curPage: 1,
                    pageSize: 10,
                    date: [moment().valueOf(), moment().valueOf()],
                    serviceCode: null, // 三方服务接口名称
                    returnResult: null, // 返回结果
                    partnerId: null, // 合作方id
                    contractCode: null, // 合同编号
                    searchResult: null, // 查询结果
                    errorCode: null, // 错误码
                    sequenceId: null, // 调用唯一标识码
                    channelSequenceId: null,
                    searchWhere: null, // 入参字段
                    searchValue: null, // 入参值
                    serviceType: null,
                    invokeSource: null, // 调用数据来源
                    docId: null
                }
            }
        });
        this.getServiceList();
        this.search();
    };

    render() {
        const { threeServiceList, partnerList, contractList } = this.state;
        const { threeCallDetailStore, globalStore } = this.props;
        const { menuTreeReady, allMap } = globalStore;
        const { tableList, searchParams, total, loading } = threeCallDetailStore;
        const { date, serviceCode, serviceType } = searchParams;

        const columns = [
            {
                title: I18N.threecalldetail.diaoYongWeiYiBiao, // 调用唯一标识码
                dataIndex: 'sequenceId',
                key: 'sequenceId',
                width: 200,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 20) {
                        dom = <Tooltip title={text}>{text.substr(0, 15)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.threecalldetail.yeWuWeiYiLiu,
                dataIndex: 'channelSequenceId',
                width: 180,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 15) {
                        dom = <Tooltip title={text}>{text.substr(0, 15)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.threecalldetail.shuJuYuanFuWu, // 三方服务接口名称
                dataIndex: 'serviceName',
                key: 'serviceName',
                width: 160,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Tooltip title={text}>{text.substr(0, 10)}...</Tooltip>;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.threecalldetail.shuJuLeiXingLai, // 数据类型来源
                dataIndex: 'invokeSource',
                width: 160,
                key: 'invokeSource'
            },
            {
                title: I18N.threecalldetail.shuJuLeiXing, // 数据类型
                dataIndex: 'serviceTypeName',
                width: 120,
                key: 'serviceTypeName'
            },
            // {
            // 	title: '入参', // 入参
            // 	dataIndex: "inputParams",
            // 	key: "inputParams",
            // 	width: 160,
            // 	render: (text) => {
            // 		let dom = (
            // 			text
            // 				? <MessageBox content={text} placement="left">
            // 					<span className="u-ellipsis">{text}</span>
            // 				</MessageBox>
            // 				: "--"
            // 		);
            // 		return dom;
            // 	}
            // },
            // {
            // 	title: '出参', // 出参
            // 	dataIndex: "outputParams",
            // 	key: "outputParams",
            // 	width: 160,
            // 	render: (text) => {
            // 		let dom = (
            // 			text
            // 				? <MessageBox content={text} placement="left">
            // 					<span className="u-ellipsis">{text}</span>
            // 				</MessageBox>
            // 				: "--"
            // 		);
            // 		return dom;
            // 	}
            // },
            {
                title: I18N.threecalldetail.fanHuiJieGuo, // 返回结果
                dataIndex: 'responseResult',
                key: 'responseResult',
                width: 120,
                render: (text, record) => {
                    let dom = (
                        <Tooltip title={record.msg}>
                            <a>{text}</a>
                        </Tooltip>
                    );
                    return text !== I18N.threecalldetail.shiBai && text !== 'Failed' ? text : dom;
                }
            },
            {
                title: I18N.threecalldetail.heZuoFangMingCheng,
                dataIndex: 'partnerName',
                width: 120,
                render: (_, record) => <Ellipsis widthLimit={100} title={record.partnerName} lines={2} />
            },
            ...(!allMap.isIntegrationTG
                ? [
                      {
                          title: I18N.threecalldetail.heTongMingCheng,
                          dataIndex: 'contractName',
                          width: 120,
                          render: (text, record) => {
                              let { contractVersion } = record;
                              if (!text) return null;
                              return <Ellipsis widthLimit={100} title={`${text}【V${contractVersion}】`} />;
                          }
                      }
                  ]
                : []),
            {
                title: I18N.threecalldetail.chaXunJieGuo, // 查询结果
                dataIndex: 'searchResult',
                key: 'searchResult',
                width: 130
            },
            {
                title: I18N.threecalldetail.qingQiuShiJian, // 请求时间
                dataIndex: 'requestTime',
                key: 'requestTime',
                width: 185,
                render: (text) => {
                    let dom = (
                        <div>
                            <div>{text ? text : '--'}</div>
                        </div>
                    );
                    return dom;
                }
            },
            {
                title: I18N.threecalldetail.shiFouBenDi,
                dataIndex: 'cachedName',
                width: 120
            },
            {
                title: I18N.threecalldetail.haoShiMS, // 耗时(ms)
                dataIndex: 'cost',
                key: 'cost',
                width: 100
            },
            {
                title: I18N.threecalldetail.caoZuo, // 耗时(ms)
                dataIndex: 'action',
                fixed: 'right',
                width: 120,
                render: (text, record) => {
                    // return <a onClick={() => this.showModal(record)}>{I18N.threecalldetail.chaKanMingXi}</a>;
                    return (
                        <HandleIcon>
                            <HandleIcon.Item title={I18N.threecalldetail.chaKanMingXi}>
                                <Icon
                                    type="profile"
                                    onClick={() => {
                                        this.showModal(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        </HandleIcon>
                    );
                }
            }
        ];

        let innerCount = 0;
        let list = [
            'returnResult',
            'partnerId',
            'contractCode',
            'searchResult',
            'errorCode',
            'sequenceId',
            'searchWhere',
            'searchValue',
            'invokeSource'
        ];
        list.forEach((item) => {
            if (searchParams[item]) innerCount += 1;
        });
 
        return (
            <div>
                {menuTreeReady && checkFunctionHasPermission('TZ0501', 'query') && (
                    <div className="page-global-body">
                        <div className="page-global-search">
                            <div className="item" style={{ marginLeft: '0' }}>
                                <RangePicker
                                    className="middle-calendar-picker"
                                    allowClear={false}
                                    value={date ? [moment(date[0]), moment(date[1])] : null}
                                    onChange={(e) => this.changeField(e, 'datePicker', 'date')}
                                    format={dateFormat}
                                    ranges={DateRanges}
                                />
                                {/* 系统一次最多支持查询90天 */}
                                <Tooltip title={I18N.threecalldetail.xiTongYiCiZui}>
                                    <Icon type="question-circle" style={{ fontSize: '16px', marginLeft: '5px' }} />
                                </Tooltip>
                            </div>
                            <div className="item">
                                <Select
                                    showSearch
                                    allowClear
                                    style={{ width: '160px' }}
                                    placeholder={I18N.threecalldetail.qingXuanZeShuJu2} // 数据类型
                                    dropdownMatchSelectWidth={false}
                                    optionFilterProp="children"
                                    value={serviceType ? serviceType : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'serviceType')}>
                                    {allMap &&
                                        allMap.serviceTypeList.map((item, index) => {
                                            return (
                                                <Option value={item.dataType} key={index}>
                                                    {item.name}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </div>
                            <div className="item">
                                <Select
                                    showSearch
                                    allowClear
                                    style={{ width: '320px' }}
                                    placeholder={I18N.threecalldetail.qingXuanZeShuJu} // 三方服务接口名称
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    optionFilterProp="children"
                                    value={serviceCode ? serviceCode : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'serviceCode')}>
                                    {threeServiceList.map((item, index) => {
                                        return (
                                            <Option value={item.name} key={index}>
                                                {item.displayName}
                                            </Option>
                                        );
                                    })}
                                </Select>
                            </div>
                            <div className="item">
                                <SearchDrawer
                                    innerCount={innerCount}
                                    partnerList={partnerList}
                                    contractList={contractList}
                                    getContractList={this.getContractList}
                                    onClear={() => this.clearSearchParam()}
                                    onSearch={() => this.search()}
                                />
                            </div>
                        </div>
                        <div className="page-global-body-in">
                            <div className="page-global-body-main">
                                <Table
                                    rowKey={(record) => record.sequenceId}
                                    className="table-card-body"
                                    columns={columns}
                                    dataSource={tableList}
                                    pagination={false}
                                    loading={loading}
                                    scroll={{ x: 2000 }}
                                />
                                <div className="page-global-body-pagination">
                                    <span className="ml20">{I18N.template(I18N.threecalldetail.gongTOTA, { val1: total })}</span>
                                    <Pagination
                                        showSizeChanger
                                        showQuickJumper
                                        current={searchParams.curPage}
                                        pageSize={searchParams.pageSize}
                                        total={total}
                                        onChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                        onShowSizeChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                )}
                {menuTreeReady && !checkFunctionHasPermission('TZ0501', 'query') && <NoPermission />}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    threeCallDetailStore: state.threeCallDetail
}))(TableContainer(ThreeCallDetail));
