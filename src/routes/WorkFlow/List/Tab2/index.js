import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { connect } from 'dva';
import { cloneDeep } from 'lodash';
import { Tag, Button, Tooltip, message, Popconfirm, QueryListScene, Ellipsis, HandleIcon, Icon } from 'tntd';
// import history from '@/utils/history';
import { colorRgb } from '@/utils/utils';
import QueryListWrapper from '@/components/QueryListWrapper';
import { statusColorMap } from '@/constants/workflow';
import service from '../../service';
import { ReferenceDrawer, ReferenceCheck, ReferenceOnlineCheck } from '@tddc/reference';
import { referenceAPI } from '@/services';
import AddModify from './Modal/AddModify';
import ImportWorkFlowModal from './Modal/ImportWorkFlow';
import Publish from './Modal/Publish';

import otp from './otp';

const { QueryForm, QueryList, Field, createActions } = QueryListScene;
const actions = createActions();

const Tab1 = (props) => {
    const { areaType, history, globalStore, setAssignModalData, setPriviligeVisible, priviligeVisible } = props;
    const { orgList, appList, currentApp, currentOrgCode, allOrgList, allAppList } = globalStore;
    const [addModifyData, setAddModifyData] = useState();
    const [importWorkFlow, setImportWorkFlow] = useState();
    const [statusDict, setStatusDict] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [publishData, setPublishData] = useState();
    const [referenceDrawerData, setReferenceDrawerData] = useState(null);

    useEffect(() => {
        service.statusDict().then((res) => {
            setStatusDict(res?.data?.workflowStatus || {});
        });
    }, []);

    useEffect(() => {
        if (areaType && areaType === '1') {
            actions.search();
        }
    }, [areaType]);

    useEffect(() => {
        if (!priviligeVisible) {
            actions.search({ current: 1 });
        }
    }, [priviligeVisible]);

    // 查询
    const query = (params) => {
        const { pageSize = 10, total, current, obj, ...rest } = params;
        const dParams = {
            appCode: currentApp.name || 'all',
            orgCode: currentOrgCode,
            areaType,
            curPage: current,
            pageSize,
            ...(obj || {}),
            ...rest
        };

        return service.getWorkFlowList(dParams).then(({ data }) => ({
            ...data,
            data: data.contents || [],
            current: data.curPage,
            pageSize: data.pageSize
        }));
    };

    // 删除
    const del = ({ uuid, code }) => {
        ReferenceCheck({
            strongMsg: I18N.tab2.index.cunZaiQiangYinYong,
            rq: () =>
                referenceAPI.checkComponentReference({
                    componentType: 'DATASOURCE_WORKFLOW',
                    componentId: code
                })
        }).then(() => {
            service.workFlowDel({ uuid }).then((res) => {
                if (res?.success) {
                    message.success(res?.message || I18N.tab2.index.shanChuChengGong);
                    actions.search({ current: 1 });
                } else {
                    message.error(res?.message || I18N.tab2.index.shanChuShiBai);
                }
            });
        });
    };
    const goToAssign = (record)=> {
        let data = cloneDeep(record);
        service.getAuthorize({bizId:record.uuid}).then((v)=>{
            data.appCodes = v?.data?.appCodes;
            data.orgCodes = v?.data?.orgCodes;
            setAssignModalData(data);
            setPriviligeVisible(true);
        })
    }
    const columns = [
        {
            title: I18N.tab2.index.gongZuoLiuMingCheng,
            dataIndex: 'displayName',
            width: 300,
            ellipsis: true,
            render: (displayName, record) => {
                const color = statusColorMap[record?.status]?.color || '#126BFB';
                return (
                    <Ellipsis
                        key={displayName}
                        prefix={
                            <>
                                <Tag color="tnt-purple">V{record?.version}</Tag>
                                <Tag color={colorRgb(color, 0.1)} style={{ border: `1px solid ${color}`, color }}>
                                    {statusDict?.[record?.status]}
                                </Tag>
                            </>
                        }
                        title={displayName || '- -'}
                    />
                );
            }
        },
        {
            title: I18N.tab2.index.gongZuoLiuBiaoZhi,
            dataIndex: 'code',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.tab2.index.suoShuJiGou,
            width: 120,
            dataIndex: 'orgCode',
            render: (text, row) => {
                let orgName = allOrgList?.find((v)=>v.code === text)?.name;
                return <Ellipsis title={orgName || '- -'} />;
            }
        },
        {
            title: I18N.addmodify.index.quDao,
            width: 120,
            dataIndex: 'appCode',
            render: (text, row) => {
                let appName = allAppList?.find((v)=>v.name === text)?.displayName
                return <Ellipsis title={appName || '- -'} />;
            }
        },
        {
            title: I18N.tab2.index.miaoShu,
            dataIndex: 'description',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.tab2.index.chuangJianRen,
            dataIndex: 'creator',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.tab2.index.chuangJianShiJian,
            dataIndex: 'gmtCreate',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.tab2.index.xiuGaiRen,
            dataIndex: 'operator',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.tab2.index.xiuGaiShiJian,
            dataIndex: 'gmtModify',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.tab2.index.caoZuo,
            width: 200,
            fixed: 'right',
            render: (record) => {
                return (
                    <HandleIcon>
                        <HandleIcon.Item title={I18N.tab2.index.bianPai}>
                            <Icon
                                type="bianpai"
                                onClick={() => {
                                    history.push(`/handle/workflow/arrange/opera/edit?uuid=${record.uuid}&currentTab=${areaType}&appCode=${record.appCode}&orgCode=${record.orgCode}`);
                                }}
                            />
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.tab2.index.chaKan}>
                            <Icon
                                type="profile"
                                onClick={() => {
                                    history.push(`/handle/workflow/arrange/opera/view?uuid=${record.uuid}&currentTab=${areaType}`);
                                }}
                            />
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.tab2.index.xiuGai}>
                            <Icon
                                type="form"
                                onClick={() => {
                                    setAddModifyData({
                                        ...record,
                                        titleType: 'update',
                                        title: I18N.tab2.index.xiuGai
                                    });
                                }}
                            />
                        </HandleIcon.Item>
                        {record?.status !== 3 && (
                            <HandleIcon.Item title={I18N.tab2.index.shangXian}>
                                <Icon
                                    type="online"
                                    onClick={() => {
                                        setPublishData(record);
                                    }}
                                />
                            </HandleIcon.Item>
                        )}

                        <HandleIcon.Item title={I18N.tab2.index.shanChu}>
                            <Popconfirm
                                placement="topLeft"
                                title={
                                    // eslint-disable-next-line prettier/prettier
                                <div>{I18N.tab2.index.queRenShanChu}<span style={{ display: 'inline-block', maxWidth: 150 }}><Ellipsis title={record?.displayName} widthLimit={150} /></span>{I18N.tab2.index.ma}</div>
                                }
                                overlayStyle={{
                                    width: 280
                                }}
                                onConfirm={() => {
                                    del(record);
                                }}>
                                <Icon type="delete" />
                            </Popconfirm>
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.tab2.index.fuZhi}>
                            <Icon
                                type="copy"
                                onClick={() => {
                                    setAddModifyData({
                                        ...record,
                                        displayName: `${record?.displayName}_copy`,
                                        code: `${record?.code}_copy`,
                                        titleType: 'copy',
                                        title: I18N.tab2.index.fuZhi
                                    });
                                }}
                            />
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.tab2.index.banBenLiShi}>
                            <Icon
                                type="history"
                                onClick={() => {
                                    history.push(
                                        `/handle/workflow/arrange/versionList?currentTab=${areaType}&uuid=${record?.uuid}&version=${record?.version}`
                                    );
                                }}
                            />
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.tab2.index.yinYongGuanXi}>
                            <Icon type="correlation" onClick={() => setReferenceDrawerData(record)} />
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.tab2.index.shouQuan}>
                            <Icon
                                type="user-privilege"
                                onClick={() => {
                                    goToAssign(record)
                                }}
                            />
                        </HandleIcon.Item>
                    </HandleIcon>
                );
            }
        }
    ];

    // 修改查询表单
    const onFormChange = (values, changeInfo) => {
        if (['status'].includes(changeInfo.name)) {
            actions.search({
                ...values,
                current: 1
            });
        }
    };

    return (
        <QueryListScene query={query} memory actions={actions} initSearch={true}>
            <QueryForm
                onChange={onFormChange}
                extraActions={
                    <>
                        <Button.Group>
                            <Tooltip title={I18N.tab2.index.piLiangShangXian}>
                                <Button
                                    disabled={!selectedRows?.length}
                                    onClick={() => {
                                        setPublishData(selectedRows);
                                    }}>
                                    <Icon type="batch-online" />
                                </Button>
                            </Tooltip>
                            <Tooltip title={I18N.tab2.index.daoRu}>
                                <Button
                                    onClick={() => {
                                        setImportWorkFlow(true);
                                    }}>
                                    <Icon type="import" />
                                </Button>
                            </Tooltip>
                        </Button.Group>
                        <Button
                            onClick={() => {
                                setAddModifyData({
                                    titleType: 'add',
                                    title: I18N.tab2.index.xinZeng
                                });
                            }}
                            icon="plus"
                            type="primary"
                            style={{ marginLeft: 10 }}>
                            {I18N.tab2.index.xinZeng}
                        </Button>
                    </>
                }>
                <Field
                    type="selectInput"
                    name="obj"
                    props={{
                        placeholder: I18N.tab2.index.shuRuSouSuoNei,
                        onPressEnter: () => {
                            actions.search({ current: 1 });
                        },
                        options: [
                            { label: I18N.tab2.index.gongZuoLiuMingCheng, value: 'displayName' },
                            { label: I18N.tab2.index.gongZuoLiuBiaoZhi, value: 'code' }
                        ]
                    }}
                />
                <Field
                    type="select"
                    name="status"
                    props={{
                        placeholder: I18N.tab2.index.shuRuXuanZeZhuang,
                        options:
                            Object.keys(statusDict)?.map((key) => ({
                                label: <Ellipsis title={statusDict[key]} />,
                                value: key
                            })) || []
                    }}
                />
            </QueryForm>
            <QueryList
                columns={columns}
                rowKey="uuid"
                rowSelection={{
                    columnWidth: '35px',
                    selectedRowKeys: selectedRows?.map((v) => v.uuid),
                    onChange: (selectedRowKeys, sRows) => {
                        // 翻页的场景sRows未代入
                        const notMatchUuids = selectedRowKeys.filter((uuid) => {
                            if (!sRows.find((s) => s.uuid === uuid)) {
                                return uuid;
                            }
                        });
                        const sRowsPre = selectedRows.filter((s) => notMatchUuids.includes(s.uuid)) || [];
                        setSelectedRows(sRowsPre.concat(sRows));
                    },
                    getCheckboxProps: (record) => ({
                        // disabled: [2, 3].includes(record?.status)
                        disabled: [3].includes(record?.status)
                    })
                }}
                scroll={{ x: 1600 }}
            />
            <AddModify
                visible={!!addModifyData}
                data={addModifyData}
                search={actions.search}
                orgList={orgList}
                appList={appList}
                onCancel={() => {
                    setAddModifyData();
                }}
            />
            <ImportWorkFlowModal
                visible={!!importWorkFlow}
                search={() => {
                    actions.search({ current: 1 });
                }}
                onCancel={() => {
                    setImportWorkFlow();
                }}
            />
            <Publish
                visible={!!publishData}
                data={publishData}
                search={() => {
                    setSelectedRows([]);
                    actions.search();
                }}
                onCancel={() => {
                    setPublishData();
                }}
            />
            <ReferenceDrawer
                title={referenceDrawerData ? `${referenceDrawerData?.displayName}【${referenceDrawerData?.code}】` : ''}
                visible={!!referenceDrawerData}
                onClose={() => {
                    setReferenceDrawerData(null);
                }}
                fetchReference={() => {
                    return referenceAPI.getRelationResult({
                        componentType: 'DATASOURCE_WORKFLOW',
                        componentId: referenceDrawerData.code
                    });
                }}
            />
        </QueryListScene>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(QueryListWrapper(Tab1, actions));
