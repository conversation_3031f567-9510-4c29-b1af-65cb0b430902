import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { Modal, Button, Form, Input, message, TreeSelect, Select } from 'tntd';
import service from '../../../service';
import otp from '../otp';
const { Option } = Select;
const { TextArea } = Input;

const AddModifyModal = (props) => {
    const { form, data, onCancel, search, visible, orgList, appList } = props;
    const { getFieldDecorator, setFieldsValue, resetFields } = form;
    const [loading, setLoading] = useState(false);
    const { title = I18N.modal.addmodify.xinZeng, titleType, uuid } = data || {};
    useEffect(() => {
        if (visible) {
            setFieldsValue(data || {});
            return () => {
                resetFields();
            };
        }
    }, [visible]);

    const submit = () => {
        form.validateFields((errors, formData) => {
            if (!errors) {
                setLoading(true);
                if (uuid) {
                    formData.uuid = uuid;
                }
                let serviceType = '';
                switch (titleType) {
                    case 'update':
                        serviceType = 'updateWorkFlow';
                        break;
                    case 'copy':
                        serviceType = 'copyFlow';
                        break;
                    case 'add':
                    default:
                        serviceType = 'addWorkFlow';
                        break;
                }
                service[serviceType](formData)
                    .then((res) => {
                        if (res?.success) {
                            search && search();
                            message.success(res?.message || I18N.template(I18N.modal.addmodify.tITLE3, { val1: title }));
                            onCancel && onCancel();
                        } else {
                            message.error(res?.message || I18N.template(I18N.modal.addmodify.tITLE2, { val1: title }));
                        }
                    })
                    .finally(() => {
                        setLoading(false);
                    });
            }
        });
    };

    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.modal.addmodify.quXiao}
        </Button>,
        <Button type="primary" onClick={submit} loading={loading} key="ok">
            {I18N.modal.addmodify.queDing}
        </Button>
    ];
    return (
        <Modal
            maskClosable={false}
            title={I18N.template(I18N.modal.addmodify.tITLE, { val1: title })}
            visible={visible}
            onCancel={onCancel}
            width={660}
            footer={footerDom}>
            <Form {...otp.formItemLayout}>
                <Form.Item label={I18N.modal.addmodify.gongZuoLiuMingCheng2}>
                    {getFieldDecorator('displayName', {
                        rules: [
                            {
                                required: true,
                                message: I18N.modal.addmodify.qingTianXieGongZuo2
                            },
                            {
                                max: 200,
                                message: I18N.modal.addmodify.zuiDuoGeZiFu2
                            },
                            {
                                pattern: /^[\w\s\u4E00-\u9FA5\-.]+$/,
                                message: I18N.modal.addmodify.gongZuoLiuMingCheng
                            }
                        ]
                    })(<Input placeholder={I18N.modal.addmodify.jinZhiChiZhongYing} />)}
                </Form.Item>
                <Form.Item label={I18N.modal.addmodify.gongZuoLiuBiaoZhi2}>
                    {getFieldDecorator('code', {
                        rules: [
                            {
                                required: true,
                                message: I18N.modal.addmodify.qingTianXieGongZuo
                            },
                            {
                                max: 200,
                                message: I18N.modal.addmodify.zuiDuoGeZiFu2
                            },
                            {
                                pattern: /^[a-zA-Z0-9\_]+$/,
                                message: I18N.modal.addmodify.gongZuoLiuBiaoZhi
                            }
                        ]
                    })(<Input disabled={uuid && !['copy'].includes(titleType)} placeholder={I18N.modal.addmodify.qingTianXieGongZuo} />)}
                </Form.Item>
                <Form.Item className="form-item" label={I18N.modal.addmodify.suoShuJiGou}>
                    {getFieldDecorator('orgCode', {
                        rules: [{ required: true, message: I18N.steps.zero.qingTianXieJiGou }]
                    })(
                        <TreeSelect
                            placeholder={I18N.modal.addmodify.xuanZeJiGou}
                            searchPlaceholder={I18N.modal.addmodify.jiGouMingCheng}
                            treeNodeFilterProp="title"
                            showSearch
                            treeData={orgList}
                            treeDefaultExpandAll
                            // onChange={(e) => {
                            //     getAppByOrgId(e);
                            //     form.setFieldsValue({ appCode: undefined });
                            // }}
                            dropdownStyle={{ maxHeight: 300, overflow: 'auto', width: 340 }}
                        />
                    )}
                </Form.Item>
                <Form.Item className="form-item" label={I18N.components.commontable.suoShuQuDao}>
                    {getFieldDecorator('appCode', {
                        rules: [{ required: true, message: I18N.modal.addmodify.qingXuanZeSuoShu }]
                    })(
                        <Select
                            showSearch
                            filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                            placeholder={I18N.modal.addmodify.qingXuanZeSuoShu}>
                            {appList.map((item) => {
                                if(item.key) {
                                    return (
                                        <Option value={item.key} key={item.key}>
                                            {item.name}
                                        </Option>
                                    );
                                }
                            })}
                        </Select>
                    )}
                </Form.Item>
                <Form.Item label={I18N.modal.addmodify.miaoShu}>
                    {getFieldDecorator('description', {
                        rules: [
                            {
                                max: 2000,
                                message: I18N.modal.addmodify.zuiDuoGeZiFu
                            }
                        ]
                    })(<TextArea style={{ marginTop: '6px' }} placeholder={I18N.modal.addmodify.qingTianXieMiaoShu} />)}
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default Form.create()(AddModifyModal);
