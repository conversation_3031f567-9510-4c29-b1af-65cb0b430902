import I18N from '@/utils/I18N';
import { useState } from 'react';
import { Tabs, TabsContainer, message } from 'tntd';
import { connect } from 'dva';
import { getUrlKey } from '@/utils/utils';
import AssignModal from '@tddc/assign-modal';
import Tab1 from './Tab1';
import Tab2 from './Tab2';
import service from '../service';

const { TabPane } = Tabs;

const Page = (props) => {
    const { location, history, globalStore } = props;
    console.log(globalStore, 'globalstore');
    const { orgList, appList, currentUser } = globalStore;
    const { pathname } = location;
    const currentTab = getUrlKey('currentTab') || '2';
    //授权
    const [priviligeVisible, setPriviligeVisible] = useState(false);
    const [assignModalData, setAssignModalData] = useState(false);
    // tab切换
    const changeTabHandle = (key) => {
        const search = '?currentTab=' + key;
        history.push(pathname + search);
    };
    const onSubmit = (data) => {
        service
            .authorize({
                appCodes: data.appKeys || assignModalData.appCodes,
                orgCodes: data.checkedKeys || assignModalData.orgCodes,
                bizId: data.uuid || assignModalData.uuid
            })
            .then((res) => {
                if (res && res.success && res.code === 200) {
                    message.success(I18N.list.index.shouQuanChengGong);
                    setPriviligeVisible(false);
                }
            });
    };
    return (
        <>
            <Tabs activeKey={currentTab} onChange={changeTabHandle} animated={false} type="ladder-card">
                <TabPane tab={I18N.list.index.yunXingQu} key="2">
                    <Tab1
                        areaType={currentTab}
                        history={history}
                        setAssignModalData={setAssignModalData}
                        priviligeVisible={priviligeVisible}
                        setPriviligeVisible={setPriviligeVisible}
                    />
                </TabPane>
                <TabPane tab={I18N.list.index.bianJiQu} key="1">
                    <Tab2
                        areaType={currentTab}
                        history={history}
                        setAssignModalData={setAssignModalData}
                        priviligeVisible={priviligeVisible}
                        setPriviligeVisible={setPriviligeVisible}
                    />
                </TabPane>
            </Tabs>
            <AssignModal
                visible={priviligeVisible}
                dataItem={assignModalData}
                orgList={orgList}
                title={I18N.list.index.gongZuoLiuShouQuan}
                close={() => setPriviligeVisible(false)}
                // search={editorAreaRef.current.reset && (() => editorAreaRef.current.reset())}
                disabled={!assignModalData?.canWriter || currentTab === '2'}
                appList={appList
                    .filter((v) => v.key)
                    .map((item) => {
                        return {
                            label: item?.name,
                            value: item?.key
                        };
                    })}
                onSubmit={onSubmit}
                // lang={personalMode?.lang}
                orgTitle={I18N.list.index.shouQuanKeYongJi}
                appTitle={I18N.list.index.shouQuanKeYongQu}
                orgCheckboxTitle={I18N.list.index.quanBuJiGouKe}
                appCheckboxTitle={I18N.list.index.quanBuQuDaoKe}
            />
        </>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(TabsContainer(Page));
