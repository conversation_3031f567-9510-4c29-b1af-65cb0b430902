import I18N from '@/utils/I18N';
import { useState } from 'react';
import { Tabs, TabsContainer, message } from 'tntd';
import { connect } from 'dva';
import './index.less';
import service from './service';
import { getUrlKey } from '@tntd/utils';
import history from '@/utils/history';
import EditorArea from './EditorArea';
import RunningArea from './RunningArea';
import AssignModal from '@tddc/assign-modal';
const TabPane = Tabs.TabPane;

const Page = (props) => {
    const { location, globalStore } = props;
    const { orgList, appList, currentUser } = globalStore;
    const currentTab = getUrlKey('currentTab');

    const [key, setKey] = useState('1');
    //授权
    const [priviligeVisible, setPriviligeVisible] = useState(false);
    const [assignModalData, setAssignModalData] = useState(false);
    const onSubmit = (data) => {
        service
            .authorize({
                appCodes: data.appKeys || assignModalData.appCodes,
                orgCodes: data.checkedKeys || assignModalData.orgCodes,
                bizId: data.uuid || assignModalData.uuid
            })
            .then((res) => {
                if (res && res.success && res.code === 200) {
                    message.success(I18N.interfacemanagement.list.shouQuanChengGong);
                    setPriviligeVisible(false);
                }
            });
    };
    const changeTab = (key) => {
        const { pathname } = location;
        const search = '?currentTab=' + key;
        setKey(key);
        history.push(pathname + search);
    };

    return (
        <>
            <Tabs activeKey={currentTab || key} onChange={changeTab} animated={false} type="ladder-card">
                <TabPane tab={I18N.formula.listtab.yunXingQu} key="1">
                    <RunningArea
                        currentTab={currentTab}
                        setAssignModalData={setAssignModalData}
                        setPriviligeVisible={setPriviligeVisible}
                    />
                </TabPane>
                <TabPane tab={I18N.formula.listtab.bianJiQu} key="2">
                    <EditorArea currentTab={currentTab} setAssignModalData={setAssignModalData} setPriviligeVisible={setPriviligeVisible} />
                </TabPane>
            </Tabs>
            <AssignModal
                visible={priviligeVisible}
                dataItem={assignModalData}
                orgList={orgList}
                title={I18N.formula.listtab.hanShuKuShouQuan}
                close={() => setPriviligeVisible(false)}
                // search={editorAreaRef.current.reset && (() => editorAreaRef.current.reset())}
                disabled={!assignModalData?.canWriter || key === '1'}
                appList={appList
                    .filter((v) => v.key)
                    .map((item) => {
                        return {
                            label: item?.name,
                            value: item?.key
                        };
                    })}
                onSubmit={onSubmit}
                // lang={personalMode?.lang}
                orgTitle={I18N.formula.listtab.shouQuanKeYongJi}
                appTitle={I18N.formula.listtab.shouQuanKeYongQu}
                orgCheckboxTitle={I18N.formula.listtab.quanBuJiGouKe}
                appCheckboxTitle={I18N.formula.listtab.quanBuQuDaoKe}
            />
        </>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(TabsContainer(Page));
