import I18N from '@/utils/I18N';
import { useEffect, useState, useRef } from 'react';
import { connect } from 'dva';
import { Breadcrumb, Spin, message } from 'tntd';
import { getUrlKey } from '@/utils/utils';
import './index.less';
import service from '../service';

import DetailInfo from '../DetailInfo';

const Page = (props) => {
    const { history, match, globalStore, dispatch } = props;
    const { params } = match || {};
    const { uuid } = params || {};
    const type = getUrlKey('type');
    const { orgList, appList, orgCodeMap, allOrgListMap } = globalStore;
    const formRef = useRef();
    const [versionInfo, setVersionInfo] = useState({});
    const [loading, setLoading] = useState(true);
    let typeName = I18N.detail.index.xinZeng;
    if (type === 'view') typeName = I18N.detail.index.chaKan;
    if (type === 'edit') typeName = I18N.detail.index.xiuGai;

    useEffect(() => {
        init();
        document.querySelector('.g-formula-detail').parentNode.style.minWidth = 'auto';
        return () => {
            document.querySelector('.g-formula-detail').parentNode.style.minWidth = '1024px';
        };
    }, [uuid]);

    const init = async () => {
        setLoading(true);

        await dispatch({
            type: 'global/getWorkflowConfig'
        });
        setLoading(false);

        if (uuid) {
            await service.versionDetail({ versionUuids: [uuid] }).then((res) => {
                if (res.success) {
                    setVersionInfo(res.data || {});
                } else {
                    message.error(res.message || res.msg);
                }
            });
        }
    };

    return (
        <div className="g-formula-detail">
            <Spin spinning={loading}>
                <div className="page-global-body">
                    {!loading && (
                        <DetailInfo
                            ref={formRef}
                            item={versionInfo}
                            history={history}
                            curVersionUuid={uuid}
                            orgCodeMap={orgCodeMap}
                            orgList={orgList}
                            appList={appList}
                            allOrgListMap={allOrgListMap}
                            type={type}
                        />
                    )}
                </div>
            </Spin>
        </div>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(Page);
