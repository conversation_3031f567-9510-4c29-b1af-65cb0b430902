import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { connect } from 'dva';
import { cloneDeep } from 'lodash';
import { message, Tag, Modal, QueryListScene, HandleIcon, Ellipsis, Icon } from 'tntd';

import { ReferenceDrawer, ReferenceCheck } from '@tddc/reference';
import { referenceAPI } from '@/services';
import { formatStandardTime, getUrlKey } from '@/utils/utils';
import { queryRouterHandle } from '@/utils/queryRouterHandle';
import TestModal from '@/components/BpmnCalculateDialog/Inner/TestModal';
import QueryListWrapper from '@/components/QueryListWrapper';
import PagePermission from '@/components/PagePermission';

import PublishResult from '../Modal/PublishResult';
import service from '../service';

const { QueryForm, Field, QueryList, createActions } = QueryListScene;
const actions = createActions();
const confirm = Modal.confirm;

const Page = (props) => {
    const { history, currentTab, globalStore, setAssignModalData, setPriviligeVisible,priviligeVisible } = props;
    console.log(props,'props')
    const { orgList, appList,allOrgList, allAppList, currentApp, currentOrgCode } = globalStore;
    const [prevCode, setPrevCode] = useState();
    const [referenceDrawerData, setReferenceDrawerData] = useState(null);
    const [batchResult, setBatchResult] = useState(null);
    const [data, setData] = useState([]);
    const [testDataInfo, setTestDataInfo] = useState({
        visible: false,
        testData: []
    });
    const { visible, testData } = testDataInfo;
    const [code, setCode] = useState('');
    const [type, setType] = useState(1);

    // let searchObj = location?.search ? searchToObject(location?.search) : {};
    // let { code: formulaCode, status } = searchObj;
    const formulaCode = getUrlKey('code');
    const status = getUrlKey('status');
    
    useEffect(() => {
        if (currentTab === '1') {
            actions.search();
        }
    }, [currentTab]);
    useEffect(() => {
        return () => {
            queryRouterHandle.init();
        };
    }, []);
    useEffect(()=>{
        if(!priviligeVisible) {
            actions.search({ current: 1 });
        }
        
    },[priviligeVisible])
    useEffect(() => {
        if (formulaCode || status) {
            actions.setFormData({
                obj: {
                    functionCode: formulaCode
                },
                status: status || undefined
            });
        }
    }, [formulaCode, status]);
    const query = ({ current = 1, pageSize = 10, ...rest }) => {
        queryRouterHandle.handle();
        let params = {
            curPage: current,
            areaType: 1,
            appCode: currentApp.name,
            orgCode: currentOrgCode,
            pageSize,
            ...rest,
            functionName: rest?.obj?.functionName,
            functionCode: rest?.obj?.functionCode
        };
        setPrevCode(code || '');
        if (code !== prevCode && code) {
            params.functionCode = code;
        }
        delete params.obj;
        return service.getFunctionQuery(params).then((res) => {
            if (!res) return;
            setData(res?.data);
            if (res.success) {
                let data = res?.data;
                return {
                    pageSize: Number(data?.pageSize),
                    current: Number(data?.curPage),
                    total: Number(data?.total),
                    data: data?.contents || []
                };
            }
            message.error(res.message || res.msg);

            message.error(res.message || res.msg);

            //
        });
    };

    const [selectedRowKeys, setSelectedRowKeys] = useState([]);

    const goToAssign = (record)=> {
        let data = cloneDeep(record);
        service.getAuthorize({bizId:record.uuid}).then((v)=>{
            data.appCodes = v?.data?.appCodes;
            data.orgCodes = v?.data?.orgCodes;
            setAssignModalData(data);
            setPriviligeVisible(true);
        })
    }
    const columns = [
        {
            title: I18N.runningarea.index.hanShuMingCheng,
            dataIndex: 'functionName',
            width: 300,
            render: (text, record) => {
                const status = record?.viewVersion?.status;
                const version = record?.viewVersion?.version;
                return <Ellipsis prefix={<Tag color="tnt-purple">V{version}</Tag>} title={text} />;
            }
        },
        {
            title: I18N.runningarea.index.biaoZhi,
            dataIndex: 'functionCode',
            width: 200
        },
        // {
        //     title: '状态',
        //     width: 200,
        //     dataIndex: 'status',
        //     render: (text, record) => {
        //         let res = auditStatusMap[text].value;
        //         if (text === 2 || text === 3) {
        //             let extra = record.auditInfo === 1 ? '(上线)' : '(下线)';
        //             res = res + extra;
        //         }
        //         return (
        //             <div>
        //                 <Status text={res} color={auditStatusMap[text].color} />
        //             </div>
        //         );
        //     }
        // },
        {
            title: I18N.runningarea.index.leiXing,
            width: 110,
            dataIndex: 'type',
            render: (text) => {
                const map = {
                    1: I18N.runningarea.index.gongShi,
                    2: I18N.runningarea.index.jiaoBen
                };
                return <Tag color={text === 1 ? 'geekblue' : 'cyan'}>{map[text]}</Tag>;
            }
        },
        {
            title: I18N.runningarea.index.suoShuJiGou,
            width: 120,
            dataIndex: 'orgCode',
            render: (text, row) => {
                let orgName = allOrgList?.find((v)=>v.code === text)?.name;
                return <Ellipsis title={orgName || '- -'} />;
            }
        },
        {
            title: I18N.addmodify.index.quDao,
            width: 120,
            dataIndex: 'appCode',
            render: (text, row) => {
                let appName = allAppList?.find((v)=>v.name === text)?.displayName
                return <Ellipsis title={appName || '- -'} />;
            }
        },
        {
            title: I18N.runningarea.index.xiuGaiRen,
            dataIndex: 'updateBy',
            width: 150,
            render: (text) => {
                return <Ellipsis title={text} />;
            }
        },
        {
            title: I18N.runningarea.index.xiuGaiShiJian,
            dataIndex: 'gmtModified',
            width: 190,
            render: (text, record) => {
                return formatStandardTime(record.gmtModified || record.gmtCreate);
            }
        },
        {
            title: I18N.runningarea.index.caoZuo,
            dataIndex: 'operate',
            width: 200,
            operate: true,
            fixed: 'right',
            render: (text, record) => (
                <HandleIcon>
                    {window.auth('layoutFunctionLibrary', 'Offline') && record?.canWriter && (
                        <HandleIcon.Item title={I18N.runningarea.index.xiaXian}>
                            <Icon
                                type="offline"
                                onClick={() => {
                                    ReferenceCheck({
                                        strongMsg: I18N.runningarea.index.cunZaiQiangYinYong,
                                        rq: () =>
                                            referenceAPI.checkComponentReference({
                                                // componentType: 'DATASOURCE_FUNCTION',
                                                // componentId: record?.functionVersionDTOList?.[0]?.uuid
                                                componentType: 'DATASOURCE_FUNCTION',
                                                componentId: record.functionCode
                                            })
                                    }).then(() => {
                                        confirm({
                                            title: I18N.template(I18N.runningarea.index.niQueDingYaoXia, { val1: record.functionName }),
                                            onOk: () => {
                                                service.functionOffline({ uuid: record.uuid }).then((res) => {
                                                    if (res?.success) {
                                                        message.success(res?.message);
                                                        actions?.search();
                                                    } else {
                                                        message.error(res.message || res.msg);
                                                    }
                                                });
                                            }
                                        });
                                    });
                                }}
                            />
                        </HandleIcon.Item>
                    )}

                    <HandleIcon.Item title={I18N.runningarea.index.chaKan}>
                        <Icon
                            type="profile"
                            onClick={() => {
                                const verUuid = record?.viewVersion?.uuid;
                                history.push(`/handle/formula/view/${verUuid}`);
                            }}
                        />
                    </HandleIcon.Item>
                    <HandleIcon.Item title={I18N.runningarea.index.shouQuan}>
                        <Icon
                            type="user-privilege"
                            onClick={() => {
                                goToAssign(record);
                            }}
                        />
                    </HandleIcon.Item>
                    <HandleIcon.Item title={I18N.runningarea.index.chaKanBanBen}>
                        <Icon
                            type="history"
                            onClick={() => {
                                history.push(`/handle/formula/history/${record.uuid}`);
                            }}
                        />
                    </HandleIcon.Item>
                    <HandleIcon.Item title={I18N.runningarea.index.yinYongGuanXi}>
                        <Icon type="correlation" onClick={() => setReferenceDrawerData(record)} />
                    </HandleIcon.Item>
                </HandleIcon>
            )
        }
    ];

    return (
        <PagePermission
            auth={() => {
                return true || window.auth('layoutFunctionLibrary', 'Query');
            }}>
            <QueryListScene memory query={query} actions={actions} className="formula-page" initSearch={formulaCode || status}>
                <QueryForm>
                    <Field
                        title=""
                        name="obj"
                        type="selectInput"
                        props={{
                            placeholder: I18N.runningarea.index.shuRuSouSuoNei,
                            onChange: (vals) => {
                                if (!vals.functionName && !vals.functionCode) {
                                    const allVals = actions.getFormData();
                                    const { obj, ...rest } = allVals;
                                    actions.setFormData({ ...rest });
                                }
                            },
                            onPressEnter: (e) => {
                                const vals = actions.getFormData();
                                actions.search({ ...vals, current: 1 });
                            },
                            options: [
                                { label: I18N.runningarea.index.hanShuMingCheng, value: 'functionName' },
                                { label: I18N.runningarea.index.hanShuBiaoZhi, value: 'functionCode' }
                            ]
                        }}
                    />
                </QueryForm>

                <QueryList rowKey="functionCode" columns={columns} scroll={{ x: 1800 }} />
            </QueryListScene>
            {visible && (
                <TestModal
                    visible={visible}
                    data={testData}
                    code={code}
                    type={type}
                    onCancel={() => {
                        setTestDataInfo({
                            visible: false,
                            testData: []
                        });
                        setCode('');
                        setType(1);
                    }}
                />
            )}

            <ReferenceDrawer
                title={referenceDrawerData ? `${referenceDrawerData?.functionName}【${referenceDrawerData?.functionCode}】` : ''}
                visible={!!referenceDrawerData}
                onClose={() => {
                    setReferenceDrawerData(null);
                }}
                fetchReference={() => {
                    return referenceAPI.getRelationResult({
                        relationType: 'function',
                        componentType: 'DATASOURCE_FUNCTION',
                        componentId: referenceDrawerData.functionCode
                    });
                }}
            />

            <PublishResult
                onCancel={() => {
                    actions?.search();
                    setSelectedRowKeys([]);
                    setBatchResult(null);
                }}
                visible={!!batchResult}
                dataSource={batchResult || []}
            />
        </PagePermission>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(QueryListWrapper(Page, actions));
