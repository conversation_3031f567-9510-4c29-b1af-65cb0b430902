import I18N from '@/utils/I18N';
const avg = I18N.detailinfo.constants.pingJunZhi;
const max = I18N.detailinfo.constants.zuiDaZhi;
const min = I18N.detailinfo.constants.zuiXiaoZhi;
const sum = I18N.detailinfo.constants.qiuHe;
const pow = I18N.detailinfo.constants.miHanShu;
const day = I18N.detailinfo.constants.huoQuTian;
const month = I18N.detailinfo.constants.huoQuYue;
const year = I18N.detailinfo.constants.huoQuNian;
const contains = I18N.detailinfo.constants.baoHan;
const equals = I18N.detailinfo.constants.xiangDeng;
const abs = I18N.detailinfo.constants.jueDuiZhi;
const ceil = I18N.detailinfo.constants.xiangShangQuZheng;
const floor = I18N.detailinfo.constants.xiangXiaQuZheng;
const round = I18N.detailinfo.constants.siSheWuRu;
const ifelseif = I18N.detailinfo.constants.ruGuoNaMe2;
const ifelseifelse = I18N.detailinfo.constants.ruGuoQiTaRu3;
const ifthenelse = I18N.detailinfo.constants.ruGuoNaMeFou2;
const random = I18N.detailinfo.constants.suiJiShu;
const variance = I18N.detailinfo.constants.fangCha;
const standardDeviation = I18N.detailinfo.constants.biaoZhunCha;
const notContains = I18N.detailinfo.constants.buBaoHan;
const notEquals = I18N.detailinfo.constants.buXiangDeng;
const concat = I18N.detailinfo.constants.pinJieZiFuChuan;
const length1 = I18N.detailinfo.constants.ziFuChuanChangDu;
const substring = I18N.detailinfo.constants.jieQuZiFuChuan;
const trim = I18N.detailinfo.constants.quTouWeiKongGe;
const if1 = I18N.detailinfo.constants.ruGuo;
const then1 = I18N.detailinfo.constants.naMe;
const elseif1 = I18N.detailinfo.constants.qiTaRuGuo;
const else1 = I18N.detailinfo.constants.fouZe;
const now = I18N.detailinfo.constants.huoQuDangQianShi;
const currentDate = I18N.detailinfo.constants.huoQuDangQianRi4;
const toString = I18N.detailinfo.constants.zhuanHuanWeiZiFu;
const toLong = I18N.detailinfo.constants.zhuanHuanWeiZhengShu;
const toDouble = I18N.detailinfo.constants.zhuanHuanWeiXiaoShu;
const toBoolean = I18N.detailinfo.constants.zhuanHuanWeiBuEr;
const toDateTime = I18N.detailinfo.constants.zhuanHuanWeiShiJian;
const dateTimeDiff = I18N.detailinfo.constants.shiJianXiangJian;
const nvl = I18N.detailinfo.constants.kongZhiFuZhi;
const isNull = I18N.detailinfo.constants.panKong;
const isNotNull = I18N.detailinfo.constants.panFeiKong;
const and1 = I18N.detailinfo.constants.qie;
const or1 = I18N.detailinfo.constants.huo;
const ifelseif2 = I18N.detailinfo.constants.ruGuoNaMe;
const ifelseifelse2 = I18N.detailinfo.constants.ruGuoQiTaRu2;
const ifthenelse2 = I18N.detailinfo.constants.ruGuoNaMeFou;

const quickList = [
    { name: avg, content: I18N.detailinfo.constants.qiuDuoGeShuZhi2, value: `#${avg}(,,4)` },
    {
        name: max,
        content: I18N.detailinfo.constants.biJiaoDuoGeShu2,
        value: `#${max}(,)`
    },
    {
        name: min,
        content: I18N.detailinfo.constants.biJiaoDuoGeShu,
        value: `#${min}(,)`
    },
    { name: sum, content: I18N.detailinfo.constants.qiuDuoGeShuZhi, value: `#${sum}(,)` },
    {
        name: pow,
        content: I18N.detailinfo.constants.hanShuXingShiWei,
        value: `#${pow}(,)`
    },
    { name: '＝', content: I18N.detailinfo.constants.dengYu, value: '=' },
    { name: '>', content: I18N.detailinfo.constants.daYu, value: '>' },
    { name: '<', content: I18N.detailinfo.constants.xiaoYu, value: '<' },
    { name: '()', content: I18N.detailinfo.constants.kuoHao, value: '()' },
    { name: day, content: I18N.detailinfo.constants.huoQuDangQianRi3, value: `#${day}()` },
    { name: month, content: I18N.detailinfo.constants.huoQuDangQianRi2, value: `#${month}()` },
    { name: year, content: I18N.detailinfo.constants.huoQuDangQianRi, value: `#${year}()` },
    { name: '!＝', content: I18N.detailinfo.constants.buDengYu, value: '!=' },
    { name: '>=', content: I18N.detailinfo.constants.daYuDengYu, value: '>=' },
    { name: '<=', content: I18N.detailinfo.constants.xiaoYuDengYu, value: '<=' },
    { name: '+', content: I18N.detailinfo.constants.jia, value: '+' },
    { name: '*', content: I18N.detailinfo.constants.cheng, value: '*' },
    { name: contains, content: I18N.detailinfo.constants.jiaoYanZiDuanN2, value: `#${contains}(,)` },
    { name: equals, content: I18N.detailinfo.constants.biJiaoZiFuChuan2, value: `#${equals}(,)` },
    { name: abs, content: I18N.detailinfo.constants.quJueDuiZhiRu, value: `#${abs}()` },
    { name: ceil, content: I18N.detailinfo.constants.quBiDangQianShu, value: `#${ceil}()` },
    {
        name: floor,
        content: I18N.detailinfo.constants.buGuanSiSheWu,
        value: `#${floor}()`
    },
    {
        name: round,
        content: I18N.detailinfo.constants.zaiQuXiaoShuJin,
        value: `#${round}()`
    },
    { name: '-', content: I18N.detailinfo.constants.jian, value: '-' },
    { name: '/', content: I18N.detailinfo.constants.chu, value: '/' },
    { name: and1, content: I18N.detailinfo.constants.qieYongYuTiaoJian, value: and1 },
    { name: or1, content: I18N.detailinfo.constants.huoYongYuTiaoJian, value: or1 },
    {
        name: ifelseifelse,
        content: I18N.detailinfo.constants.ruGuoQiTaRu,
        value: `#${ifelseifelse}`
    },
    {
        name: ifelseif,
        value: `#${ifelseif2}`,
        content: I18N.detailinfo.constants.tiaoJianHanShuYong2
    },
    {
        name: ifthenelse,
        content: I18N.detailinfo.constants.tiaoJianHanShuYong,
        value: `#${ifthenelse}`
    },
    { name: random, content: I18N.detailinfo.constants.suiJiXuanQuDa, value: `#${random}()` },
    {
        name: variance,
        content: I18N.detailinfo.constants.geGeShuJuFen,
        value: `#${variance}(,)`
    },
    {
        name: standardDeviation,
        content: I18N.detailinfo.constants.biaoZhunChaShiFang,
        value: `#${standardDeviation}(,)`
    },
    { name: notContains, content: I18N.detailinfo.constants.jiaoYanZiDuanN, value: `#${notContains}(,)` },
    { name: notEquals, content: I18N.detailinfo.constants.biJiaoZiFuChuan, value: `#${notEquals}(,)` },
    {
        name: concat,
        content: I18N.detailinfo.constants.shiYongLiangGeHuo,
        value: `#${concat}(,)`
    },
    { name: length1, content: I18N.detailinfo.constants.shuRuYiGeZi, value: `#${length1}()` },
    {
        name: substring,
        content: I18N.detailinfo.constants.yongYuTiQuZi,
        value: `#${substring}(,,)`
    },
    { name: trim, content: I18N.detailinfo.constants.quDiaoZiFuChuan, value: `#${trim}()` },
    {
        name: now,
        content: I18N.detailinfo.constants.huoQuXiTongDang2,
        value: `#${now}()`
    },
    {
        name: currentDate,
        content: I18N.detailinfo.constants.huoQuXiTongDang,
        value: `#${currentDate}()`
    },
    {
        name: toString,
        content: I18N.detailinfo.constants.leiXingZhuanHuanHan5,
        value: `#${toString}()`
    },
    {
        name: toLong,
        content: I18N.detailinfo.constants.leiXingZhuanHuanHan4,
        value: `#${toLong}()`
    },
    {
        name: toDouble,
        content: I18N.detailinfo.constants.leiXingZhuanHuanHan3,
        value: `#${toDouble}()`
    },
    {
        name: toBoolean,
        content: I18N.detailinfo.constants.leiXingZhuanHuanHan2,
        value: `#${toBoolean}()`
    },
    {
        name: toDateTime,
        content: I18N.detailinfo.constants.leiXingZhuanHuanHan,
        value: `#${toDateTime}()`
    },
    {
        name: dateTimeDiff,
        content: I18N.detailinfo.constants.liangGeShiJianXiang,
        value: `#${dateTimeDiff}(,,)`
    },
    {
        name: nvl,
        content: I18N.detailinfo.constants.kongZhiFuZhiHan,
        value: `#${nvl}(,)`
    },
    { name: isNull, content: I18N.detailinfo.constants.panDingZiDuanShi2, value: `#${isNull}()` },
    { name: isNotNull, content: I18N.detailinfo.constants.panDingZiDuanShi, value: `#${isNotNull}()` },
    // { name: "数组处理", content: "参数1-数据源字段、参数2-数据过滤器(支持&、|)、参数3-计算属性、参数4-计算方式(sum/count/max/min/avg)", value: "#数组处理(,,,)" }
    {
        name: I18N.constants.index.ziFuChuanTiHuan,
        content: I18N.detailinfo.constants.zhiJiangYiGeZi3,
        value: I18N.detailinfo.constants.ziFuChuanTiHuan2
    },
    {
        name: I18N.detailinfo.constants.riQiGeShiHua2,
        content:
            I18N.detailinfo.constants.jiangRiQiZhuanHuan,
        value: I18N.detailinfo.constants.riQiGeShiHua3
    },
    {
        name: I18N.detailinfo.constants.zhuanZiFuChuan2,
        content: I18N.detailinfo.constants.zhengShuXiaoShuRi,
        value: I18N.detailinfo.constants.zhuanZiFuChuan3
    },
    {
        name: I18N.detailinfo.constants.ziFuChuanZhuanShu2,
        content: I18N.detailinfo.constants.zhengShuXiaoShuZi,
        value: I18N.detailinfo.constants.ziFuChuanZhuanShu3
    },
    {
        name: I18N.detailinfo.constants.shuJuHeBing2,
        content: I18N.detailinfo.constants.shuoMingHeBingLiang,
        value: I18N.detailinfo.constants.shuJuHeBing3
    },
    {
        name: I18N.detailinfo.constants.zhuanBuEr2,
        content: I18N.detailinfo.constants.zhiJiangYiGeZi2,
        value: I18N.detailinfo.constants.zhuanBuEr3
    },
    {
        name: I18N.detailinfo.constants.zhuanShiJianXing2,
        content: I18N.detailinfo.constants.zhiJiangYiGeZi,
        value: I18N.detailinfo.constants.zhuanShiJianXing3
    }
];

const ctrlList = [
    { name: avg, value: `${avg}(,,4)`, realValue: 'avg' },
    { name: max, value: `${max}(,)`, realValue: 'max' },
    { name: min, value: `${min}(,)`, realValue: 'min' },
    { name: sum, value: `${sum}(,)`, realValue: 'sum' },
    { name: pow, value: `${pow}(,)`, realValue: 'pow' },
    { name: day, value: `${day}()`, realValue: 'day' },
    { name: month, value: `${month}()`, realValue: 'month' },
    { name: year, value: `${year}()`, realValue: 'year' },
    { name: contains, value: `${contains}(,)`, realValue: 'contains' },
    { name: equals, value: `${equals}(,)`, realValue: 'equals' },
    { name: abs, value: `${abs}()`, realValue: 'abs' },
    { name: ceil, value: `${ceil}()`, realValue: 'ceil' },
    { name: floor, value: `${floor}()`, realValue: 'floor' },
    { name: round, value: `${round}()`, realValue: 'round' },
    {
        name: ifelseif,
        value: ifelseif2,
        realValue: `if
#elseif`
    },
    {
        name: ifelseifelse,
        value: ifelseifelse2,
        realValue: `if
#elseif
#else`
    },
    {
        name: ifthenelse,
        value: ifthenelse2,
        realValue: `if
#then
#else`
    },
    { name: random, value: `${random}()`, realValue: 'random' },
    { name: variance, value: `${variance}(,)`, realValue: 'variance' },
    { name: standardDeviation, value: `${standardDeviation}(,)`, realValue: 'standardDeviation' },
    { name: notContains, value: `${notContains}(,)`, realValue: 'notContains' },
    { name: notEquals, value: `${notEquals}(,)`, realValue: 'notEquals' },
    { name: concat, value: `${concat}(,)`, realValue: 'concat' },
    { name: length1, value: `${length1}()`, realValue: 'length' },
    { name: substring, value: `${substring}(,,)`, realValue: 'substring' },
    { name: trim, value: `${trim}()`, realValue: 'trim' },
    { name: if1, value: `${if1}(){}`, realValue: 'if', noShow: true },
    { name: then1, value: `${then1}{}`, realValue: 'then', noShow: true },
    { name: elseif1, value: `${elseif1}(){}`, realValue: 'elseif', noShow: true },
    { name: else1, value: `${else1}{}`, realValue: 'else', noShow: true },
    { name: now, value: `${now}()`, realValue: 'now' },
    { name: currentDate, value: `${currentDate}()`, realValue: 'currentDate' },
    { name: toString, value: `${toString}()`, realValue: 'toString' },
    { name: toLong, value: `${toLong}()`, realValue: 'toLong' },
    { name: toDouble, value: `${toDouble}()`, realValue: 'toDouble' },
    { name: toBoolean, value: `${toBoolean}()`, realValue: 'toBoolean' },
    { name: toDateTime, value: `${toDateTime}()`, realValue: 'toDateTime' },
    { name: dateTimeDiff, value: `${dateTimeDiff}(,,)`, realValue: 'dateTimeDiff' },
    { name: nvl, value: `${nvl}(,)`, realValue: 'nvl' },
    { name: isNull, value: `${isNull}()`, realValue: 'isNull' },
    { name: isNotNull, value: `${isNotNull}()`, realValue: 'isNotNull' },
    // { name: "数组处理", value: "数组处理(,,,)", realValue: "ArrayOper" }
    { name: I18N.constants.index.ziFuChuanTiHuan, value: I18N.detailinfo.constants.ziFuChuanTiHuan, realValue: 'replace' },
    { name: I18N.detailinfo.constants.riQiGeShiHua2, value: I18N.detailinfo.constants.riQiGeShiHua, realValue: 'SformatDate' },
    { name: I18N.detailinfo.constants.zhuanZiFuChuan2, value: I18N.detailinfo.constants.zhuanZiFuChuan, realValue: 'toString' },
    { name: I18N.detailinfo.constants.ziFuChuanZhuanShu2, value: I18N.detailinfo.constants.ziFuChuanZhuanShu, realValue: 'toDouble' },
    { name: I18N.detailinfo.constants.shuJuHeBing2, value: I18N.detailinfo.constants.shuJuHeBing, realValue: 'toJsonMerging' },
    { name: I18N.detailinfo.constants.ziDuanShuJuLei2, value: I18N.detailinfo.constants.ziDuanShuJuLei, realValue: 'toBoolean' },
    { name: I18N.detailinfo.constants.zhuanBuEr2, value: I18N.detailinfo.constants.zhuanBuEr, realValue: 'toBoolean' },
    { name: I18N.detailinfo.constants.zhuanShiJianXing2, value: I18N.detailinfo.constants.zhuanShiJianXing, realValue: 'toDateTime' }
];

const nomalChinese = [
    { name: and1, value: 'and' },
    { name: or1, value: 'or' }
];

let keywordFunctionArray = [];
for (let i = 0; i < ctrlList.length; i++) {
    keywordFunctionArray.push(`#${ctrlList[i].name}`);
}
localStorage.keywordFunctionArray = keywordFunctionArray;
// 把nomalChinese，ctrlList存本地，供初始化解析用
localStorage.cmCtrlList = JSON.stringify(ctrlList);
localStorage.cmNomalChinese = JSON.stringify(nomalChinese);

const codeKeywords = [
    // groovy 关键词
    'as',
    'catch',
    'def',
    'enum',
    'for',
    'import',
    'new',
    'super',
    'throws',
    'while',
    'assert',
    'class',
    'default',
    'extends',
    'goto',
    'in',
    'null',
    'switch',
    'trait',
    'break',
    'const',
    'do',
    'false',
    'if',
    'instanceof',
    'package',
    'this',
    'true',
    'case',
    'continue',
    'else',
    'finally',
    'implements',
    'interface',
    'return',
    'throw',
    'try',
    // java 关键词
    'abstract',
    'transient',
    'int',
    'strictfp',
    'synchronized',
    'boolean',
    'char',
    'do',
    'final',
    'private',
    'short',
    'void',
    'double',
    'long',
    'protected',
    'static',
    'volatile',
    'byte',
    'float',
    'native',
    'public',
    // JDK 常用类
    'System',
    'Runtime',
    'String',
    'StringBuffer',
    'StringBuilder',
    'Date',
    'DateFormat',
    'SimpleDateFormat',
    'Calendar',
    'GregorianGalendar',
    'Math',
    'Integer',
    'Double',
    'Float',
    'Boolean',
    'List',
    'HashMap',
    'Map',
    'ArrayList',
    'Arrays',
    'Random',
    'Iterator'
];

// const codePlaceholder = `/**
// * 脚本执行器
// * 请在此类的run方法中补您你需要实现的业务逻辑
// *
// * @param inputMap 入参集合
// * @return 出参集合
// */
// import com.alibaba.fastjson.*;
// import org.apache.commons.lang3.*;
// import java.text.SimpleDateFormat;

// public class GroovyEtlHandlerCallerImpl implements GroovyEtlHandlerCaller {

//    public Map < String, String > run(Map < String, String > inputMap) {
// 	   Map < String, String > outputMap = new HashMap < String, String > ();
// 	   // TODO 请在此处补充需要实现的业务逻辑，结果值put入outputMap中。
// 	   //获取脚本入参
// 	   String lzqtest = inputMap.get("S_DC_VS_MOBILE");
// 	   //结果字段出参设置
// 	   outputMap.put("S_DC_VS_MOBILE", "222");

// 	   return outputMap;
//    }
// }
// `;

const codePlaceholder = I18N.detailinfo.constants.jiaoBenZhiXingQi;

export { quickList, ctrlList, nomalChinese, codeKeywords, codePlaceholder };
