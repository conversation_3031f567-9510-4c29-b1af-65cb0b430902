/*
 * @CreatDate: 2020-12-28 13:42:12
 * @Describe: 第一步
 */

import I18N from '@/utils/I18N';
import { useState, useImperativeHandle, forwardRef } from 'react';
import { Input, Select, Radio, Upload, Icon, message, Ellipsis } from 'tntd';
import { bytesToSize } from '@/utils/utils';
import service from '../../service';
import { cloneDeep } from 'lodash';

const { Dragger } = Upload;

const Option = Select.Option;

let OneStep = (props, ref) => {
    const { callerList = [], style } = props;
    const [params, setParams] = useState({
        sourceUploadType: 1,
        id: null,
        sourceFtpAddress: null,
        sourceFile: null,
        sourceUser: null,
        sourcePwd: null
    });
    const changeField = (e, field) => {
        let obj = {};
        obj[field] = e;
        setParams({
            ...params,
            ...obj
        });
    };

    const [fileList, setFileList] = useState([]);
    const uploadProps = {
        showUploadList: false,
        multiple: true,
        accept: '.xlsx',
        beforeUpload: (file, list) => {
            let copyFileList = fileList.concat([]);
            list.forEach((item) => {
                const index = fileList.findIndex((k) => k.name === item.name);
                if (index >= 0) {
                    copyFileList[index] = item;
                } else {
                    copyFileList.push(item);
                }
            });
            setFileList(copyFileList);
            return false;
        }
    };

    const { sourceUploadType, id, sourceFtpAddress, sourceUser, sourcePwd, sourceFile } = params;

    useImperativeHandle(ref, () => ({
        getParams: () => {
            let filterParams;
            const obj = callerList.find((k) => k.id === id);
            if (sourceUploadType === 1) {
                filterParams = {
                    sourceUploadType,
                    sourceFtpAddress,
                    sourceFile,
                    sourceUser,
                    sourcePwd,
                    name: obj?.name,
                    type: obj?.type
                };
            } else {
                filterParams = {
                    sourceUploadType,
                    fileList,
                    name: obj?.name,
                    type: obj?.type
                };
            }

            return filterParams;
        },
        validateParams: () => {
            let flag = false;
            if (sourceUploadType === 1 && (!id || !sourceFtpAddress || !sourceFile || !sourceUser || !sourcePwd)) {
                flag = true;
            } else if (sourceUploadType === 2 && (!id || fileList.length === 0)) {
                flag = true;
            }
            if (flag) {
                message.warning(I18N.steps.one.cunZaiBiTianXiang);
                return false;
            }
            return true;
        },
        checkFtp: async () => {
            if (sourceUploadType === 1) {
                const obj = callerList.find((k) => k.id === id);
                const filterParams = {
                    sourceUploadType,
                    sourceFtpAddress,
                    sourceFile,
                    sourceUser,
                    sourcePwd,
                    name: obj?.name,
                    type: obj?.type
                };
                const res = await service.checkFtp(filterParams);
                if (res?.success) {
                    return true;
                }
                message.error(res?.msg);
                return false;
            }
            return true;
        }
    }));

    const deleteFile = (i) => {
        let copyFileList = cloneDeep(fileList);
        copyFileList.splice(i, 1);
        setFileList(copyFileList);
    };

    return (
        <div className="one-step" style={style}>
            <Radio.Group onChange={(e) => changeField(e.target.value, 'sourceUploadType')} value={sourceUploadType}>
                <Radio value={1}>{I18N.steps.one.fTPSF}</Radio>
                <Radio value={2}>{I18N.steps.one.benDiWenJian}</Radio>
            </Radio.Group>
            <div className="wrap">
                <div className="box">
                    <span className="label">
                        <b>*</b>
                        {I18N.addmodifymodal.index.fuWuJieKou}
                    </span>
                    <Select
                        showSearch
                        allowClear
                        dropdownMatchSelectWidth={false}
                        dropdownStyle={{ width: 350 }}
                        placeholder={I18N.steps.one.qingXuanZeFuWu}
                        optionFilterProp="children"
                        value={id ? id : undefined}
                        onChange={(e) => changeField(e, 'id')}>
                        {callerList.map((item, index) => {
                            return (
                                <Option value={item.id} key={index}>
                                    <Ellipsis
                                        title={
                                            <>
                                                {' '}
                                                {item.displayName}
                                                {item.type === 2 ? I18N.steps.one.zu : ''}
                                            </>
                                        }>
                                        {' '}
                                        {item.displayName}
                                        {item.type === 2 ? I18N.steps.one.zu : ''}
                                    </Ellipsis>
                                </Option>
                            );
                        })}
                    </Select>
                </div>
                {sourceUploadType === 1 && (
                    <>
                        <div className="box">
                            <span className="label">
                                <b>*</b>
                                {I18N.steps.one.fuWuQiDiZhi}
                            </span>
                            <Input
                                placeholder={I18N.steps.one.qingShuRuFuWu}
                                value={sourceFtpAddress}
                                onChange={(e) => changeField(e.target.value, 'sourceFtpAddress')}
                            />
                        </div>
                        <div className="box">
                            <span className="label">
                                <b>*</b>
                                {I18N.steps.one.yuanShuJuWenJian}
                            </span>
                            <Input
                                placeholder={I18N.steps.one.qingShuRuYuanShu}
                                value={sourceFile}
                                onChange={(e) => changeField(e.target.value, 'sourceFile')}
                            />
                        </div>
                        <div className="box">
                            <span className="label">
                                <b>*</b>
                                {I18N.steps.one.dengLuYongHuMing}
                            </span>
                            <Input
                                placeholder={I18N.steps.one.qingShuRuDengLu}
                                value={sourceUser}
                                onChange={(e) => changeField(e.target.value, 'sourceUser')}
                            />
                        </div>
                        <div className="box">
                            <span className="label">
                                <b>*</b>
                                {I18N.steps.one.dengLuMiMa}
                            </span>
                            <Input
                                placeholder={I18N.steps.one.qingShuRuYongHu}
                                value={sourcePwd}
                                onChange={(e) => changeField(e.target.value, 'sourcePwd')}
                            />
                        </div>
                    </>
                )}
                {sourceUploadType === 2 && (
                    <div className="box">
                        <span className="label" style={{ verticalAlign: 'top' }}>
                            <b>*</b>
                            {I18N.steps.one.shangChuanWenJian}
                        </span>
                        <Dragger {...uploadProps}>
                            <p className="ant-upload-drag-icon">
                                <Icon type="cloud-upload" />
                            </p>
                            <p className="ant-upload-text">{I18N.steps.one.dianJiShangChuanHuo}</p>
                            <p className="ant-upload-hint">{I18N.steps.one.qingShangChuanCS}</p>
                        </Dragger>
                        {fileList?.length > 0 &&
                            fileList.map((item, index) => (
                                <div className="upload-name" key={index}>
                                    <img src={require('../img/xlsx.svg')} />
                                    <Ellipsis title={item.name} widthLimit={420}>
                                        {' '}
                                        <span>{item.name}</span>
                                    </Ellipsis>
                                    <span className="size">{bytesToSize(item?.size)}</span>
                                    <Icon type="delete" onClick={() => deleteFile(index)} />
                                </div>
                            ))}
                    </div>
                )}
            </div>
        </div>
    );
};

OneStep = forwardRef(OneStep);

export default OneStep;
