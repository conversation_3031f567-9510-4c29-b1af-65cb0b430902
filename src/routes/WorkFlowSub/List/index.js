import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { TableContainer, Button, message, Popconfirm, QueryListScene, Ellipsis, HandleIcon, Icon } from 'tntd';
import { connect } from 'dva';
import history from '@/utils/history';
import QueryListWrapper from '@/components/QueryListWrapper';
import AssignModal from '@tddc/assign-modal';
import service from '../service';
import AddModify from './Modal/AddModify';
import otp from '../otp';
import { cloneDeep } from 'lodash';
import { applyPatch } from 'diff';

const { QueryForm, QueryList, Field, createActions } = QueryListScene;
const actions = createActions();

const SubList = (props) => {
    const { globalStore } = props;
    const { orgList, appList, allOrgList, allAppList, currentUser, currentApp, currentOrgCode } = globalStore;
    const [addModifyData, setAddModifyData] = useState();
    const [assignModalData, setAssignModalData] = useState(false);
    const [visible, setVisible] = useState(false);
    // 查询
    const query = (params) => {
        const { current, obj, total, pageSize = 10, ...rest } = params;
        const dParams = {
            appCode: currentApp.name || 'all',
            orgCode: currentOrgCode,
            curPage: current,
            pageSize,
            ...(obj || {}),
            ...rest
        };

        return service.workflowTemplateList(dParams).then(({ data }) => ({
            ...data,
            data: data.contents || [],
            current: data.curPage,
            pageSize: data.pageSize
        }));
    };

    // 删除
    const del = (record) => {
        service.subWorkFlowDel({ uuid: record.uuid }).then((res) => {
            if (res?.success) {
                message.success(res?.message || I18N.list.index.shanChuChengGong);
                actions.search({ current: 1 });
            } else {
                message.error(res?.message || I18N.list.index.shanChuShiBai);
            }
        });
    };

    const onSubmit = (data) => {
        service
            .authorize({
                appCodes: data.appKeys,
                orgCodes: data.checkedKeys,
                bizId: assignModalData.uuid
            })
            .then((res) => {
                if (res && res.success && res.code === 200) {
                    message.success(I18N.list.index.shouQuanChengGong);
                    setVisible(false);
                    actions.search({ current: 1 });
                }
            });
    };

    const goToAssign = (record) => {
        let data = cloneDeep(record);
        service.getAuthorize({ bizId: record.uuid }).then((v) => {
            data.appCodes = v?.data?.appCodes;
            data.orgCodes = v?.data?.orgCodes;
            setAssignModalData(data);
            setVisible(true);
        });
    };
    const columns = [
        {
            title: I18N.list.index.liuChengMuBanMing,
            dataIndex: 'displayName',
            width: 300,
            ellipsis: true,
            render: (displayName, record) => {
                return <Ellipsis key={displayName} title={displayName || '- -'} />;
            }
        },
        {
            title: I18N.list.index.liuChengMuBanBiao,
            dataIndex: 'code',
            width: 300,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis title={text || '- -'} />;
            }
        },
        {
            title: I18N.list.index.suoShuJiGou,
            width: 120,
            dataIndex: 'orgCode',
            render: (text, row) => {
                let orgName = allOrgList?.find((v) => v.code === text)?.name;
                return <Ellipsis title={orgName || '- -'} />;
            }
        },
        {
            title: I18N.addmodify.index.quDao,
            width: 120,
            dataIndex: 'appCode',
            render: (text, row) => {
                let appName = allAppList?.find((v) => v.name === text)?.displayName;
                return <Ellipsis title={appName || '- -'} />;
            }
        },
        {
            title: I18N.list.index.miaoShu,
            dataIndex: 'description',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.list.index.chuangJianRen,
            dataIndex: 'creator',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.list.index.chuangJianShiJian,
            dataIndex: 'gmtCreate',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return text || '- -';
            }
        },
        {
            title: I18N.list.index.xiuGaiRen,
            dataIndex: 'operator',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return <Ellipsis key={text} title={text || '- -'} />;
            }
        },
        {
            title: I18N.list.index.xiuGaiShiJian,
            dataIndex: 'gmtModify',
            width: 200,
            ellipsis: true,
            render: (text) => {
                return text || '- -';
            }
        },
        {
            title: I18N.list.index.caoZuo,
            width: 150,
            fixed: 'right',
            render: (record) => {
                return (
                    <HandleIcon num={3}>
                        <HandleIcon.Item title={I18N.list.index.bianPai}>
                            <Icon
                                type="bianpai"
                                onClick={() => {
                                    history.push(
                                        `/handle/workflow/sub/opera/edit?uuid=${record.uuid}&orgCode=${record.orgCode}&appCode=${record.appCode}`
                                    );
                                }}
                            />
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.list.index.chaKan}>
                            <Icon
                                type="profile"
                                onClick={() => {
                                    history.push(`/handle/workflow/sub/opera/view?uuid=${record.uuid}`);
                                }}
                            />
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.list.index.xiuGai}>
                            <Icon
                                type="form"
                                onClick={() => {
                                    setAddModifyData({
                                        ...record,
                                        titleType: 'update',
                                        title: I18N.list.index.xiuGai
                                    });
                                }}
                            />
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.list.index.shanChu}>
                            <Popconfirm
                                placement="topLeft"
                                title={
                                    // eslint-disable-next-line prettier/prettier
                                 <div>{I18N.list.index.queRenShanChu}<span style={{ display: 'inline-block', maxWidth: 150 }}><Ellipsis title={record?.displayName} widthLimit={150} /></span>{I18N.list.index.ma}</div>
                                }
                                overlayStyle={{
                                    width: 280
                                }}
                                onConfirm={() => {
                                    del(record);
                                }}>
                                <Icon type="delete" />
                            </Popconfirm>
                        </HandleIcon.Item>
                        <HandleIcon.Item title={I18N.list.index.shouQuan}>
                            <Icon
                                type="user-privilege"
                                onClick={() => {
                                    goToAssign(record);
                                }}
                            />
                        </HandleIcon.Item>
                    </HandleIcon>
                );
            }
        }
    ];

    return (
        <QueryListScene query={query} memory actions={actions}>
            <QueryForm
                extraActions={
                    <>
                        <Button
                            onClick={() => {
                                setAddModifyData({
                                    titleType: 'add',
                                    title: I18N.list.index.xinZeng
                                });
                            }}
                            icon="plus"
                            type="primary">
                            {I18N.list.index.xinZeng}
                        </Button>
                    </>
                }>
                <Field
                    type="selectInput"
                    name="obj"
                    props={{
                        placeholder: I18N.list.index.shuRuSouSuoNei,
                        style: {
                            width: otp.list.selectInputWidth1
                        },
                        onChange: (vals) => {
                            if (!vals.displayName && !vals.code) {
                                const allVals = actions.getFormData();
                                const { obj, ...rest } = allVals;
                                actions.setFormData({ ...rest });
                            }
                        },
                        onPressEnter: (e) => {
                            const vals = actions.getFormData();
                            actions.search({ ...vals, current: 1 });
                        },
                        addonBeforeStyle: {
                            width: otp.list.selectInputWidth2
                        },
                        options: [
                            { label: I18N.list.index.liuChengMuBanMing, value: 'displayName' },
                            { label: I18N.list.index.liuChengMuBanBiao, value: 'code' }
                        ]
                    }}
                />
            </QueryForm>
            <QueryList columns={columns} rowKey="uuid" scroll={{ x: 1700 }} />
            <AddModify
                visible={!!addModifyData}
                data={addModifyData}
                search={actions.search}
                onCancel={() => {
                    setAddModifyData();
                }}
            />
            <AssignModal
                visible={visible}
                dataItem={assignModalData}
                orgList={orgList}
                title={I18N.list.index.liuChengMuBanShou}
                close={() => setVisible(false)}
                // search={editorAreaRef.current.reset && (() => editorAreaRef.current.reset())}
                disabled={!assignModalData?.canWriter}
                appList={appList
                    .filter((v) => v.key)
                    .map((item) => {
                        return {
                            label: item?.name,
                            value: item?.key
                        };
                    })}
                onSubmit={onSubmit}
                // lang={personalMode?.lang}
                orgTitle={I18N.list.index.shouQuanKeYongJi}
                appTitle={I18N.list.index.shouQuanKeYongQu}
                orgCheckboxTitle={I18N.list.index.quanBuJiGouKe}
                appCheckboxTitle={I18N.list.index.quanBuQuDaoKe}
            />
        </QueryListScene>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(QueryListWrapper(TableContainer(SubList), actions));
