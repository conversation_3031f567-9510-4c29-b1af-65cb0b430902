/*
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 应用服务列表
 */

import I18N from '@/utils/I18N';
import React, { PureComponent, Suspense } from 'react';
import { connect } from 'dva';
import copy from 'copy-to-clipboard';
import {
    Button,
    Table,
    Pagination,
    message,
    TableContainer,
    Select,
    Input,
    Switch,
    Tooltip,
    Modal,
    HandleIcon,
    Icon,
    Ellipsis
} from 'tntd';
import { appServiceListAPI, dataServiceListAPI } from '@/services';
import { checkFunctionHasPermission } from '@/utils/permission';
import { routeDisplayConfigParser } from '@/utils/routeDisplayConfigParser';
import NoPermission from '@/components/NoPermission';
import { sortableColumnTitleRenderer } from '@/utils/sortableColumnTitleRenderer';
import { getUrlKey } from '@/utils/utils';

const AddModifyModal = React.lazy(() => import('./Inner/AddModifyModal'));

const Option = Select.Option;

class AppServiceList extends PureComponent {
    constructor(props) {
        super(props);
        const { dispatch } = props;
        const displayName = getUrlKey('displayName');
        dispatch({
            type: 'appServiceList/setAttrValue',
            payload: {
                searchParams: {
                    displayName
                }
            }
        });
    }

    state = {
        threeServiceList: [],
        downloadDialogVisible: false,
        downloadOrg: undefined,
        resetUuid: null
    };

    componentDidMount() {
        this.timer = setInterval(() => {
            const { globalStore } = this.props;
            const { menuTreeReady } = globalStore;
            if (menuTreeReady) {
                clearInterval(this.timer);
                if (checkFunctionHasPermission('TZ0401', 'query')) {
                    this.search();
                    this.getServiceList();
                }
            }
        }, 100);
    }

    componentDidUpdate(preProps) {
        const appName = preProps.globalStore.currentApp.name;
        const { relationOrgList } = this.props.appServiceListStore;
        const nextAppName = this.props.globalStore.currentApp.name;
        const allMap = preProps.globalStore.allMap;
        const nextAllMap = this.props.globalStore.allMap;

        if (appName !== nextAppName) {
            this.search();
        }

        if (!this.state.downloadOrg || allMap !== nextAllMap) {
            this.setState({
                downloadOrg: relationOrgList[0]?.code
            });
        }
    }

    componentWillUnmount() {
        this.props.dispatch({
            type: 'appServiceList/reset'
        });
    }

    getServiceList = () => {
        dataServiceListAPI.getList().then((res) => {
            if (res && res.success) {
                if (!res.data) return;
                this.setState({
                    threeServiceList: res.data.contents ? res.data.contents : []
                });
            } else {
                message.error(res.msg);
            }
        });
    };

    // 查询
    search = (curPage, pageSize, noLoading) => {
        const { dispatch, appServiceListStore } = this.props;
        const { searchParams } = appServiceListStore;
        const status = this.props.location.search == '?status=1' ? 1 : undefined;
        if (status) {
            this.changeField(status, 'select', 'status', true);
            this.props.history.push(this.props.location.pathname);
        }
        dispatch({
            type: 'appServiceList/getList',
            payload: {
                curPage: curPage ? curPage : 1,
                pageSize: pageSize ? pageSize : searchParams.pageSize,
                noLoading,
                sortField: searchParams.sortField,
                sortRule: searchParams.sortRule
            }
        });
    };

    // 分页
    paginationOnChange(curPage, pageSize) {
        this.search(curPage, pageSize);
    }

    // 新增
    add = () => {
        const { dispatch } = this.props;
        dispatch({
            type: 'appServiceList/setAttrValue',
            payload: {
                dialogShow: {
                    addEditModal: true
                },
                modalType: 1
            }
        });
    };

    // 修改
    modify(record) {
        const {
            uuid,
            displayName,
            name,
            serviceName,
            channelName,
            cacheDay,
            retry,
            timeout,
            switchFlag,
            providerDisplayName,
            dataType,
            responseType,
            routeConfigStatusList,
            appName,
            serviceProportion
        } = record;
        const { dispatch } = this.props;
        const { threeServiceList } = this.state;
        dispatch({
            type: 'appServiceList/setAttrValue',
            payload: {
                dialogShow: {
                    addEditModal: true
                },
                modalType: 2,
                updateId: uuid,
                dialogData: {
                    addEditModalData: {
                        uuid,
                        name, // 业务系统服务标识
                        displayName, // 业务系统服务名称
                        serviceName, // 三方服务接口名称
                        channelName, // 业务系统标识
                        cacheDay, // 数据缓存天数
                        serviceProportion, // 数据源服务接口名称分流集合
                        retry, // 重试次数
                        timeout, // 超时时间
                        switchFlag, // 调用异常是否切换 1是，2否
                        providerName: providerDisplayName, // 供应商名称
                        dataType, // 数据类型
                        responseType, // 返回类型
                        routeConfig: routeConfigStatusList.map(({ name }) => name),
                        routeDisplayConfig: routeConfigStatusList.map(routeDisplayConfigParser(threeServiceList)),
                        appName
                    }
                }
            }
        });
    }

    // 查看
    look(record) {
        const {
            uuid,
            displayName,
            name,
            serviceName,
            channelName,
            cacheDay,
            retry,
            timeout,
            switchFlag,
            providerDisplayName,
            dataType,
            responseType,
            appName,
            serviceProportion,
            routeConfigStatusList
        } = record;
        const { dispatch } = this.props;
        const { threeServiceList } = this.state;

        dispatch({
            type: 'appServiceList/setAttrValue',
            payload: {
                dialogShow: {
                    addEditModal: true
                },
                modalType: 3,
                dialogData: {
                    addEditModalData: {
                        uuid,
                        name, // 业务系统服务标识
                        displayName, // 业务系统服务名称
                        serviceName, // 三方服务接口名称
                        channelName, // 业务系统标识
                        cacheDay, // 数据缓存天数
                        retry, // 重试次数
                        timeout, // 超时时间
                        serviceProportion, // 数据源服务接口名称分流集合
                        switchFlag, // 调用异常是否切换 1是，2否
                        providerName: providerDisplayName, // 供应商名称
                        dataType, // 数据类型
                        responseType, // 返回类型
                        routeConfig: routeConfigStatusList.map(({ name }) => name),
                        routeDisplayConfig: routeConfigStatusList.map(routeDisplayConfigParser(threeServiceList)),
                        appName
                    }
                }
            }
        });
    }

    // 删除
    delete(record) {
        appServiceListAPI.deleteData({ uuid: record.uuid }).then((res) => {
            if (res && res.success) {
                message.success(res.msg);
                const { dispatch, appServiceListStore } = this.props;
                const { searchParams, tableList } = appServiceListStore;
                let { curPage, pageSize } = searchParams;
                if (tableList.length === 1 && curPage > 1) curPage = curPage - 1;
                dispatch({
                    type: 'appServiceList/getList',
                    payload: {
                        curPage,
                        pageSize,
                        sortField: searchParams.sortField,
                        sortRule: searchParams.sortRule
                    }
                });
            } else {
                message.error(res.msg);
            }
        });
    }

    // 上下线切换
    upDownLine(e, record) {
        const params = {
            uuid: record.uuid,
            status: e ? 1 : 2
        };
        appServiceListAPI.setOnline(params).then((res) => {
            if (res && res.success) {
                message.success(I18N.appservicelist.caoZuoChengGong); // 操作成功
                const { appServiceListStore } = this.props;
                const { searchParams } = appServiceListStore;
                const { curPage, pageSize } = searchParams;
                this.search(curPage, pageSize, 'noLoading');
            } else {
                message.error(res.msg);
            }
        });
    }

    // 改变参数
    changeField(e, type, field, noSearch) {
        const { dispatch } = this.props;
        let val = null;
        let obj = {};
        if (type === 'select') val = e;
        if (type === 'input') val = e.target.value;
        if (!e) {
            obj[field] = null;
        } else {
            obj[field] = val;
        }
        dispatch({
            type: 'appServiceList/setAttrValue',
            payload: {
                searchParams: {
                    ...obj
                }
            }
        });
        if (type !== 'input' && !noSearch) {
            this.search();
        }
    }

    handleTableSort = (pagination, filters, sorter) => {
        const { dispatch, appServiceListStore } = this.props;
        const { searchParams } = appServiceListStore;

        if (sorter && sorter.order) {
            dispatch({
                type: 'appServiceList/getList',
                payload: {
                    curPage: 1,
                    pageSize: searchParams.pageSize,
                    sortField: sorter.field === 'appDisplayName' ? 'appName' : sorter.field,
                    sortRule: sorter.order === 'ascend' ? 'asc' : 'desc'
                }
            });
        } else {
            dispatch({
                type: 'appServiceList/getList',
                payload: {
                    curPage: 1,
                    pageSize: searchParams.pageSize,
                    sortField: undefined,
                    sortRule: undefined
                }
            });
        }
    };

    // 复制密钥
    clipboardHandle() {
        message.success(I18N.appservicelist.fuZhiChengGong); // 复制成功
    }

    // 重置密钥
    resetKey(uuid) {
        appServiceListAPI.setSecretKey({ uuid }).then((res) => {
            if (res && res.success) {
                message.success(I18N.appservicelist.caoZuoChengGong); // 操作成功
                const { appServiceListStore } = this.props;
                const { searchParams } = appServiceListStore;
                const { curPage, pageSize } = searchParams;
                this.search(curPage, pageSize, 'noLoading');
            } else {
                message.error(res.msg);
            }
        });
    }

    showDownloadFileDialog = (record) => {
        const { dispatch } = this.props;
        appServiceListAPI.getRelationOrgList({ uuid: record?.uuid }).then((res) => {
            dispatch({
                type: 'appServiceList/setAttrValue',
                payload: {
                    relationOrgList: res?.data || []
                }
            });
        });
        this.downloadRecord = record;
        this.setState({
            downloadDialogVisible: true
        });
    };

    handleDownloadDialogConfirm = () => {
        const { downloadOrg } = this.state;

        if (downloadOrg) {
            this.downloadFile(this.downloadRecord, downloadOrg);
            this.setState({
                downloadDialogVisible: false
            });
        }
    };

    handleDownloadDialogHide = () => {
        this.setState({
            downloadDialogVisible: false
        });
    };

    downloadFile = (record, organizationCode) => {
        const { uuid, displayName, appName } = record;

        appServiceListAPI.downloadDoc(
            { uuid, appNames: appName, organizationCode },
            I18N.template(I18N.appservicelist.dISPL, { val1: displayName })
        );
    };

    // 重新获取密钥
    refreshKey = (record) => {
        const { uuid, name } = record || {};

        appServiceListAPI
            .reBuildSecretKey({
                uuid,
                name
            })
            .then((res) => {
                if (res.success) {
                    message.success(I18N.appservicelist.zhongZhiMiYaoCheng);
                    this.search();
                } else {
                    message.error(res.message || I18N.appservicelist.zhongZhiMiYaoShi);
                }
            })
            .catch((e) => {
                message.error(e.message || I18N.appservicelist.zhongZhiMiYaoShi);
            })
            .finally(() => {
                this.setState({
                    resetUuid: null
                });
            });
    };

    render() {
        const { appServiceListStore, globalStore } = this.props;
        const { threeServiceList, downloadDialogVisible, downloadOrg, resetUuid } = this.state;
        const { allMap, providerList, menuTreeReady } = globalStore;
        const { tableList, searchParams, total, loading, dialogShow, relationOrgList } = appServiceListStore;
        console.log(relationOrgList, 'relationOrgList');
        const { displayName, serviceName, status } = searchParams;

        const columns = [
            {
                title: sortableColumnTitleRenderer(I18N.appservicelist.fuWuMingCheng), // 业务系统服务名称
                dataIndex: 'displayName',
                key: 'displayName',
                width: 160,
                sorter: true,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Ellipsis title={text} widthLimit={120} />;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: sortableColumnTitleRenderer(I18N.appservicelist.fuWuBiaoZhi), // 业务系统服务标识
                dataIndex: 'name',
                key: 'name',
                width: 200,
                sorter: true,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 15) {
                        dom = <Ellipsis title={text} widthLimit={160} />;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.appservicelist.quDao,
                dataIndex: 'appDisplayName',
                key: 'appDisplayName',
                width: 160,
                render: (text) => {
                    let dom = text;
                    if (text && text.length > 10) {
                        dom = <Ellipsis title={text} widthLimit={120} />;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.appservicelist.miYao,
                dataIndex: 'secretKey',
                width: 160,
                render: (secretKey, record) => {
                    return (
                        <div className="secret-key">
                            <Tooltip title={I18N.appservicelist.fuZhiMiYao}>
                                <span
                                    className="key"
                                    onClick={() => {
                                        copy(secretKey);
                                        message.success(I18N.appservicelist.fuZhiChengGong);
                                    }}
                                />
                            </Tooltip>
                            <Tooltip title={I18N.appservicelist.zhongZhiMiYao}>
                                <span
                                    className={`reset-key ${resetUuid && record.uuid === resetUuid ? 'disabled' : ''}`}
                                    onClick={() => {
                                        if (resetUuid) {
                                            return;
                                        }
                                        this.setState({
                                            resetUuid: record.uuid
                                        });
                                        this.refreshKey(record);
                                    }}
                                />
                            </Tooltip>
                        </div>
                    );
                }
            },
            {
                title: I18N.appservicelist.shuJuYuanFuWu, // 三方服务名称
                width: 220,
                render: (text, record) => {
                    let serviceDisplayNames =
                        record.serviceProportion && record.serviceProportion.map((res) => res.serviceDisplayName).join(',');
                    let dom = serviceDisplayNames;

                    if (serviceDisplayNames && serviceDisplayNames.length > 10) {
                        dom = <Ellipsis title={serviceDisplayNames} widthLimit={180} />;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: I18N.appservicelist.shuJuLeiXing, // 数据类型
                render: (text, record) => {
                    // 转换数据类型为中文
                    let dataTypeNames = [];
                    record.serviceProportion &&
                        record.serviceProportion.forEach((res) => {
                            if (allMap && allMap.serviceTypeList) {
                                const obj = allMap.serviceTypeList.find((item) => item.dataType === res.serviceType);
                                if (obj) {
                                    dataTypeNames.push(obj.name);
                                }
                            }
                        });
                    dataTypeNames = dataTypeNames.join(',');
                    let dom = dataTypeNames;
                    if (dataTypeNames && dataTypeNames.length > 10) {
                        dom = <Ellipsis title={dataTypeNames} widthLimit={125} />;
                    }
                    return dom ? dom : '--';
                }
            },
            {
                title: sortableColumnTitleRenderer(I18N.appservicelist.zhuangTai), // 服务状态
                dataIndex: 'status',
                key: 'status',
                width: 150,
                sorter: true,
                render: (text, record) => {
                    const map = {
                        '-1': I18N.appservicelist.shanChu, // 已删除
                        '2': I18N.appservicelist.xiaXian, // 已下线
                        '1': I18N.appservicelist.shangXian // 已上线
                    };
                    let dom = (
                        <Switch
                            className="u-checked"
                            checkedChildren={I18N.appservicelist.shangXian} // 已上线
                            unCheckedChildren={I18N.appservicelist.xiaXian} // 已下线
                            checked={text === 1}
                            onChange={(e) => {
                                if (!checkFunctionHasPermission('TZ0401', 'online')) {
                                    return message.info(I18N.appservicelist.wuQuanXianCaoZuo); // 暂无权限
                                }
                                this.upDownLine(e, record);
                            }}
                        />
                    );
                    return text === -1 ? map[text] : dom;
                }
            },
            {
                title: sortableColumnTitleRenderer(I18N.appservicelist.chuangJianShiJian),
                dataIndex: 'gmtCreate',
                key: 'gmtCreate',
                sorter: true,
                width: 190
            },
            {
                title: I18N.appservicelist.chuangJianRen,
                dataIndex: 'creator',
                width: 200,
                key: 'creator'
            },
            {
                title: sortableColumnTitleRenderer(I18N.appservicelist.xiuGaiShiJian),
                width: 190,
                dataIndex: 'gmtModify',
                sorter: true
            },
            {
                title: I18N.appservicelist.xiuGaiRen,
                dataIndex: 'operator',
                width: 200,
                key: 'operator'
            },
            {
                title: I18N.appservicelist.caoZuo, // 操作
                dataIndex: 'operate',
                key: 'operate',
                width: 160,
                fixed: 'right',
                render: (text, record) => {
                    let dom = (
                        <HandleIcon>
                            {record.status == 1
                                ? null
                                : checkFunctionHasPermission('TZ0401', 'modify') && (
                                      <HandleIcon.Item title={I18N.appservicelist.xiuGai}>
                                          <Icon
                                              type="form"
                                              onClick={() => {
                                                  this.modify(record);
                                              }}
                                          />
                                      </HandleIcon.Item>
                                  )}

                            {checkFunctionHasPermission('TZ0401', 'look') && (
                                <HandleIcon.Item title={I18N.appservicelist.chaKan}>
                                    <Icon
                                        type="profile"
                                        onClick={() => {
                                            this.look(record);
                                        }}
                                    />
                                </HandleIcon.Item>
                            )}
                            {record.status == 1
                                ? null
                                : checkFunctionHasPermission('TZ0401', 'delete') && (
                                      <HandleIcon.Item title={I18N.appservicelist.shanChu}>
                                          <Icon
                                              type="delete"
                                              onClick={() => {
                                                  Modal.confirm({
                                                      title: I18N.appservicelist.queDingShanChuCi,
                                                      onOk: () => {
                                                          this.delete(record);
                                                      }
                                                  });
                                              }}
                                          />
                                      </HandleIcon.Item>
                                  )}
                            <HandleIcon.Item title={I18N.appservicelist.xiaZai}>
                                <Icon type="download-file" onClick={() => this.showDownloadFileDialog(record)} />
                            </HandleIcon.Item>
                        </HandleIcon>
                    );
                    return dom;
                }
            }
        ];

        return (
            <div className="p-app-service">
                <Modal
                    title={I18N.appservicelist.xuanZeJiGouBing}
                    visible={downloadDialogVisible}
                    onOk={this.handleDownloadDialogConfirm}
                    onCancel={this.handleDownloadDialogHide}>
                    <div className="item">
                        <span>{I18N.appservicelist.jiGou}：</span>
                        <Select
                            value={downloadOrg}
                            style={{
                                width: '200px'
                            }}
                            onChange={(value) => {
                                this.setState({
                                    downloadOrg: value
                                });
                            }}>
                            {relationOrgList?.map((org) => (
                                <Select.Option key={org.code} value={org.code}>
                                    {org.name}
                                </Select.Option>
                            ))}
                        </Select>
                    </div>
                </Modal>
                {menuTreeReady && checkFunctionHasPermission('TZ0401', 'query') && (
                    <div className="page-global-body">
                        <div className="page-global-search">
                            <div className="item">
                                <Input.Search
                                    placeholder={I18N.appservicelist.qingShuRuFuWu} // 请输入服务名称
                                    value={displayName}
                                    onChange={(e) => this.changeField(e, 'input', 'displayName')}
                                    onPressEnter={() => this.search()}
                                    onSearch={() => this.search()}
                                />
                            </div>
                            <div className="item">
                                <Select
                                    showSearch
                                    allowClear
                                    dropdownMatchSelectWidth={false}
                                    dropdownStyle={{ width: 350 }}
                                    style={{ width: '220px' }}
                                    placeholder={I18N.appservicelist.qingXuanZeShuJu} // 请选择三方数据名称
                                    optionFilterProp="children"
                                    value={serviceName ? serviceName : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'serviceName')}>
                                    {threeServiceList &&
                                        threeServiceList.map((item, index) => {
                                            return (
                                                <Option value={item.name} key={index}>
                                                    {item.displayName}
                                                </Option>
                                            );
                                        })}
                                </Select>
                            </div>
                            <div className="item">
                                <Select
                                    showSearch
                                    allowClear
                                    dropdownMatchSelectWidth={false}
                                    placeholder={I18N.appservicelist.qingXuanZeFuWu} // 请选择服务状态
                                    optionFilterProp="children"
                                    value={status ? status : undefined}
                                    onChange={(e) => this.changeField(e, 'select', 'status')}>
                                    <Option value={1}>
                                        {I18N.appservicelist.shangXian}
                                        {/* 已上线 */}
                                    </Option>
                                    <Option value={2}>
                                        {I18N.appservicelist.xiaXian}
                                        {/* 已下线 */}
                                    </Option>
                                </Select>
                            </div>
                            {/* <div className="item">
                                <Button
                                    type="primary"
                                    // loading={loading}
                                    onClick={() => this.search()}>
                                    {I18N.appservicelist.chaXun}
                                </Button>
                            </div> */}
                            {checkFunctionHasPermission('TZ0401', 'add') && (
                                <div className="item right">
                                    <Button type="primary" icon="plus" onClick={this.add}>
                                        {/* 新增 */}
                                        {I18N.appservicelist.xinZeng}
                                    </Button>
                                </div>
                            )}
                        </div>
                        <div className="page-global-body-in">
                            <div className="page-global-body-main">
                                <Table
                                    rowKey={(record) => record.uuid}
                                    className="table-card-body"
                                    columns={columns}
                                    dataSource={tableList}
                                    pagination={false}
                                    loading={loading}
                                    scroll={{ x: 2100 }}
                                    onChange={this.handleTableSort}
                                />
                                <div className="page-global-body-pagination">
                                    <span className="ml20">{I18N.template(I18N.appservicelist.gongTOTA, { val1: total })}</span>
                                    <Pagination
                                        showSizeChanger
                                        showQuickJumper
                                        current={searchParams.curPage}
                                        pageSize={searchParams.pageSize}
                                        total={total}
                                        onChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                        onShowSizeChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                )}
                {menuTreeReady && !checkFunctionHasPermission('TZ0401', 'query') && <NoPermission />}
                {dialogShow.addEditModal && (
                    <Suspense fallback={null}>
                        <AddModifyModal providerList={providerList} />
                    </Suspense>
                )}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    appServiceListStore: state.appServiceList
}))(TableContainer(AppServiceList));
