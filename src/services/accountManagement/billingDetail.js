import { getUrl, deleteEmptyObjItem } from '../../utils/common';
import request, { downloadFileHandle } from '../../utils/request';

// 外部计费列表
const getExternalList = async (params) => {
    return request(getUrl('/charge/getContractList', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

// 导出外部计费列表
const exportExternal = async (params, name, fileType) => {
    return downloadFileHandle(
        getUrl('/charge/contractList/export', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        name,
        fileType
    );
};

// 外部计费弹框列表
const getModalExternalList = async (params) => {
    return request(getUrl('/charge/getContractBillList', deleteEmptyObjItem(params)), {
        method: 'GET'
    });
};

// 导出外部计费明细列表
const exportExternalDetail = async (params, name, fileType) => {
    return downloadFileHandle(
        getUrl('/charge/contractBillList/export', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        name,
        fileType
    );
};

export default {
    getExternalList,
    exportExternal,
    getModalExternalList,
    exportExternalDetail
};
