import { getUrl, deleteEmptyObjItem } from '../../utils/common';
import request from '../../utils/request';
import { getHeader } from '@/utils/common';

// 获取大盘数据
const getChannelInvokeStatistic = async (params) => {
	return request(getUrl('/statistics/channel/getChannelInvokeStatistic', deleteEmptyObjItem(params)), {
		method: 'GET',
		headers: getHeader()
	});
};

// 获取三方服务明细和调用机构明细
const getChannelDetailByType = async (params) => {
	return request(getUrl('/statistics/channel/getChannelDetailByType', deleteEmptyObjItem(params)), {
		method: 'GET',
		headers: getHeader()
	});
};

export default {
	getChannelInvokeStatistic,
	getChannelDetailByType
};
