import { getHeader, getUrl, deleteEmptyObjItem } from '../../utils/common';
import request, { downloadFileHandle, downloadPostFileHandle, PostForm } from '../../utils/request';

// 获取表格数据
const getList = async (params) => {
    return request(
        getUrl('/bridgeApi/serviceConfig/list', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 获取所有三方数据
const getListAll = async (params) => {
    params.includeDelete = true;
    return request(
        getUrl('/bridgeApi/serviceConfig/listAll', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 获取所有三方数据 数据源服务接口列表
const getListAll2 = async (params = {}) => {
    params.includeDelete = true;
    return request(
        getUrl('/bridgeApi/serviceConfig/list', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 调用方服务修改操作获取所有可用数据源信息
const getUpdateServiceInfo = async (params = {}) => {
    return request(
        getUrl('/bridgeApi/serviceConfig/getUpdateServiceInfo', params),
        {
            method: 'GET'
        },
        true
    );
};

// 根据ID获取数据服务
const getDetail = async (params) => {
    return request(
        getUrl('/bridgeApi/serviceConfig/getById', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 新增数据
const addData = async (params) => {
    return request(
        '/bridgeApi/serviceConfig/add',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 删除数据
const deleteData = async (params) => {
    return request(
        '/bridgeApi/serviceConfig/delete',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

// 更新数据
const updateData = async (params) => {
    return request(
        '/bridgeApi/serviceConfig/update',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 复制数据
const copyData = async (params) => {
    return request(
        '/bridgeApi/serviceConfig/copy',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 上下线
const setOnline = async (params) => {
    return request(
        '/bridgeApi/serviceConfig/online',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 导出
const exportData = async (params, name, fileType) => {
    return downloadFileHandle(
        getUrl('/bridgeApi/serviceConfig/export', params),
        {
            method: 'GET'
        },
        name,
        fileType
    );
};

// 导入
const importData = async (params) => {
    return request(
        '/bridgeApi/serviceConfig/import',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 更新mock配置
const updateMockConfig = async (params) => {
    return request(
        '/bridgeApi/serviceConfig/updateMockConfig',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

// 查询服务mockData
const getMockData = async (params) => {
    return request(
        getUrl('/bridgeApi/serviceConfig/listMockData', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 新增mock配置
const addMockData = async (params) => {
    return request(
        '/bridgeApi/serviceConfig/addMockData',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

// 修改mock配置
const updateMockData = async (params) => {
    return request(
        '/bridgeApi/serviceConfig/updateMockData',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

// 删除mock配置
const deleteMockData = async (params) => {
    return request(
        '/bridgeApi/serviceConfig/deleteMockData',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

// 导出mock模板
const exportMockDataTemplate = async (params) => {
    return downloadFileHandle(
        getUrl('/bridgeApi/serviceConfig/exportMockDataTemplate', params),
        {
            method: 'GET'
        },
        params.fileName,
        params.fileType,
        null,
        true
    );
};

// 导入mock配置
const importMockData = async (params) => {
    return request(
        '/bridgeApi/serviceConfig/importMockData',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

// 删除延迟合同
const deleteDelayContract = async (params) => {
    return request(
        '/bridgeApi/serviceConfig/commonStore/delete',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

// 缓存有效期
const saveSetting = async (params) => {
    return request(
        '/bridgeApi/serviceConfig/updateCacheDay',
        {
            method: 'POST',
            body: deleteEmptyObjItem(params)
        },
        true
    );
};

const exportRecord = async (params, name, fileType, errorMsg) => {
    return downloadFileHandle(
        getUrl('/bridgeApi/serviceConfig/export', params),
        {
            method: 'GET'
        },
        name,
        fileType,
        errorMsg,
        true
    );
};

const importRecord = async (params, fileList, file) => {
    return PostForm('/bridgeApi/serviceConfig/import', 'POST', deleteEmptyObjItem(params), fileList, file, true);
};

const getIndexPackage = async (params) => {
    return request(
        getUrl('/bridgeApi/serviceConfig/captain/indexPackage', params),
        {
            method: 'GET'
        },
        true
    );
};

const getRelationInputConfig = async (params) => {
    return request(
        getUrl('/bridgeApi/contract/select', params),
        {
            method: 'GET'
        },
        true
    );
};

// const getDocumentType = async (params) => {
// 	return request(getUrl('/datalandApi/documentType/get', params), {
// 		method: 'GET',
//         headers: { ...getHeader() },
// 	}, true);
//  };
const getDocumentType = async (params) => {
    return request(
        '/datalandApi/documentType/get',
        {
            method: 'POST',
            // headers: getHeader(),
            headers: { ...getHeader(), 'Content-Type': 'application/json' },
            body: { ...params }
        },
        true
    );
};
// const getDocumentType = async (params) => {
//     return request('/datalandApi/documentType/get', {
//         method: 'POST',
//         headers: getHeader(),
//         // headers: { ...getHeader(), 'Content-Type': 'application/json' },
//         body: { ...params }
//     }, true);
// }

const getDatalandAble = async (params) => {
    return request(
        getUrl('/captainApi/captain/common/app/datalandable', params),
        {
            method: 'GET'
        },
        true
    );
};
export default {
    getList,
    addData,
    deleteData,
    updateData,
    copyData,
    setOnline,
    getDetail,
    exportData,
    importData,
    getListAll,
    getListAll2,
    getUpdateServiceInfo,
    updateMockConfig,
    getMockData,
    addMockData,
    updateMockData,
    deleteMockData,
    exportMockDataTemplate,
    importMockData,
    deleteDelayContract,
    saveSetting,
    exportRecord,
    importRecord,
    getIndexPackage,
    getRelationInputConfig,
    getDocumentType,
    getDatalandAble
};
