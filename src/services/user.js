import { getHeader, getUrl, deleteEmptyObjItem } from '../utils/common';
import <PERSON><PERSON> from 'universal-cookie';
import request from '../utils/request';

const getAuth = async(params) => {
	return request(getUrl('/auth', deleteEmptyObjItem(params)), {
		method: 'GET',
		headers: getHeader()
	});
};

const signIn = async(param) => {
	return request('/bridgeApi/user/login', {
		method: 'POST',
		headers: getHeader(),
		body: { ...param }
	}, true);
};

const signOut = async(params) => {
	return request('/bridgeApi/user/logout', {
		method: 'POST',
		headers: getHeader(),
		body: { ...params }
	}, true);
};

// 生成一次性token
const auth = async(params) => {
	return request('/bridgeApi/user/getAuthCode', {
		method: 'POST',
		headers: getHeader(),
		body: { ...params }
	}, true);
};

const getUserInfo = async(param) => {
	return request(getUrl('/bridgeApi/userCenter/getUserInfo', param), {
		method: 'GET',
		headers: getHeader()
	}, true);
};

const getUserMenuTree = async(params) => {
	return request(getUrl('/bridgeApi/user/menuTree', params), {
		method: 'GET',
		headers: getHeader()
	}, true);
};

/**
 * @description: 统一登录接口
 * @param params
 * @returns {Promise<Object>}
 */
const userLogin = async(params) => {
	return request(getUrl('/bridgeApi/user/login'), {
		method: 'POST',
		body: params
	}, true);
};

const getAppByOrgId = async(params) => {
	return request(getUrl('/bridgeApi/user/app', params), {
		method: 'GET',
		headers: getHeader()
	}, true);
};

export default {
	signIn,
	signOut,
	getUserInfo,
	getAuth,
	getUserMenuTree,
	userLogin,
	auth,
	getAppByOrgId
};
