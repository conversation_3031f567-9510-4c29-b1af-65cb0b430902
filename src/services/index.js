import userAPI from './user';
import baseAPI from './base';
import appChannelAPI from './dashboard/appChannel';
import dataServiceAPI from './dashboard/dataService';
import dataServiceListAPI from './supplierManagement/dataServiceList';
import supplierListAPI from './supplierManagement/supplierList';
import externalAPI from './accountManagement/external';
import internalAPI from './accountManagement/internal';
import billingDetailAPI from './accountManagement/billingDetail';
import appServiceListAPI from './appServiceCenter/appServiceList';
import serviceGroupAPI from './appServiceCenter/serviceGroup';
import threeCallDetailAPI from './dataManagement/threeCallDetail';
import businessChannelAPI from './dataManagement/businessChannel';
import exceptionListAPI from './dataManagement/exceptionList';
import pageQueryAPI from './dataManagement/pageQuery';
import threeDataMonitorAPI from './monitorWarning/threeDataMonitor';
import threeDataRemainWarnAPI from './monitorWarning/threeDataRemainWarn';
import supplierWarningAPI from './monitorWarning/supplierWarning';
import costAnalysisAPI from './qualityAnalysis/costAnalysis';
import agingAnalysisAPI from './qualityAnalysis/agingAnalysis';
import etlAPI from './supplierManagement/etl';
import systemFieldsAPI from './supplierManagement/systemFields';
import contractListAPI from './supplierManagement/contractList';
import captainAPI from './captain';
import referenceAPI from './reference'; // 关联引用相关接口
import parameterAPI from './parameter';
import formulaAPI from './formula'; // 关联引用相关接口
export {
	formulaAPI,
	referenceAPI,
	baseAPI,
	userAPI,
	appChannelAPI,
	dataServiceAPI,
	dataServiceListAPI,
	supplierListAPI,
	externalAPI,
	internalAPI,
	billingDetailAPI,
	appServiceListAPI,
	serviceGroupAPI,
	threeCallDetailAPI,
	businessChannelAPI,
	exceptionListAPI,
	pageQueryAPI,
	threeDataMonitorAPI,
	threeDataRemainWarnAPI,
	supplierWarningAPI,
	costAnalysisAPI,
	agingAnalysisAPI,
	etlAPI,
	systemFieldsAPI,
	contractListAPI,
	captainAPI,
	parameterAPI
};
