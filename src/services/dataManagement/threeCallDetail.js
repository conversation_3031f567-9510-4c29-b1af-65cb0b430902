import { getUrl, deleteEmptyObjItem } from '../../utils/common';
import request from '../../utils/request';

// 三方数据调用明细列表
const getList = async(params) => {
	return request(getUrl('/dataManage/thirdService/getInvokeDetailList', deleteEmptyObjItem(params)), {
		method: 'GET'
	});
};

const getDetail = async (params) => {
	return request(getUrl('/dataManage/thirdService/getInvokeDetail', deleteEmptyObjItem(params)), {
		method: 'GET'
	});
};

export default {
	getList,
	getDetail
};
