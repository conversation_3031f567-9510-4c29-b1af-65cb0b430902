import I18N from '@/utils/I18N';
import moment from 'moment';
import { message } from 'tntd';
import { matchPath } from 'dva/router';
import config from '@/common/config';
import { useState, useEffect } from 'react';
import { filter, isEqual, isRegExp, get } from 'lodash';
import { getAppStore } from '@/app';

const { routerPrefix } = config;

export const colorRgb = (str, opacity) => {
    let sColor = str?.toLowerCase();
    if (sColor) {
        if (sColor.length === 4) {
            let sColorNew = '#';
            for (let i = 1; i < 4; i += 1) {
                sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
            }
            sColor = sColorNew;
        }
        // 处理六位的颜色值
        let sColorChange = [];
        for (let i = 1; i < 7; i += 2) {
            sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2), 16));
        }
        return 'rgba(' + sColorChange.join(',') + ',' + opacity + ')';
    }
    return sColor;
};

// 时间戳转年月日时分秒
export function formatStandardTime(time) {
    return time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '--';
}
export const generateUUID = () => {
    let d = new Date().getTime();
    let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        let r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
    });
    return uuid;
};
export const generateUUIDContinuity = () => {
    let d = new Date().getTime();
    let uuid = 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        let r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
    });
    return uuid;
};
// 递归获取机构
export const traverseTree = (treeData, callback, pnode, pnodes = []) => {
    (treeData || []).every((node, index) => {
        let result;
        if (callback) {
            result = callback(node, pnode, pnodes?.length ? pnodes : [pnode].filter((item) => !!item), index);
        }
        // 回调函数返回false则终止遍历
        if (result !== false) {
            node && traverseTree(node.children || [], callback, node, [node, ...pnodes]);
        }

        return result !== false;
    });
    return treeData;
};

export const isAppListVisible = (pathname = window.location.pathname) =>
    ![
        '/formula',
        '/formula/detail',
        '/supplierManagement/etl',
        '/supplierManagement/supplierList',
        '/accountManagement/contractBillingDetail',
        '/supplierManagement/supplierList/contractList', // 合同管理页面
        '/supplierManagement/dataServiceList/addModify', // 新增/修改/查看数据源接口页面
        '/supplierManagement/dataServiceList/detail', // 数据源详情页面
        '/supplierManagement/dataServiceList/mockConfig', // mock配置页面
        '/accountManagement/external',
        '/supplierManagement/dataServiceList',
        '/workflow/arrange',
        '/workflow/sub',
        '/interface/management',
        '/Interface/interfaceAnalysis',
        '/apiAnalysis/report',
        '/dataManagement/threeCallDetail'
    ].some((item) => pathname.includes(item) === true);

// 设置机构/应用可见 最高优先级
export const isOrgListVisible = (pathname = window.location.pathname) =>
    [
        // freyr-react应用

        // '/appServiceCenter/serviceGroup', // 调用方服务组列表
        // '/appServiceCenter/appServiceList', // 调用方服务管理
        // '/dataManagement/businessChannel', // 调用方调用明细
        '/dashboard/appChannel', // 调用方大盘
        '/accountManagement/internal', // 按应用计费
        '/batchCall/taskList', // 批量调用任务管理
        // '/accountManagement/internal', // 调用方计费
        '/formula',
        '/workflow/arrange',
        '/workflow/sub',
        '/interface/management'
    ].some((item) => {
        return matchPath(pathname, {
            path: `${routerPrefix}${item}`,
            exact: true
        });
    });

// 定义机构下应用不可见
export const isOrgAppListUnVisible = (pathname = window.location.pathname) => {
    const unVisibleList = [
        '/supplierManagement/supplierList',
        '/accountManagement/contractBillingDetail',
        '/supplierManagement/etl',
        '/supplierManagement/supplierList/contractList'
    ];
    // 如果是本地开发，开启过滤条件
    if (window.lightBoxActions) {
        unVisibleList.push('/supplierManagement/dataServiceList');
    }
    return unVisibleList.some((item) => {
        return matchPath(pathname, {
            path: `${routerPrefix}${item}`,
            exact: true
        });
    });
};

export function getUrlKey(name) {
    return (
        decodeURIComponent((new RegExp(`[?|&]${name}=([^&;]+?)(&|#|;|$)`).exec(location.href) || ['', ''])[1].replace(/\+/g, '%20')) || null
    );
}

// 时分秒为00：00：00的时间戳
export function formatStartTime(time) {
    return moment(`${moment(time).format('YYYY-MM-DD')} 00:00:00`).valueOf();
}

// 时分秒为23：59：59的时间戳
export function formatEndTime(time) {
    return moment(`${moment(time).format('YYYY-MM-DD')} 23:59:59`).valueOf();
}

// 时分秒为00：00：00
export function formatST(time) {
    return moment(`${moment(time).format('YYYY-MM-DD')} 00:00:00`).format('YYYY-MM-DD HH:mm:ss');
}

export const useGetGlobalStore = () => {
    let { global } = getAppStore().getState();
    const [globalStore, setGlobalStore] = useState(global);
    useEffect(() => {
        if (!isEqual(globalStore, global)) {
            setGlobalStore(global);
        }
    }, [global]);
    return globalStore;
};

export function flatten(arr) {
    var res = [];
    for (let i = 0, length = arr.length; i < length; i++) {
        if (Array.isArray(arr[i])) {
            res = res.concat(flatten(arr[i]));
        } else {
            res.push(arr[i]);
        }
    }
    return res;
}

// 时分秒为23：59：59
export function formatET(time) {
    return moment(`${moment(time).format('YYYY-MM-DD')} 23:59:59`).format('YYYY-MM-DD HH:mm:ss');
}

export function fixedZero(val) {
    return Number(val) < 10 ? `0${val}` : val;
}

export function getTimeDistance(type) {
    const now = new Date();
    const oneDay = 1000 * 60 * 60 * 24;

    if (type === 'today') {
        now.setHours(0);
        now.setMinutes(0);
        now.setSeconds(0);
        return [moment(now), moment(now.getTime() + (oneDay - 1000))];
    }

    if (type === 'week') {
        let day = now.getDay();
        now.setHours(0);
        now.setMinutes(0);
        now.setSeconds(0);

        if (day === 0) {
            day = 6;
        } else {
            day -= 1;
        }

        const beginTime = now.getTime() - day * oneDay;

        return [moment(beginTime), moment(beginTime + (7 * oneDay - 1000))];
    }

    if (type === 'month') {
        const year = now.getFullYear();
        const month = now.getMonth();
        const nextDate = moment(now).add(1, 'months');
        const nextYear = nextDate.year();
        const nextMonth = nextDate.month();

        return [
            moment(`${year}-${fixedZero(month + 1)}-01 00:00:00`),
            moment(moment(`${nextYear}-${fixedZero(nextMonth + 1)}-01 00:00:00`).valueOf() - 1000)
        ];
    }

    if (type === 'year') {
        const year = now.getFullYear();

        return [moment(`${year}-01-01 00:00:00`), moment(`${year}-12-31 23:59:59`)];
    }
}

export function getPlainNode(nodeList, parentPath = '') {
    const arr = [];
    nodeList.forEach((node) => {
        const item = node;
        item.path = `${parentPath}/${item.path || ''}`.replace(/\/+/g, '/');
        item.exact = true;
        if (item.children && !item.component) {
            arr.push(...getPlainNode(item.children, item.path));
        } else {
            if (item.children && item.component) {
                item.exact = false;
            }
            arr.push(item);
        }
    });
    return arr;
}

export function digitUppercase(n) {
    const fraction = [I18N.utils.jiao, I18N.utils.fen];
    const digit = [
        I18N.utils.ling,
        I18N.utils.yi3,
        I18N.utils.er2,
        I18N.utils.san2,
        I18N.utils.si2,
        I18N.utils.wu2,
        I18N.utils.lu,
        I18N.utils.qi2,
        I18N.utils.ba2,
        I18N.utils.jiu2
    ];
    const unit = [
        [I18N.utils.yuan, I18N.utils.wan, I18N.utils.yi2],
        ['', I18N.utils.shi, I18N.utils.bai, I18N.utils.qian]
    ];
    let num = Math.abs(n);
    let s = '';
    fraction.forEach((item, index) => {
        s += (digit[Math.floor(num * 10 * 10 ** index) % 10] + item).replace(/零./, '');
    });
    s = s || I18N.utils.zheng;
    num = Math.floor(num);
    for (let i = 0; i < unit[0].length && num > 0; i += 1) {
        let p = '';
        for (let j = 0; j < unit[1].length && num > 0; j += 1) {
            p = digit[num % 10] + unit[1][j] + p;
            num = Math.floor(num / 10);
        }
        s = p.replace(/(零.)*零$/, '').replace(/^$/, I18N.utils.ling) + unit[0][i] + s;
    }

    return s
        .replace(/(零.)*零元/, I18N.utils.yuan)
        .replace(/(零.)+/g, I18N.utils.ling)
        .replace(/^整$/, I18N.utils.lingYuanZheng);
}

export function changeTime(date) {
    let date1 = new Date(date);
    let Y = date1.getFullYear() + '-';
    let M = (date1.getMonth() + 1 < 10 ? '0' + (date1.getMonth() + 1) : date1.getMonth() + 1) + '-';
    let D = (date1.getDate() < 10 ? '0' + date1.getDate() : date1.getDate()) + ' ';
    let h = date1.getHours() + ':';
    let m = date1.getMinutes() + ':';
    let s = date1.getSeconds();

    return Y + M + D + h + m + s;
}

// 弹窗未完全关闭禁止再次提交
export function messageError(payload) {
    return new Promise((resolve) => {
        message.error(payload, () => {
            resolve(false);
        });
    });
}

export function getPermissionConfigInfo(menuTree, menuCode, functionCode) {
    let params = {};
    menuTree.map((item) => {
        item.children &&
            item.children.map((subItem) => {
                if (subItem.code === menuCode) {
                    params['X-Menu-Uuid'] = subItem['menuUuid'];
                }
                subItem['functionList'] &&
                    subItem['functionList'].map((fItem) => {
                        if (fItem.code === functionCode) {
                            params['X-Function-Uuid'] = fItem['funcUuid'];
                        }
                    });
            });
    });
    return params;
}

// JS判断字符串长度（英文占1个字符，中文汉字占2个字符）
export function getStrLen(str) {
    let len = 0;
    for (let i = 0; i < str.length; i++) {
        let c = str.charCodeAt(i);
        // 单字节加1
        if ((c >= 0x0001 && c <= 0x007e) || (c >= 0xff60 && c <= 0xff9f)) {
            len++;
        } else {
            len += 2;
        }
    }
    return len;
}

/*
  格式化金额，
  s : 金额
  n : 保留位数
*/
export function formatMoney(s, n) {
    n = n > 0 && n <= 20 ? n : 2;
    s = String(parseFloat(String(s).replace(/[^\d\.-]/g, '')).toFixed(n));
    const l = s.split('.')[0].split('').reverse();
    const r = s.split('.')[1];
    let t = '';
    for (let i = 0; i < l.length; i++) {
        t += l[i] + ((i + 1) % 3 === 0 && i + 1 !== l.length ? ',' : '');
    }
    return t.split('').reverse().join('') + '.' + r;
}

/*
  格式化数字
*/
export function formatCount(num) {
    let str = `${num}`;
    const re = /(?=(?!(\b))(\d{3})+$)/g;
    str = str.replace(re, ',');
    return str;
}

// Tab页面添加Search定位
export function searchToObject(search) {
    let pairs = search.substring(1).split('&');
    let obj = {};
    let pair;
    let i;
    for (i in pairs) {
        if (pairs[i] === '') {
            continue;
        }
        pair = pairs[i].split('=');
        obj[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
    }
    return obj;
}

/**
 * @desc   自动转换字节单位
 * @param {Number} bytes
 * @return {String}
 */
export function bytesToSize(bytes) {
    if (bytes === 0) return '0 B';
    var k = 1024;
    var sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    var i = Math.floor(Math.log(bytes) / Math.log(k));

    return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
}

// 逗号分割
export function formatNumComma(num) {
    return String(num).replace(/\B(?=(\d{3})+\b)/g, ',');
}
// 简单数字转中文
export function toChinese(num) {
    var nums = [
        I18N.utils.ling,
        I18N.utils.yi,
        I18N.utils.er,
        I18N.utils.san,
        I18N.utils.si,
        I18N.utils.wu,
        I18N.utils.liu,
        I18N.utils.qi,
        I18N.utils.ba,
        I18N.utils.jiu
    ];
    return nums[num];
}
//时分秒转cron表达式
export function dateChangeCron(h = 0, m = 0, s = 0) {
    var cron = '';
    cron = s + ' ' + m + ' ' + h + ' * * ? *';
    return cron;
    //  console.log（cron）      19 30 16 * * 2,3 *
}

export function numAdd(num1, num2) {
    let baseNum, baseNum1, baseNum2;
    try {
        baseNum1 = num1.toString().split('.')[1].length;
    } catch (e) {
        baseNum1 = 0;
    }
    try {
        baseNum2 = num2.toString().split('.')[1].length;
    } catch (e) {
        baseNum2 = 0;
    }
    baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
    return (num1 * baseNum + num2 * baseNum) / baseNum;
}

export const $ = (dom) => {
    const funcs = {
        addClass: (text) => {
            if (dom) {
                const oldClass = filter((dom.className || '').split(' '), (i) => !!i);
                oldClass.push(text);
                dom.className = oldClass.join(' ');
            }
            return funcs;
        },
        removeClass: (text) => {
            if (dom) {
                const oldClass = filter((dom.className || '').split(' '), (i) => !!i && !(isRegExp(text) ? text.test(i) : i === text));
                dom.className = oldClass.join(' ');
            }
            return funcs;
        }
    };
    return funcs;
};
// 生成随机数，首位是字母

export function makeRandomCode(num = 10) {
    const numArr = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
    const charArr = [
        'a',
        'b',
        'c',
        'd',
        'e',
        'f',
        'g',
        'h',
        'i',
        'j',
        'k',
        'l',
        'm',
        'n',
        'o',
        'p',
        'q',
        'r',
        's',
        't',
        'u',
        'v',
        'w',
        'x',
        'y',
        'z'
    ];
    const arr = [...numArr, ...charArr];
    let code = charArr[Math.floor(Math.random() * charArr.length)];
    for (let i = 0; i < num - 1; i++) {
        code += `${arr[Math.floor(Math.random() * arr.length)]}`;
    }
    return code;
}
