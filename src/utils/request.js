import I18N from '@/utils/I18N';
import fetch from 'dva/fetch';
import { message } from 'tntd';
import { routerRedux } from 'dva/router';
import queryString from 'query-string';
import { getHeader, getUrl } from '@/utils/common';
import { getAppStore } from '../app';
import Config from '../common/config';
import { getPermissionConfigInfo } from './utils';
import { searchToObject } from '@/utils/utils';

const codeMessage = {
    200: I18N.utils.request.fuWuQiChengGong,
    201: I18N.utils.request.xinJianHuoXiuGai,
    202: I18N.utils.request.yiGeQingQiuYi,
    204: I18N.utils.request.shanChuShuJuCheng,
    400: I18N.utils.request.faChuDeQingQiu2,
    401: I18N.utils.request.yongHuZanWuQuan,
    403: I18N.utils.request.yongHuDeDaoShou,
    404: I18N.utils.request.faChuDeQingQiu,
    406: I18N.utils.request.qingQiuDeGeShi,
    410: I18N.utils.request.qingQiuDeZiYuan,
    422: I18N.utils.request.dangChuangJianYiGe,
    500: I18N.utils.request.fuWuQiFaSheng,
    502: I18N.utils.request.wangGuanCuoWu,
    503: I18N.utils.request.fuWuBuKeYong,
    504: I18N.utils.request.wangGuanChaoShi
};

function checkStatus(response) {
    const store = getAppStore();
    const { dispatch } = store;
    if (response.status >= 200 && response.status < 300) {
        return response;
    }
    const errortext = codeMessage[response.status] || response.statusText;
    const error = new Error(errortext);
    error.name = response.status;
    error.response = response;
    throw error;
}

/**
 * Requests a URL, returning a promise.
 *
 * @param  {string} url       The URL we want to request
 * @param  {object} [options] The options we want to pass to "fetch"
 * @return {object}           An object containing either "data" or "err"
 */
export default function request(url, options = {}, noApiPrefix) {
    const { notify = true, originRes = false } = options;
    const defaultOptions = {
        credentials: 'include'
    };
    const newOptions = {
        headers: getHeader(),
        ...defaultOptions,
        ...options,
        noApiPrefix
    };

    if (newOptions.method === 'POST' || newOptions.method === 'PUT' || newOptions.method === 'DELETE') {
        if (!(newOptions.body instanceof FormData)) {
            newOptions.headers = {
                Accept: 'application/json',
                'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
                ...newOptions.headers
            };

            if (options?.headers && options?.headers['Content-Type'] === 'application/json') {
                newOptions.body = JSON.stringify(newOptions.body);
            } else {
                newOptions.body = queryString.stringify(newOptions.body);
            }
            // newOptions.body = JSON.stringify(newOptions.body);
            // newOptions.body = queryString.stringify(newOptions.body);
        } else {
            // newOptions.body is FormData
            newOptions.headers = {
                Accept: 'application/json',
                'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
                ...newOptions.headers
            };
        }
    }

    url = noApiPrefix ? url : Config.apiPrefix + url;

    if (noApiPrefix === '/mockApi') {
        url = noApiPrefix + url;
    }

    if ((!!window.ActiveXObject || 'ActiveXObject' in window) && newOptions.method && newOptions.method.toUpperCase() === 'GET') {
        // ie兼容 阻止get请求缓存
        newOptions.headers = {
            ...newOptions.headers,
            'Cache-Control': 'no-cache',
            Pragma: 'no-cache'
        };
    }

    // if (getAppStore) {
    //     // 获取当前机构
    //     const globalState = getAppStore()?.getState().global || {};
    //     const { currentOrgCode } = globalState;
    //     // 查询类接口传右上角用户选择的机构编码
    //     let queryParams = url.split('?');
    //     if (queryParams && queryParams.length > 1) {
    //         queryParams = searchToObject(`?${queryParams[1]}`);
    //         if (queryParams && !queryParams.hasOwnProperty('orgCode')) {
    //             if (Object.keys(queryParams)?.length) {
    //                 url = `${url}&orgCode=${currentOrgCode}`;
    //             } else {
    //                 if (!url.includes('?')) {
    //                     url += '?';
    //                 }
    //                 url = `${url}orgCode=${currentOrgCode}`;
    //             }
    //         }
    //     }
    // }

    return fetch(url, newOptions)
        .then(checkStatus)
        .then((response) => {
            if (originRes) {
                return response;
            } else if (response) {
                return response.json();
            }
        })
        .catch((e) => {
            const { dispatch } = getAppStore && getAppStore();
            const status = e.name;
            if (status === 401) {
                const { response } = e;
                try {
                    response.json().then(function (data) {
                        const { code } = data || {};

                        if (String(code) === '4011003') {
                            dispatch({
                                type: 'global/setAttrValue',
                                payload: {
                                    multiUserModal: true
                                }
                            });
                        } else {
                            dispatch({
                                type: 'login/goLogin'
                            });
                        }
                    });
                } catch (e) {
                    dispatch({
                        type: 'login/goLogin'
                    });
                }
                dispatch({
                    type: 'login/goLogin'
                });
                return;
            }
            if (status === 403) {
                if (process.env.SYS_ENV === 'development') {
                    dispatch(routerRedux.push(`${Config.routerPrefix}/exception/403`));
                    return;
                }
                dispatch(routerRedux.push(`${Config.routerPrefix}/exception/403`));
                return;
            }
            if (status <= 504 && status >= 500) {
                if (process.env.SYS_ENV === 'development') {
                    return;
                }
                dispatch(routerRedux.push(`${Config.routerPrefix}/exception/500`));
                return;
            }
            if (status === 404) {
                if (process.env.SYS_ENV !== 'development') {
                    dispatch(routerRedux.push(`${Config.routerPrefix}/exception/404`));
                }
            }
        });
}

// const checkResponse = (response) => {
//     if (!response) {
//         return {
//             success: false,
//             data: {}
//         };
//     }
//     if (!response.success) {
//         const messages = response.message || response.returnMsg || response.message || response.msg || '未知错误' + response.code;
//         message.error(messages.slice(0, 200));
//     }

//     return response;
// };

// 针对a标签下载
export function downloadReport(url, options, fileName, fileType, noApiPrefix) {
    url = noApiPrefix ? url : Config.apiPrefix + url;
    let downFileName = `${fileName}.${fileType || 'pdf'}`;

    const newOptions = {
        headers: getHeader(),
        ...options
    };

    return fetch(url, newOptions)
        .then((res) => {
            const disposition = res.headers.get('Content-Disposition');
            if (disposition && disposition.split('=') && !(fileName && fileType)) {
                downFileName = disposition.split('=')[1].replace(/\"/g, '');
                if (downFileName) {
                    downFileName = decodeURIComponent(downFileName);
                }
            }
            return res.blob();
        })
        .then((blob) => {
            const url = URL.createObjectURL(blob);
            let a = document.createElement('a');
            a.download = `${downFileName}`;
            a.href = url;
            document.body.appendChild(a);
            a.click();
            a.remove();
        })
        .catch((err) => {
            console.error(err);
        });
}

/**
 * @description: 下载文件请求
 * @param {*} url
 * @param {*} options
 * @param {*} reportName
 * @param {*} fileType
 * @param {*} errorMsg 失败提示文案
 */
export function downloadFileHandle(url, options, reportName, fileType, errorMsg, noApiPrefix) {
    url = noApiPrefix ? url : Config.apiPrefix + url;
    options.headers = getHeader();
    return fetch(url, Object.assign(options, { credentials: 'include' }))
        .then((res) => {
            if (res.status !== 200) {
                throw new Error(res.statusText);
            }
            return res.blob();
        })
        .then((blob) => {
            const url = URL.createObjectURL(blob);
            let a = document.createElement('a');
            a.download = `${reportName}.${fileType || 'pdf'}`;
            a.href = url;
            document.body.appendChild(a);
            a.click();
            a.remove(); // document.body.removeChild(a)
        })
        .catch((err) => {
            errorMsg && message.error(errorMsg);
            console.error(err);
        });
}

export function downloadPostFileHandle(url, options, reportName, fileType) {
    url = Config.apiPrefix + url;
    options = {
        ...options,
        credentials: 'include',
        headers: getHeader()
    };
    options.headers = {
        Accept: 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
        ...options.headers
    };
    options.body = queryString.stringify(options.body);
    // options.body = queryString.stringify(options.body);

    return fetch(url, options)
        .then((res) => res.blob())
        .then((blob) => {
            const url = URL.createObjectURL(blob);
            let a = document.createElement('a');
            a.download = `${reportName}.${fileType || 'pdf'}`;
            a.href = url;
            document.body.appendChild(a);
            a.click();
            a.remove(); // document.body.removeChild(a)
        })
        .catch((err) => {
            console.error(err);
        });
}
/**
 * @description: 下载5个进件文件请求
 * @param {*} url
 * @param {*} options
 * @param {*} reportName
 */
export function downloadAllFile(url, options) {
    return fetch(url, Object.assign(options, { credentials: 'include' }))
        .then((res) => res.blob())
        .then((blob) => {
            const url = URL.createObjectURL(blob);
            let a = document.createElement('a');
            a.download = 'report.zip';
            a.href = url;
            document.body.appendChild(a);
            a.click();
            a.remove(); // document.body.removeChild(a)
        })
        .catch((err) => {
            console.error(err);
        });
}

export function PostForm(url, type, datas, fileList, fileKey, noApiPrefix) {
    url = noApiPrefix ? url : Config.apiPrefix + url;
    const formData = new FormData();
    Object.keys(datas).forEach((key) => {
        formData.append(key, datas[key]);
    });
    fileList &&
        fileList.length > 0 &&
        fileList.forEach((item) => {
            formData.append(fileKey, item);
        });
    return fetch(url, {
        method: type,
        credentials: 'include',
        body: formData,
        headers: getHeader()
    })
        .then((res) => {
            return res.json();
        })
        .then((res) => {
            return res;
        });
}
