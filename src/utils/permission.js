import {getAppStore} from '../app';

export function checkFunctionHasPermission(menuCode, functionCode) {
	return window.auth(menuCode, functionCode);

	// const store = getAppStore();
	// let globalStore = store.getState().global;
	// let { customTree } = globalStore;
	// let functionList = [];
	// let hasPermission = false;
	// if (customTree["menuTree"] && customTree["menuTree"].length) {
	// 	customTree["menuTree"].map((item, index) => {
	// 		item["children"] && item["children"].map((subItem, subIndex) => {
	// 			if (subItem.code && subItem.code === menuCode) {
	// 				functionList = subItem.functionList ? subItem.functionList : [];
	// 				if (functionList.find(fItem => fItem.code === functionCode)) {
	// 					hasPermission = true;
	// 				}
	// 			}
	// 		});
	// 	});
	// }
	// return hasPermission;
	// // return true;
}
