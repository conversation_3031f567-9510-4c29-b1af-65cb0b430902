export const isJSON = (str) => {
    if (typeof str === 'string') {
        try {
            let obj = JSON.parse(str);
            if (typeof obj === 'object' && obj) {
                return true;
            }
            return false;
        } catch (e) {
            console.log('error：' + str + '!!!' + e);
            return false;
        }
    } else {
        console.log('It is not a string!');
    }
};
export const isEvalJSON = (str) => {
    if (typeof str === 'string') {
        try {
            let obj = eval('(' + str + ')');
            if (typeof obj === 'object' && obj) {
                return true;
            }
            return false;
        } catch (e) {
            // console.log('error：' + str + '!!!' + e);
            return false;
        }
    } else {
        // console.log('It is not a string!');
    }
};
