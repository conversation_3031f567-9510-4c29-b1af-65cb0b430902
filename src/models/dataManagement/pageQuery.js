import { message } from 'tntd';
import { pageQueryAPI } from '../../services';
import { cloneDeep } from 'lodash';
import { compare } from '../../utils/operatorState';

export default {
	namespace: 'pageQuery',
	state: {
		loading: false
	},

	effects: {
		*getListSets({ payload }, { call, put }) {
			let response = yield call(pageQueryAPI.getLists, payload);
			if (!response) {
				yield put({
					type: 'setAttrValue',
					payload: {
						loading: false
					}
				});
				return;
			}
			if (!response.success) {
				message.error(response.message);
				yield put({
					type: 'setAttrValue',
					payload: {
						loading: false
					}
				});
				return;
			}
			yield put({
				type: 'setAttrValue',
				payload: response
			});
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === '[object Object]' && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = null;
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		}
	}

};
