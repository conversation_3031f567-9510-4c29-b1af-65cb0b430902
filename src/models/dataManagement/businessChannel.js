import { message } from 'tntd';
import { businessChannelAPI } from '../../services';
import { cloneDeep } from 'lodash';
import { compare } from '../../utils/operatorState';
import { formatStartTime, formatEndTime } from '@/utils/utils';
import moment from 'moment';

export default {
	namespace: 'businessChannel',
	state: {
		tableList: [],
		total: 0,
		searchParams: {
			curPage: 1,
			pageSize: 10,
			date: [moment().valueOf(), moment().valueOf()],
			serviceCode: null, // 三方服务接口名称
			returnResult: null, // 返回结果
			searchResult: null, // 查询结果
			channelCode: null, // 业务系统
			organizeCode: null, // 机构
			sequenceId: null, // 流水号
			searchWhere: null, // 入参系统字段名称
			searchValue: null, // 入参系统字段的值
			errorCode: null, // 错误码
			invokeSource: null, // 数据来源
			cacheFlag: null, // 是否缓存
			channelSequenceId: null // 业务唯一流水号
		},
		loading: false,
		dialogShow: {
			searchDrawer: false
		}
	},

	effects: {
		*getList({ payload }, { call, put, select }) {
			if (payload && !payload.noLoading) {
				yield put({
					type: 'setAttrValue',
					payload: {
						loading: true
					}
				});
			}
			const businessChannel = yield select(state => ({ searchParams: state.businessChannel.searchParams }));
			const { searchParams } = businessChannel;
			let params = Object.assign({}, searchParams);
			const global = yield select(state => ({ currentApp: state.global.currentApp }));
			const { currentApp } = global;
			params.appNames = currentApp.name;
			if (payload) {
				const { curPage, pageSize } = payload;
				if (curPage) params.curPage = curPage;
				if (pageSize) params.pageSize = pageSize;
			}
			if (params.date) {
				params.startTime = formatStartTime(params.date[0]);
				params.endTime = formatEndTime(params.date[1]);
				delete params.date;
			}

			let res = yield call(businessChannelAPI.getList, params);
			yield put({
				type: 'setAttrValue',
				payload: {
					loading: false,
					tableList: []
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			yield put({
				type: 'setAttrValue',
				payload: {
					tableList: res.data.contents,
					total: res.data.total,
					searchParams: {
						curPage: res.data.curPage,
						pageSize: res.data.pageSize
					}
				}
			});
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === '[object Object]' && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = null;
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		}
	}

};
