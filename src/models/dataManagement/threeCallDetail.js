import { message } from 'tntd';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import { formatStartTime, formatEndTime } from '@/utils/utils';
import { threeCallDetailAPI } from '../../services';
import { compare } from '../../utils/operatorState';

export default {
    namespace: 'threeCallDetail',
    state: {
        tableList: [],
        total: 0,
        searchParams: {
            curPage: 1,
            pageSize: 10,
            date: [moment().valueOf(), moment().valueOf()],
            serviceCode: null, // 三方服务接口名称
            returnResult: null, // 返回结果
            partnerId: null, // 合作方id
            contractCode: null, // 合同编号
            searchResult: null, // 查询结果
            errorCode: null, // 错误码
            sequenceId: null, // 调用唯一标识码
            searchWhere: null, // 入参字段
            searchValue: null, // 入参值
            serviceType: null, // 数据类型
            invokeSource: null, // 调用数据来源
            invokeSequenceId: null //新增字段唯一标识码
        },
        loading: false,
        dialogShow: {
            searchDrawer: false
        }
    },

    effects: {
        *getList({ payload }, { call, put, select }) {
            if (payload && !payload.noLoading) {
                yield put({
                    type: 'setAttrValue',
                    payload: {
                        loading: true
                    }
                });
            }
            const threeCallDetail = yield select((state) => ({ searchParams: state.threeCallDetail.searchParams }));
            const global = yield select((state) => ({ currentApp: state.global.currentApp }));
            const { searchParams } = threeCallDetail;
            const { currentApp } = global;
            let params = Object.assign({}, searchParams);
            params.appNames = currentApp.name;
            if (payload) {
                const { curPage, pageSize } = payload;
                if (curPage) params.curPage = curPage;
                if (pageSize) params.pageSize = pageSize;
            }
            if (params.date) {
                params.startTime = formatStartTime(params.date[0]);
                params.endTime = formatEndTime(params.date[1]);
                delete params.date;
            }
            let res = yield call(threeCallDetailAPI.getList, params);
            yield put({
                type: 'setAttrValue',
                payload: {
                    loading: false,
                    tableList: []
                }
            });
            if (!res) return;
            if (!res.success) return message.error(res.msg);
            if (!res.data) return;
            yield put({
                type: 'setAttrValue',
                payload: {
                    tableList: res.data.contents,
                    total: res.data.total,
                    searchParams: {
                        curPage: res.data.curPage,
                        pageSize: res.data.pageSize
                    }
                }
            });
        }
    },

    reducers: {
        setAttrValue(state, { payload }) {
            return (function multiple(state, newState) {
                let stateChange = state;
                // 在不按照state模板的情况下,payload添加属性和属性值的情况下使用
                stateChange = compare(stateChange, newState);
                for (let [key, value] of Object.entries(stateChange)) {
                    // 这里严格判断value是否是对象{},不能使用typeof,原因自己查
                    if (
                        Object.prototype.toString.call(value) === '[object Object]' &&
                        newState[key] !== undefined &&
                        newState[key] !== null
                    ) {
                        stateChange[key] = multiple(value, newState[key]);
                    } else {
                        if (newState[key] !== undefined && newState[key] !== null) {
                            stateChange[key] = newState[key];
                        }
                        if (newState[key] === null) {
                            stateChange[key] = null;
                        }
                    }
                }
                return stateChange;
            })(cloneDeep(state), payload);
        }
    }
};
