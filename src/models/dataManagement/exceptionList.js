import { message } from 'tntd';
import { exceptionListAPI } from '../../services';
import { cloneDeep } from 'lodash';
import { compare } from '../../utils/operatorState';
import { formatStartTime, formatEndTime } from '@/utils/utils';

export default {
	namespace: 'exceptionList',
	state: {
		tableList: [],
		total: 0,
		searchParams: {
			curPage: 1,
			pageSize: 10,
			date: null,
			channelCode: null // 业务系统
		},
		loading: false
	},

	effects: {
		*getList({ payload }, { call, put, select }) {
			if (payload && !payload.noLoading) {
				yield put({
					type: 'setAttrValue',
					payload: {
						loading: true
					}
				});
			}
			const exceptionList = yield select(state => ({ searchParams: state.exceptionList.searchParams }));
			const { searchParams } = exceptionList;
			let params = Object.assign({}, searchParams);
			if (payload) {
				const { curPage, pageSize } = payload;
				if (curPage) params.curPage = curPage;
				if (pageSize) params.pageSize = pageSize;
			}
			if (params.date) {
				params.startTime = formatStartTime(params.date[0]);
				params.endTime = formatEndTime(params.date[1]);
				delete params.date;
			}

			let res = yield call(exceptionListAPI.getList, params);
			yield put({
				type: 'setAttrValue',
				payload: {
					loading: false
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			yield put({
				type: 'setAttrValue',
				payload: {
					tableList: res.data.contents,
					total: res.data.total,
					searchParams: {
						curPage: res.data.curPage,
						pageSize: res.data.pageSize
					}
				}
			});
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === '[object Object]' && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = null;
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		}
	}

};
