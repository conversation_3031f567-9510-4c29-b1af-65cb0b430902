import { message } from 'tntd';
import moment from 'moment';
import { dataServiceAPI } from '../../services';
import { cloneDeep } from 'lodash';
import { compare } from '../../utils/operatorState';
import { formatStartTime, formatEndTime } from '@/utils/utils';
import { dataServiceLang } from '@/constants/lang';

export default {
    namespace: 'dataService',
    state: {
        searchParams: {
            name: null,
            // 数据类型及明细
            typeDate: [moment().subtract(6, 'days').valueOf(), moment().valueOf()], // 时间 - 默认一周
            // 三方数据调用明细
            serviceType: null, // 数据类型
            serviceCode: null, // 三方服务接口名称
            date: [moment().subtract(6, 'days').valueOf(), moment().valueOf()], // 时间 - 默认一周
            // 三方数据调用排行
            top: null,
            serviceType2: null, // 三方数据类型
            rangeDate: [moment().subtract(6, 'days').valueOf(), moment().valueOf()] // 时间 - 默认一周
        },
        dataTypeloading: false, // 数据类型loading
        dataTypeChart: null, // 数据类型
        threeDataDetailLoading: false, // 三方数据明细loading
        threeDataDetail: [], // 三方数据明细
        threeDataLoading: false, // 三方数据调用loading
        threeDataChart: null, // 三方数据调用量
        threeDataRangeLoading: false, // 三方数据调用排行loading
        threeDataRangeChart: null // 三方数据调用排行
    },

    effects: {
        // 获取数据类型
        *getStatisticResult({}, { call, put, select }) {
            yield put({
                type: 'setAttrValue',
                payload: {
                    dataTypeloading: true,
                    threeDataDetailLoading: true,
                    threeDataDetail: []
                }
            });
            const dataService = yield select((state) => ({ searchParams: state.dataService.searchParams }));
            const { typeDate } = dataService.searchParams;
            let params = {};
            if (typeDate) {
                params.startDate = formatStartTime(typeDate[0]);
                params.endDate = formatEndTime(typeDate[1]);
            }
            let res = yield call(dataServiceAPI.getStatisticResult, params);
            yield put({
                type: 'setAttrValue',
                payload: {
                    dataTypeloading: false,
                    threeDataDetailLoading: false
                }
            });
            if (!res) return;
            if (!res.success) return message.error(res.msg);
            if (!res.data) return;
            let seriesData = [];
            res.data.serviceTypeList &&
                res.data.serviceTypeList.forEach((item) => {
                    seriesData.push({
                        value: item.serviceCount,
                        name: item.serviceTypeName,
                        serviceType: item.serviceType
                    });
                });
            let dataTypeChart = {
                seriesData
            };
            yield put({
                type: 'setAttrValue',
                payload: {
                    dataTypeChart: res.data.serviceTypeList && res.data.serviceTypeList.length > 0 ? dataTypeChart : null,
                    threeDataDetail: res.data.serviceDetailList ? res.data.serviceDetailList : []
                }
            });
        },

        // 获取三方数据明细
        *getStatisticBill({ payload }, { call, put, select }) {
            yield put({
                type: 'setAttrValue',
                payload: {
                    threeDataDetailLoading: true,
                    threeDataDetail: []
                }
            });
            const dataService = yield select((state) => ({ searchParams: state.dataService.searchParams }));
            const { typeDate } = dataService.searchParams;
            let params = {
                serviceType: payload.serviceType
            };
            if (typeDate) {
                params.startDate = formatStartTime(typeDate[0]);
                params.endDate = formatEndTime(typeDate[1]);
            }
            let res = yield call(dataServiceAPI.getStatisticBill, params);
            yield put({
                type: 'setAttrValue',
                payload: {
                    threeDataDetailLoading: false
                }
            });
            if (!res) return;
            if (!res.success) return message.error(res.msg);
            if (!res.data) return;
            yield put({
                type: 'setAttrValue',
                payload: {
                    threeDataDetail: res.data ? res.data : []
                }
            });
        },

        // 获取三方数据大盘调用结果
        *getInvokeResult({}, { call, put, select }) {
            yield put({
                type: 'setAttrValue',
                payload: {
                    threeDataLoading: true
                }
            });
            const dataService = yield select((state) => ({ searchParams: state.dataService.searchParams }));
            const { serviceType, serviceCode, date } = dataService.searchParams;
            let params = {
                serviceType,
                serviceCode
            };
            if (date) {
                params.startDate = formatStartTime(date[0]);
                params.endDate = formatEndTime(date[1]);
            }

            let res = yield call(dataServiceAPI.getInvokeResult, params);
            yield put({
                type: 'setAttrValue',
                payload: {
                    threeDataLoading: false
                }
            });
            if (!res) return;
            if (!res.success) return message.error(res.msg);
            if (!res.data) return;
            let legendData = [
                { name: dataServiceLang.common('amountSuccess'), value: [] }, // 成功量
                { name: dataServiceLang.common('amountFailure'), value: [] }, // 失败量
                { name: dataServiceLang.common('libraryLimit'), value: [] }, // 库无量
                { name: dataServiceLang.common('successRate'), value: [] }, // 成功率
                { name: dataServiceLang.common('failRate'), value: [] }, // 失败率
                { name: dataServiceLang.common('libraryRate'), value: [] } // 库无率
            ];
            let seriesData = [];
            let xAxisData = [];
            res.data.forEach((item) => {
                xAxisData.push(item.statisticDate);
                legendData[0].value.push(parseInt(item.successCount, 10));
                legendData[1].value.push(parseInt(item.failCount, 10));
                legendData[2].value.push(parseInt(item.nonSearchCount, 10));
                legendData[3].value.push(parseFloat(item.successRate));
                legendData[4].value.push(parseFloat(item.failRate));
                legendData[5].value.push(parseFloat(item.nonSearchRate));
            });
            legendData.forEach((item) => {
                let flag = item.name.includes(dataServiceLang.common('rate')); // 率
                if (flag) {
                    seriesData.push({
                        name: item.name,
                        type: 'line',
                        smooth: true,
                        data: item.value,
                        yAxisIndex: 1
                    });
                } else {
                    seriesData.push({
                        name: item.name,
                        type: 'line',
                        smooth: true,
                        data: item.value
                    });
                }
            });
            let threeDataChart = {
                legendData: [
                    dataServiceLang.common('amountSuccess'),
                    dataServiceLang.common('amountFailure'),
                    dataServiceLang.common('libraryLimit'),
                    dataServiceLang.common('successRate'),
                    dataServiceLang.common('failRate'),
                    dataServiceLang.common('libraryRate')
                ], // "成功量", "失败量", "库无量", "成功率", "失败率", "库无率"
                seriesData,
                xAxisData
            };
            yield put({
                type: 'setAttrValue',
                payload: {
                    threeDataChart: res.data && res.data.length > 0 ? threeDataChart : null
                }
            });
        },

        // 获取三方数据大盘服务调用排行
        *getInvokeRank({}, { call, put, select }) {
            yield put({
                type: 'setAttrValue',
                payload: {
                    threeDataRangeLoading: true // 三方数据调用排行loading
                }
            });
            const dataService = yield select((state) => ({ searchParams: state.dataService.searchParams }));
            const { rangeDate, serviceType2, top } = dataService.searchParams;
            let params = {
                serviceType: serviceType2,
                top
            };
            if (rangeDate && rangeDate.length === 2) {
                params.startDate = formatStartTime(rangeDate[0]);
                params.endDate = formatEndTime(rangeDate[1]);
            }
            if (params.top === dataServiceLang.common('all')) params.top = null; // 全部

            let res = yield call(dataServiceAPI.getInvokeRank, params);
            yield put({
                type: 'setAttrValue',
                payload: {
                    threeDataRangeLoading: false
                }
            });
            if (!res) return;
            if (!res.success) return message.error(res.msg);
            if (!res.data) return;
            let yAxisData = [];
            let yAxisData2 = [];
            let seriesData = [];
            res.data.invokeRanks &&
                res.data.invokeRanks.forEach((item) => {
                    yAxisData.push(`${item.serviceName} - ${item.serviceTypeName}`);
                    yAxisData2.push(`${item.serviceName} - ${item.serviceTypeName}`);
                    seriesData.push(item.invokeCount);
                });
            let threeDataRangeChart = {
                chartData: {
                    yAxisData,
                    seriesData
                },
                yAxisData: yAxisData2.reverse()
            };
            yield put({
                type: 'setAttrValue',
                payload: {
                    threeDataRangeChart: res.data.invokeRanks && res.data.invokeRanks.length > 0 ? threeDataRangeChart : null
                }
            });
        }
    },

    reducers: {
        setAttrValue(state, { payload }) {
            return (function multiple(state, newState) {
                let stateChange = state;
                // 在不按照state模板的情况下,payload添加属性和属性值的情况下使用
                stateChange = compare(stateChange, newState);
                for (let [key, value] of Object.entries(stateChange)) {
                    // 这里严格判断value是否是对象{},不能使用typeof,原因自己查
                    if (
                        Object.prototype.toString.call(value) === '[object Object]' &&
                        newState[key] !== undefined &&
                        newState[key] !== null
                    ) {
                        stateChange[key] = multiple(value, newState[key]);
                    } else {
                        if (newState[key] !== undefined && newState[key] !== null) {
                            stateChange[key] = newState[key];
                        }
                        if (newState[key] === null) {
                            stateChange[key] = null;
                        }
                    }
                }
                return stateChange;
            })(cloneDeep(state), payload);
        }
    }
};
