import { message } from 'tntd';
import { internalAPI } from '@/services';
import { cloneDeep } from 'lodash';
import { compare } from '../../utils/operatorState';
import { formatStartTime, formatEndTime } from '@/utils/utils';
import moment from 'moment';

const threeMonth = [
	moment().subtract(1, 'months').valueOf(),
	moment().valueOf()
];

const defaultState = {
	dialogShow: {
		lookModal: false, // 业务系统计费 - 查看弹框
		lookModal2: false // 机构计费 - 查看弹框
	},
	// 业务系统计费
	loading: false,
	modalLoading: false,
	tableList: [],
	total: 0,
	searchParams: {
		curPage: 1,
		pageSize: 10,
		channelCode: null, // 业务系统
		serviceCode: null, // 三方服务接口code
		billMethod: null, // 均摊计费方式
		date: threeMonth, // 时间
		contractId: null, // 合同id
		contractVersion: null // 合同版本
	},
	modalTableList: [],
	modalTotal: 0,
	modalSearchParams: {
		curPage: 1,
		pageSize: 10,
		businessType: 1, // 1=业务系统，2=机构
		businessCode: null, // 业务系统code
		serviceCode: null, // 三方服务code
		billMethod: null, // 均摊计费方式
		date: null // 时间
	},
	// 机构计费
	loading2: false,
	modalLoading2: false,
	tableList2: [],
	total2: 0,
	searchParams2: {
		curPage: 1,
		pageSize: 10,
		organizeCode: null, // 机构
		serviceCode: null, // 三方服务接口code
		billMethod: null, // 均摊计费方式
		date: threeMonth, // 时间
		contractId: null, // 合同id
		contractVersion: null // 合同版本
	},
	modalTableList2: [],
	modalTotal2: 0,
	modalSearchParams2: {
		curPage: 1,
		pageSize: 10,
		businessType: 2, // 1=业务系统，2=机构
		businessCode: null, // 机构code
		serviceCode: null, // 三方服务code
		billMethod: null, // 均摊计费方式
		date: null // 时间
	}
};

export default {
	namespace: 'internal',
	state: defaultState,

	effects: {
		// 业务系统计费
		*getList({ payload }, { call, put, select }) {
			yield put({
				type: 'setAttrValue',
				payload: {
					loading: true
				}
			});
			const internal = yield select(state => ({ searchParams: state.internal.searchParams }));
			const global = yield select(state => ({ currentApp: state.global.currentApp }));
			const { searchParams } = internal;
			const { currentApp } = global;
			let params = Object.assign({}, searchParams);
			params.appNames = currentApp.name;
			if (payload) {
				const { curPage, pageSize } = payload;
				if (curPage) params.curPage = curPage;
				if (pageSize) params.pageSize = pageSize;
			}
			if (params.date) {
				params.startTime = formatStartTime(params.date[0]);
				params.endTime = formatEndTime(params.date[1]);
				delete params.date;
			}
			let res = yield call(internalAPI.getChannelList, params);
			yield put({
				type: 'setAttrValue',
				payload: {
					loading: false
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			res.data.contents &&
				res.data.contents.forEach((item, index) => {
					item.index = index;
				});
			yield put({
				type: 'setAttrValue',
				payload: {
					tableList: res.data.contents,
					total: res.data.total,
					searchParams: {
						curPage: res.data.curPage,
						pageSize: res.data.pageSize
					}
				}
			});
		},
		*getModalList({ payload }, { call, put, select }) {
			yield put({
				type: 'setAttrValue',
				payload: {
					modalLoading: true
				}
			});
			const internal = yield select(state => ({ modalSearchParams: state.internal.modalSearchParams }));
			const { modalSearchParams } = internal;
			let params = Object.assign({}, modalSearchParams);
			if (params.date) {
				params.startTime = formatStartTime(params.date[0]);
				params.endTime = formatEndTime(params.date[1]);
				delete params.date;
			}
			if (payload) {
				const { curPage, pageSize } = payload;
				if (curPage) params.curPage = curPage;
				if (pageSize) params.pageSize = pageSize;
			}
			let res = yield call(internalAPI.getBusinessBillList, params);
			yield put({
				type: 'setAttrValue',
				payload: {
					modalLoading: false
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			res.data.contents &&
				res.data.contents.forEach((item, index) => {
					item.index = index;
				});
			yield put({
				type: 'setAttrValue',
				payload: {
					modalTableList: res.data.contents,
					modalTotal: res.data.total,
					modalSearchParams: {
						curPage: res.data.curPage,
						pageSize: res.data.pageSize
					}
				}
			});
		},
		// 机构计费
		*getList2({ payload }, { call, put, select }) {
			yield put({
				type: 'setAttrValue',
				payload: {
					loading2: true
				}
			});
			const internal = yield select(state => ({ searchParams2: state.internal.searchParams2 }));
			const global = yield select(state => ({ currentApp: state.global.currentApp }));
			const { searchParams2 } = internal;
			const { currentApp } = global;
			let params = Object.assign({}, searchParams2);
			params.appNames = currentApp.name;
			if (payload) {
				const { curPage, pageSize } = payload;
				if (curPage) params.curPage = curPage;
				if (pageSize) params.pageSize = pageSize;
			}
			if (params.date) {
				params.startTime = formatStartTime(params.date[0]);
				params.endTime = formatEndTime(params.date[1]);
				delete params.date;
			}
			let res = yield call(internalAPI.getOrganizeList, params);
			yield put({
				type: 'setAttrValue',
				payload: {
					loading2: false
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			res.data.contents &&
				res.data.contents.forEach((item, index) => {
					item.index = index;
				});
			yield put({
				type: 'setAttrValue',
				payload: {
					tableList2: res.data.contents,
					total2: res.data.total,
					searchParams2: {
						curPage: res.data.curPage,
						pageSize: res.data.pageSize
					}
				}
			});
		},
		*getModalList2({ payload }, { call, put, select }) {
			yield put({
				type: 'setAttrValue',
				payload: {
					modalLoading2: true
				}
			});
			const internal = yield select(state => ({ modalSearchParams2: state.internal.modalSearchParams2 }));
			const { modalSearchParams2 } = internal;
			let params = Object.assign({}, modalSearchParams2);
			if (params.date) {
				params.startTime = formatStartTime(params.date[0]);
				params.endTime = formatEndTime(params.date[1]);
				delete params.date;
			}
			if (payload) {
				const { curPage, pageSize } = payload;
				if (curPage) params.curPage = curPage;
				if (pageSize) params.pageSize = pageSize;
			}
			let res = yield call(internalAPI.getBusinessBillList, params);
			yield put({
				type: 'setAttrValue',
				payload: {
					modalLoading2: false
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			res.data.contents &&
				res.data.contents.forEach((item, index) => {
					item.index = index;
				});
			yield put({
				type: 'setAttrValue',
				payload: {
					modalTableList2: res.data.contents,
					modalTotal2: res.data.total,
					modalSearchParams2: {
						curPage: res.data.curPage,
						pageSize: res.data.pageSize
					}
				}
			});
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === '[object Object]' && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = null;
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		},
		reset() {
			return cloneDeep(defaultState);
		}
	}

};
