import { message } from 'tntd';
import { contractListAPI } from '@/services';
import { cloneDeep } from 'lodash';
import { compare } from '@/utils/operatorState';

export default {
	namespace: 'contractList',
	state: {
		tableList: [],
		total: 0,
		searchParams: {
			curPage: 1,
			pageSize: 10,
			name: null, // 合同名称
			code: null, // 编号
			status: null, // 状态
			providerUuid: null
		},
		dialogShow: {
			addEditModal: false // 新增、编辑、查看modal
		},
		dialogData: {
			addEditModalData: {
				code: null, // 合同编号
				version: null, // 合同版本
				name: null, // 合同名称
				startTime: null, // 合同开始日期
				endTime: null, // 合同结束日期
				chargeType: null, // 计费方式
				docFileList: [], // 接口文档
				attachFileList: [], // 合同附件
				price: null, // 价格
				masterDimensions: [], // 主属性
				followDimensions: [], // 从属性
				luaScript: '', // lua脚本
				countConfig: [], // 按次字段匹配计费
				flowRange: [], // 流量区间
				fieldPricesFlowRange: [], // 阶梯字段匹配计费-流量
				dateRange: [] // 日期区间
			}
		},
		loading: false,
		modalType: 1, // 弹框类型 1新增，2修改，3查看
		updateId: null // 点击修改传过去id
	},

	effects: {
		*getList({ payload }, { call, put, select }) {
			yield put({
				type: 'setAttrValue',
				payload: {
					loading: true
				}
			});
			const contractList = yield select(state => ({ searchParams: state.contractList.searchParams }));
			const { searchParams } = contractList;
			let params = Object.assign({}, searchParams);
			if (payload) {
				const { curPage, pageSize } = payload;
				if (curPage) params.curPage = curPage;
				if (pageSize) params.pageSize = pageSize;
			}

			let res = yield call(contractListAPI.getList, params);
			yield put({
				type: 'setAttrValue',
				payload: {
					loading: false
				}
			});
			if (!res) return;
			if (!res.success) return message.error(res.msg);
			if (!res.data) return;
			yield put({
				type: 'setAttrValue',
				payload: {
					tableList: res.data.contents,
					total: res.data.total,
					searchParams: {
						curPage: res.data.curPage,
						pageSize: res.data.pageSize
					}
				}
			});
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === '[object Object]' && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = null;
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		}
	}
};
