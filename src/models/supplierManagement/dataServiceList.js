import { message } from 'tntd';
import { isString, trim, cloneDeep } from 'lodash';
import { dataServiceListAPI } from '../../services';
import { compare } from '../../utils/operatorState';

const trimSpace = (params) => {
    if (params) {
        Object.keys(params).forEach((key) => {
            const value = params[key];
            if (isString(value)) {
                params[key] = trim(value);
            }
        });
    }
    return params;
};

const defaultState = {
    tableList: [],
    total: 0,
    searchParams: {
        curPage: 1,
        pageSize: 9, // 只影响数据服务列表页面
        providerName: null, // 供应商名称
        displayName: null, // 三方服务接口名称
        dataType: null, // 数据类型
        dataSourceType: null // 接口类型
    },
    dialogShow: {
        addEditModal: false // 新增、编辑、查看modal
    },
    dialogData: {
        addEditModalData: {
            isShowErrMsg: {},
            serviceInputIsNull: true, //入参高亮
            serviceOutputIsNull: true, //出参高亮
            uuid: null,
            partnerId: null, // 供应商名称
            dataType: null, // 数据类型
            displayName: null, // 三方服务接口名称
            name: null, // 三方数据标识
            confidence: null, // 置信度
            costLevel: null, // 成本等级
            contractCode: null, // 合同
            chargeMethod: null, // 计费类型
            appName: null, // 应用
            cacheday: null, // 数据缓存期（天）
            retry: null, // 数据重试次数
            timeout: null, // 数据超时时间（ms）
            cacheOpen: 0, // 查询方式（old） 1缓存/0实时查询
            invokePolicy: 0, // 查询方式
            limitConfig: [{ type: null, limit: null }], // 流量上限控制
            methodType: null, // 接口类型（协议）
            url: null, // url地址
            contentType: null, // 调用方式（Method）
            postEtlHandlerName: null, // 后置ETL处理器
            preEtlHandlerName: null, // 前置ETL处理器
            documentTypeUuid: null, // 报文id
            indexPackageName: [], // 特征集
            inputTemplate: null, // 输入处理器模板
            outputTemplate: null, // 输出处理器模板
            inputConfig: [], // 服务入参
            outputConfig: [], // 服务出参
            proxy: '0' // 是否使用代理
        },
        addEditModalDataAsync: {
            asyncPollTimeInterval: null, // 轮询时间间隔
            asyncPollMaxCount: null, // 最大轮询次数
            asyncMethodType: null, // async 接口类型（协议）
            asyncUrl: null, // async url地址
            asyncContentType: null, // async 调用方式（Method）
            asyncControl: 'hidden', // async 输入处理器模板显示/隐藏控制
            asyncProxy: '0', // async 是否使用代理
            asyncProxyInfo: null, // async 代理信息
            asyncPreEtlHandlerName: null, // async 前置ETL处理器
            asyncPostEtlHandlerName: null, // async 后置ETL处理器
            asyncInputTemplate: null, // async 输入处理器模板
            asyncOutputTemplate: null, // async 输出处理器模板
            asyncInputConfig: [], // async 服务入参
            asyncOutputConfig: [] // async 服务出参
        }
    },
    loading: false,
    modalType: 1, // 弹框类型 1新增，2修改，3查看
    updateId: null, // 点击修改传过去id,
    structureList: [],
    isStructureVisible: true
};

export default {
    namespace: 'dataServiceList',
    state: cloneDeep(defaultState),

    effects: {
        *getList({ payload }, { call, put, select }) {
            if (payload && !payload.noLoading) {
                yield put({
                    type: 'setAttrValue',
                    payload: {
                        loading: true
                    }
                });
            }
            const supplierList = yield select((state) => ({ searchParams: state.dataServiceList.searchParams }));
            const { searchParams } = supplierList;
            let params = Object.assign({}, searchParams);
            if (payload) {
                const { curPage, pageSize, sortField, sortRule } = payload;
                if (curPage) params.curPage = curPage;
                if (pageSize) params.pageSize = pageSize;

                if (sortField) {
                    params.sortField = sortField;
                } else {
                    delete params.sortField;
                }

                if (sortRule) {
                    params.sortRule = sortRule;
                } else {
                    delete params.sortRule;
                }
            }
            const global = yield select((state) => ({ currentApp: state.global.currentApp }));
            const { currentApp } = global;
            params.appNames = currentApp.name;

            let res = yield call(dataServiceListAPI.getList, params);
            yield put({
                type: 'setAttrValue',
                payload: {
                    loading: false
                }
            });
            if (!res) return;
            if (!res.success) return message.error(res.msg);
            if (!res.data) return;

            let resDatalandAble = yield call(dataServiceListAPI.getDatalandAble, {});
            yield put({
                type: 'setAttrValue',
                payload: {
                    isStructureVisible: resDatalandAble?.data
                }
            });

            if (res?.success && res?.data?.contents && resDatalandAble?.data) {
                const uuids = res.data.contents?.map((item) => item?.uuid);
                let typesRes = yield call(dataServiceListAPI.getDocumentType, { dataSourcesUuid: uuids });
                yield put({
                    type: 'setAttrValue',
                    payload: {
                        structureList: typesRes?.data || []
                    }
                });
            }
            yield put({
                type: 'setAttrValue',
                payload: {
                    tableList: res.data.contents,
                    total: res.data.total,
                    searchParams: {
                        curPage: res.data.curPage,
                        pageSize: res.data.pageSize,
                        sortField: payload && payload.sortField ? payload.sortField : null,
                        sortRule: payload && payload.sortRule ? payload.sortRule : null
                    }
                }
            });
        }
        // *getDatalandAble({ payload }, { call, put, select }) {
        //     let res = yield call(dataServiceListAPI.getDatalandAble, {});
        //     yield put({
        //         type: 'setAttrValue',
        //         payload: {
        //             isStructureVisible: res?.data
        //         }
        //     });
        // }
    },

    reducers: {
        setAttrValue(state, { payload }) {
            return (function multiple(state, newState) {
                let stateChange = state;
                // 在不按照state模板的情况下,payload添加属性和属性值的情况下使用
                stateChange = compare(stateChange, newState);
                for (let [key, value] of Object.entries(stateChange)) {
                    // 这里严格判断value是否是对象{},不能使用typeof,原因自己查
                    if (
                        Object.prototype.toString.call(value) === '[object Object]' &&
                        newState[key] !== undefined &&
                        newState[key] !== null
                    ) {
                        stateChange[key] = multiple(value, newState[key]);
                    } else {
                        if (newState[key] !== undefined && newState[key] !== null) {
                            stateChange[key] = newState[key];
                        }
                        if (newState[key] === null) {
                            stateChange[key] = null;
                        }
                    }
                }
                return stateChange;
            })(cloneDeep(state), payload);
        },
        updateSearchParams(state, { payload }) {
            return {
                ...state,
                searchParams: trimSpace(payload)
            };
        },
        reset() {
            return cloneDeep(defaultState);
        }
    }
};
