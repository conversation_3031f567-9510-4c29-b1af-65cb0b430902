import I18N from '@/utils/I18N';
import dynamic from 'dva/dynamic';

// dynamic包装 函数
const dynamicWrapper = (app, models, component) =>
    dynamic({
        app,
        models: () => models.map((m) => import(`../models/${m}.js`)),
        component
    });
// 抽象化菜单配置
const getNavList = (app) => {
    let navList = {};
    navList.dashboardChildren = [
        // {
        // 	name: "业务渠道大盘",
        // 	enName: "Application channel",
        // 	icon: "list",
        // 	path: "appChannel",
        // 	component: dynamicWrapper(app, [], () => import("../routes/Dashboard/AppChannel"))
        // },
        // {
        // 	name: "数据服务大盘",
        // 	enName: "Data Service",
        // 	icon: "list",
        // 	path: "dataService",
        // 	component: dynamicWrapper(app, [], () => import("../routes/Dashboard/DataService"))
        // },
        {
            name: I18N.common.nav.diaoYongFangDaPan,
            enName: 'Consumer Data',
            icon: 'list',
            path: 'appChannel',
            component: dynamicWrapper(app, [], () => import('../routes/Dashboard/ConsumerData'))
        },
        {
            name: I18N.common.nav.shuJuYuanDaPan,
            enName: 'Provider Data',
            icon: 'list',
            path: 'dataService',
            component: dynamicWrapper(app, [], () => import('../routes/Dashboard/ProviderData'))
        },
        {
            name: I18N.common.nav.shuJuGaiLan,
            enName: 'Overview Knowledge',
            icon: 'list',
            path: 'overviewKnowledge',
            component: dynamicWrapper(app, [], () => import('../routes/Dashboard/OverviewKnowledge'))
        }
    ];
    navList.supplierManagementChildren = [
        {
            name: I18N.common.nav.xiTongZiDuanGuan,
            enName: 'System Fields',
            icon: 'list',
            path: 'systemFields',
            component: dynamicWrapper(app, [], () => import('../routes/SupplierManagement/SystemFields'))
        },
        {
            name: I18N.common.nav.heZuoFangGuanLi,
            enName: 'Supplier List',
            icon: 'list',
            path: 'supplierList',
            component: dynamicWrapper(app, [], () => import('../routes/SupplierManagement/SupplierList'))
        },
        {
            name: I18N.common.nav.heTongGuanLi,
            enName: 'Contract List',
            icon: 'list',
            path: 'supplierList/contractList',
            notRender: true,
            component: dynamicWrapper(app, [], () => import('../routes/SupplierManagement/ContractList'))
        },
        {
            name: I18N.common.nav.shuJuYuanFuWu,
            enName: 'DataService List',
            icon: 'list',
            path: 'dataServiceList',
            component: dynamicWrapper(app, [], () => import('../routes/SupplierManagement/DataServiceList'))
        },
        {
            name: I18N.common.nav.sanFangFuWuJie,
            enName: 'DataService List',
            icon: 'list',
            path: 'dataServiceList/addModify',
            notRender: true,
            component: dynamicWrapper(app, [], () => import('../routes/SupplierManagement/DataServiceList/Inner/AddModify'))
        },
        {
            name: '三方服务接口管理-详情',
            enName: 'DataService Detail',
            icon: 'list',
            path: 'dataServiceList/detail',
            notRender: true,
            component: dynamicWrapper(app, [], () => import('../routes/SupplierManagement/DataServiceList/Inner/Detail'))
        },
        {
            name: I18N.common.nav.mOCKPei,
            enName: 'Mock Config',
            icon: 'list',
            path: 'dataServiceList/mockConfig',
            notRender: true,
            component: dynamicWrapper(app, [], () => import('../routes/SupplierManagement/DataServiceList/Inner/Detail/MockConfig'))
        },
        {
            name: I18N.common.nav.eTLChuLi,
            enName: 'ETL Management',
            icon: 'list',
            path: 'etl',
            component: dynamicWrapper(app, [], () => import('../routes/SupplierManagement/Etl/Tabs'))
        }
    ];
    navList.accountManagementChildren = [
        {
            name: I18N.common.nav.diaoYongFangJiFei,
            enName: 'Internal',
            icon: 'list',
            path: 'internal',
            component: dynamicWrapper(app, [], () => import('../routes/AccountManagement/Internal'))
        },
        {
            name: I18N.common.nav.shuJuYuanJiFei,
            enName: 'External',
            icon: 'list',
            path: 'external',
            component: dynamicWrapper(app, [], () => import('../routes/AccountManagement/External'))
        },
        {
            name: I18N.common.nav.heTongJiFei,
            enName: 'ContractBillingDetail',
            icon: 'list',
            path: 'contractBillingDetail',
            component: dynamicWrapper(app, [], () => import('../routes/AccountManagement/ContractBillingDetail'))
        }
    ];
    navList.appServiceCenterChildren = [
        {
            name: I18N.common.nav.diaoYongFangFuWu3,
            enName: 'AppServiceList',
            icon: 'list',
            path: 'appServiceList',
            component: dynamicWrapper(app, [], () => import('../routes/AppServiceCenter/AppServiceList'))
        },
        {
            name: I18N.common.nav.diaoYongFangFuWu2,
            enName: 'AppServiceList',
            icon: 'list',
            path: 'serviceGroup',
            component: dynamicWrapper(app, [], () => import('../routes/AppServiceCenter/ServiceGroup'))
        },
        {
            name: I18N.common.nav.diaoYongFangFuWu,
            enName: 'AppServiceList',
            icon: 'list',
            path: 'serviceGroup/detail',
            notRender: true,
            component: dynamicWrapper(app, [], () => import('../routes/AppServiceCenter/ServiceGroup/AddModify'))
        }
    ];
    navList.dataManagementChildren = [
        {
            name: I18N.common.nav.shuJuYuanDiaoYong,
            enName: 'ThreeCallDetail',
            icon: 'list',
            path: 'threeCallDetail',
            component: dynamicWrapper(app, [], () => import('../routes/DataManagement/ThreeCallDetail'))
        },
        {
            name: I18N.common.nav.diaoYongFangDiaoYong,
            enName: 'BusinessChannel',
            icon: 'list',
            path: 'businessChannel',
            component: dynamicWrapper(app, [], () => import('../routes/DataManagement/BusinessChannel'))
        },
        // {
        // 	name: "异常列表",
        // 	enName: "ExceptionList",
        // 	icon: "list",
        // 	path: "exceptionList",
        // 	component: dynamicWrapper(app, [], () => import("../routes/DataManagement/ExceptionList"))
        // },
        {
            name: I18N.common.nav.shouGongChaXun,
            enName: 'PageQuery',
            icon: 'list',
            path: 'pageQuery',
            component: dynamicWrapper(app, [], () => import('../routes/DataManagement/PageQuery'))
        }
    ];
    navList.monitorWarningChildren = [
        {
            name: I18N.common.nav.shuJuYuanYiChang,
            enName: 'ThreeDataMonitor',
            icon: 'list',
            path: 'threeDataMonitor',
            component: dynamicWrapper(app, [], () => import('../routes/MonitorWarning/ThreeDataMonitor'))
        },
        {
            name: I18N.common.nav.shuJuYuanZongLiu,
            enName: 'ThreeDataRemainWarn',
            icon: 'list',
            path: 'threeDataRemainWarn',
            component: dynamicWrapper(app, [], () => import('../routes/MonitorWarning/ThreeDataRemainWarn'))
        },
        {
            name: I18N.common.nav.shuJuYuanQiXian,
            enName: 'SupplierWarning',
            icon: 'list',
            path: 'supplierWarning',
            component: dynamicWrapper(app, [], () => import('../routes/MonitorWarning/SupplierWarning'))
        }
    ];
    navList.qualityAnalysisChildren = [
        {
            name: I18N.common.nav.chengBenFenXiDui,
            enName: 'CostAnalysis',
            icon: 'list',
            path: 'costAnalysis',
            component: dynamicWrapper(app, [], () => import('../routes/QualityAnalysis/CostAnalysis'))
        },
        {
            name: I18N.common.nav.shiXiaoFenXiDui,
            enName: 'AgingAnalysis',
            icon: 'list',
            path: 'agingAnalysis',
            component: dynamicWrapper(app, [], () => import('../routes/QualityAnalysis/AgingAnalysis'))
        }
    ];
    return navList;
};
// nav data
export const getNavData = (app) => [
    {
        component: dynamicWrapper(app, [], () => import('../layouts/BasicLayout/')),
        layout: 'BasicLayout',
        name: I18N.common.nav.shouYe,
        path: '/',
        children: [
            {
                name: I18N.common.nav.shuJuDaPan,
                enName: 'Market data',
                icon: 'desktop',
                path: 'dashboard',
                children: getNavList(app).dashboardChildren
            },
            {
                name: I18N.common.nav.shuJuYuanGuanLi,
                enName: 'Supplier Management',
                icon: 'desktop',
                path: 'supplierManagement',
                children: getNavList(app).supplierManagementChildren
            },
            {
                name: I18N.common.nav.jiFeiGuanLi,
                enName: 'Account Management',
                icon: 'desktop',
                path: 'accountManagement',
                children: getNavList(app).accountManagementChildren
            },
            {
                name: I18N.common.nav.diaoYongFangGuanLi,
                enName: 'AppServiceCenter',
                icon: 'desktop',
                path: 'appServiceCenter',
                children: getNavList(app).appServiceCenterChildren
            },
            {
                name: I18N.common.nav.diaoYongChaXun,
                enName: 'DataManagement',
                icon: 'desktop',
                path: 'dataManagement',
                children: getNavList(app).dataManagementChildren
            },
            {
                name: I18N.common.nav.jianKongYuJing,
                enName: 'MonitorWarning',
                icon: 'desktop',
                path: 'monitorWarning',
                children: getNavList(app).monitorWarningChildren
            },
            {
                name: I18N.common.nav.zhiLiangFenXi,
                enName: 'QualityAnalysis',
                icon: 'desktop',
                path: 'qualityAnalysis',
                children: getNavList(app).qualityAnalysisChildren
            },
            {
                name: 'other',
                enName: 'Others',
                icon: 'setting',
                path: 'exception',
                notRender: true,
                children: getNavList(app).exceptionChildren
            }
        ]
    },
    {
        component: dynamicWrapper(app, [], () => import('../layouts/PublishLayout/')),
        layout: 'PublishLayout',
        name: I18N.common.nav.shouYe,
        path: '/',
        children: [
            {
                name: I18N.common.nav.shuJuDaPan,
                enName: 'Market data',
                icon: 'desktop',
                path: '/handle/dashboard',
                children: getNavList(app).dashboardChildren
            },
            {
                name: I18N.common.nav.shuJuYuanGuanLi,
                enName: 'Supplier Management',
                icon: 'desktop',
                path: '/handle/supplierManagement',
                children: getNavList(app).supplierManagementChildren
            },
            {
                name: I18N.common.nav.jiFeiGuanLi,
                enName: 'Account Management',
                icon: 'desktop',
                path: '/handle/accountManagement',
                children: getNavList(app).accountManagementChildren
            },
            {
                name: I18N.common.nav.diaoYongFangGuanLi,
                enName: 'AppServiceCenter',
                icon: 'desktop',
                path: '/handle/appServiceCenter',
                children: getNavList(app).appServiceCenterChildren
            },
            {
                name: I18N.common.nav.diaoYongChaXun,
                enName: 'DataManagement',
                icon: 'desktop',
                path: '/handle/dataManagement',
                children: getNavList(app).dataManagementChildren
            },
            {
                name: I18N.common.nav.jianKongYuJing,
                enName: 'MonitorWarning',
                icon: 'desktop',
                path: '/handle/monitorWarning',
                children: getNavList(app).monitorWarningChildren
            },
            {
                name: I18N.common.nav.zhiLiangFenXi,
                enName: 'QualityAnalysis',
                icon: 'desktop',
                path: '/handle/qualityAnalysis',
                children: getNavList(app).qualityAnalysisChildren
            },
            {
                name: 'other',
                enName: 'Others',
                icon: 'setting',
                path: '/handle/exception',
                notRender: true,
                children: getNavList(app).exceptionChildren
            }
        ]
    }
];
