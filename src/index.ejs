<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>天座</title>
    <script src="/<%= htmlWebpackPlugin.options.pathPrefix %>/vendor/polyfill.min.js"></script>
    <script>
        (function () {
            // 该事件是核心
            window.addEventListener("storage", function (event) {
                if (event.key == "getSessionStorage") {
                    // 已存在的标签页会收到这个事件
                    localStorage.setItem(
                        "_up_qjt_csrf_",
                        JSON.stringify(sessionStorage)
                    );
                    localStorage.removeItem("_up_qjt_csrf_");
                } else if (
                    event.key == "_up_qjt_csrf_" &&
                    !sessionStorage.length
                ) {
                    // 新开启的标签页会收到这个事件
                    var data = JSON.parse(event.newValue),
                        value;
                    for (key in data) {
                        sessionStorage.setItem(key, data[key]);
                    }
                }
            });
            if (!sessionStorage.length) {
                // 这个调用能触发目标事件，从而达到共享数据的目的
                localStorage.setItem("getSessionStorage", Date.now());
            }
        })();
    </script>
</head>

<body>
    <div id="root"></div>
    <script
        src="/<%= htmlWebpackPlugin.options.pathPrefix %>/vendor/<%= htmlWebpackPlugin.options.dllName %>.js"></script>
</body>

</html>
