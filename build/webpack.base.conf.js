const path = require('path');
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const autoprefixer = require('autoprefixer');
const config = require('./config');
const { dllPath, dllName } = require('@tntd/dll');
const TransformPathResolvePlugin = require('@tntd/webpack-transform-path-resolve-plugin');

const { staticPath, staticSourcePath, version } = config;
const replaceComponents = ['icon', 'checkbox'];

function resolve(dir) {
    return path.join(__dirname, '..', dir);
}

module.exports = {
    cache: true,
    context: path.resolve(__dirname, '../'),
    entry: {
        app: './src/app.js'
    },
    output: {
        path: config.build.assetsRoot,
        filename: staticPath + '/js/[name].[chunkhash:8].js',
        publicPath: '/'
    },
    resolve: {
        extensions: ['.js', '.json'],
        alias: {
            '@': resolve('src'),
            '~locale': resolve('src/constants/locale'),
            tntd: `tntd-${version}`,
            '~modules': resolve('modules/business/Components'),
            '~I18N': resolve('modules/business/I18N')
        },
        fallback: {
            path: require.resolve('path-browserify')
        },
        plugins: [
            new TransformPathResolvePlugin([
                ...replaceComponents.map((name) => ({
                    // antd里面icon使用tntd里的
                    matchPath(sourcePath, modulePath) {
                        return (
                            sourcePath.includes(path.join('node_modules', 'antd')) &&
                            modulePath.endsWith(`../${name}`) &&
                            modulePath !== path.join('tntd', 'es', name)
                        );
                    },
                    transform(modulePath) {
                        return path.join('tntd', 'es', name);
                    }
                })),
                {
                    matchPath(sourcePath, modulePath) {
                        return sourcePath.includes('node_modules') && modulePath.includes('antd') && modulePath.includes('style');
                    },
                    transform(filePath) {
                        if (filePath) {
                            return filePath.replace('antd', 'tntd');
                        }
                    }
                }
            ])
        ]
    },
    plugins: [
        new webpack.DllReferencePlugin({
            context: __dirname,
            manifest: `${dllPath}/vendor/${dllName}_manifest.json`
        }),
        new webpack.ProvidePlugin({
            React: 'react'
        }),
        new webpack.HotModuleReplacementPlugin()
    ],
    module: (devMode) => ({
        rules: [
            {
                test: /\.js$/,
                exclude: /(node_modules)/,
                use: {
                    loader: 'babel-loader?cacheDirectory=true'
                }
            },
            {
                test: /\.css$/,
                use: [devMode ? 'style-loader' : MiniCssExtractPlugin.loader, 'css-loader']
            },
            {
                test: /\.less$/,
                use: [
                    devMode ? 'style-loader' : MiniCssExtractPlugin.loader,
                    {
                        loader: 'css-loader',
                        options: {
                            modules: 'global'
                        }
                    },
                    {
                        loader: 'postcss-loader',
                        options: {
                            postcssOptions: {
                                plugins: [autoprefixer()]
                            }
                        }
                    },
                    {
                        loader: 'less-loader',
                        options: {
                            lessOptions: {
                                javascriptEnabled: true,
                                modules: true,
                                localIndexName: '[name]__[local]___[chunkhash:base64:5]',
                                modifyVars: {
                                    hack: 'true; @import "~tntd/themes/default/variables.less";'
                                }
                            }
                        }
                    }
                ]
            },
            {
                test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
                type: 'asset', // 方法2 asset/inline
                parser: {
                    dataUrlCondition: {
                        maxSize: 10000
                    }
                },
                generator: {
                    filename: path.posix.join(staticSourcePath, 'img/[name].[hash:7][ext]')
                }
            },
            {
                test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
                type: 'asset', // 方法2 asset/inline
                parser: {
                    dataUrlCondition: {
                        maxSize: 10000
                    }
                },
                generator: {
                    filename: path.posix.join(staticSourcePath, 'media/[name].[hash:7][ext]')
                }
            },
            {
                test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
                type: 'asset', // 方法2 asset/inline
                parser: {
                    dataUrlCondition: {
                        maxSize: 10000
                    }
                },
                generator: {
                    filename: path.posix.join(staticSourcePath, 'fonts/[name].[hash:7][ext]')
                }
            },
            {
                test: /\.properties$/,
                loader: resolve('build/plugins/props-loader')
            },
            {
                test: /\.(txt|jst)$/,
                loader: 'raw-loader'
            }
        ]
    })
};
