const path = require('path');
const PORT = 8000;
const MOCK = false; // 控制这里的是否开启mock
const actionMap = {
    'build:v2': 'v2',
    'build:v3': 'v3',
    start: 'v3',
    mock: 'v3'
};
const version = actionMap[process.env.npm_lifecycle_event] || 'v3';
const sourcePrefix = 'tianzuo-resource/' + version;
const cacheData = require('@tntd/webpack-branch-plugin').cacheData;
// const target = 'https://zhice.tcloud.tongdun.cn/';
const target = 'http://10.59.217.7:8088/';

module.exports = {
    projectCode: 'tianzuo',
    staticPath: sourcePrefix,
    staticSourcePath: sourcePrefix + '/static',
    version,
    dev: {
        hot: true,
        mock: MOCK,
        port: PORT,
        proxyTable: cacheData(
            {
                '/bridgeApi': {
                    target,
                    changeOrigin: true,
                    pathRewrite: {
                        // '^/bridgeApi': ''
                    }
                },
                '/handleApi': {
                    target,
                    changeOrigin: true,
                    pathRewrite: {
                        // "^/handleApi": "/api"
                    }
                },
                '/noahAgentApi': {
                    target,
                    changeOrigin: true,
                    pathRewrite: {}
                },
                '/captainApi': {
                    target,
                    changeOrigin: true,
                    pathRewrite: {
                        // "^/captainApi": "/api"
                    }
                },
                '/mockApi': {
                    target: 'https://sinan.tongdun.me/mock/23176/api', // Mock
                    changeOrigin: true,
                    pathRewrite: {
                        // '^/mockApi': ''
                    }
                }
            },
            {
                distPath: path.resolve(__dirname, '../mock'), // 缓存目录
                open: false // 是否开启接口缓存(注意：开启后请把mock关掉)
            }
        ),
        autoOpenBrowser: true,
        devtool: 'eval-source-map',
        // 环境变量在这里控制，可查看package.json中原来脚本中的环境变量定义进行指定
        env: {
            MOCK,
            PORT,
            POC: false,
            SYS_ENV: 'development',
            BABEL_ENV: 'development',
            PATH: '/' + sourcePrefix,
            version
        }
    },
    build: {
        assetsRoot: path.resolve(__dirname, '../dist'),
        devtool: 'source-map',
        env: {
            PORT,
            POC: false,
            SYS_ENV: 'production',
            BABEL_ENV: 'production',
            PATH: '/' + sourcePrefix,
            version
        }
    }
};
