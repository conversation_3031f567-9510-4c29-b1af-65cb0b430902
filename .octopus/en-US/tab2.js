export default {
  index: {
    shuRuXuanZeZhuang: 'Input selection status',
    gongZuoLiuBiaoZhi: 'Workflow ID',
    gongZuoLiuMingCheng: 'Workflow name',
    shuRuSouSuoNei: 'Input search content',
    daoRu: 'Import',
    pi<PERSON><PERSON>g<PERSON><PERSON><PERSON>ian: 'Batch Online',
    xin<PERSON><PERSON>: 'Add',
    yinYongGuanXi: 'Reference relation',
    banBenLiShi: 'Version history',
    fuZhi: 'Copy',
    shan<PERSON>hu: 'Delete',
    ma: ']?',
    queRenShan<PERSON>hu: 'Confirm deletion of [',
    shang<PERSON>ian: 'Go online',
    xiuGai: 'Modify',
    cha<PERSON><PERSON>: 'View',
    bian<PERSON>ai: 'Configure',
    cao<PERSON>uo: 'Action',
    xiuGaiShiJian: 'Modification time',
    xiuGaiRen: 'Modifier',
    chuangJianShiJian: 'Creation time',
    chuangJianRen: 'Creator',
    miao<PERSON>hu: 'Description',
    shan<PERSON>huShiBai: 'Failure to delete',
    shan<PERSON>hu<PERSON>hengGong: 'Deleted successfully',
    cunZaiQiangYinYong:
      'Strong references (referenced by online, enabled, etc. related state components) exist, prohibiting operation',
    shou<PERSON>uan: 'Authorization',
    suoShuJiGou: 'Affiliated Institution',
  },
};
