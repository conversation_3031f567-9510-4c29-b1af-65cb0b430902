export default {
  listmodal: {
    gongMODA: 'Totally {val1} Record(s)',
    daoChu: 'Export',
    meiRiJiFeiMing: 'Daily billing details',
    chaKan: 'View',
    caoZuo: 'Action',
    danRiGuSuan: 'Single-day estimation',
    danRiCanYuJi: 'Single-day participating billing traffic',
    diaoYongChengGongDe:
      'Number of traffic successfully called, participating in statistics and alerts of traffic upper limit',
    danRiChengGongLiu: 'Single day successful traffic',
    danRiChanShengZong: 'Total traffic generated per day',
    jiFeiFangShi: 'Billing method',
    riQi: 'Date',
    banBenHao: 'Version number',
    heZuoFangMingCheng: 'Partner name',
    heTongMingCheng: 'Contract name',
    jieTiJiFeiDan:
      '3. Ladder Billing: Estimated Total Price = Unit Price in Ladder Range * Participating in Billing Flow',
    yueBaoNianBaoJi:
      '2. Monthly package, annual package and quarterly package: total estimated price=average daily price * days, monthly package is calculated by 30 days, quarterly package is calculated by 90 days, and annual package is calculated by 365 days',
    anCiJiFeiDan:
      '1. Billing by Time: Estimated Total Price = Unit Price * Participating in Billing Flow',
    anZhaoJiFeiFang:
      'Estimate according to the billing method. estimate method:',
    heTongJiFeiMing: 'Contract billing detail report',
    shuJuYuanFuWu: 'Data source service interface name',
    shuJuYuanJiFei: 'Data source billing details report',
  },
  tab1: {
    gongTOTA: 'Totally {val1} Record(s)',
    junTanJiFeiFang:
      'The equalization billing method is the estimation method given by the system, which can only ensure the balance of internal and external income and expenditure on the statistical unit. you need to select an appropriate estimation method based on the scena',
    anRiJunTan: 'Divide it equally daily',
    qingXuanZeJunTan: 'Average daily share',
    qingXuanZeShuJu: 'Select interface',
    xiTongYiCiZui: 'The system can query for up to one year at a time',
    daoChu: 'Export',
    yueBaoNianBaoJi:
      '3. Monthly package, annual package and quarterly package will calculate the average daily cost',
    jiFeiLeiXingWei:
      '2. When the billing type is query billing, the amount of participating billing call=total flow - failed amount; When the billing type is queried, the amount of participating billing call=total flow - failure amount - success (the data source is not querie',
    anRiJiSuanJun:
      "1. The average cost (T+1) is calculated on a daily basis. The calculation formula is the total cost generated by multiplying the proportion of the single day's participating billing calls by the single day's system calls to the data source service interfa",
    chaKan: 'View',
    wuQuanXianCaoZuo: 'No permission operation',
    caoZuo: 'Action',
    anZhaoDiaoYongLiang:
      'According to the proportion of the call volume, the caller/caller group of other shared contracts will share the cost of system calls',
    junTanFeiYongGu: 'Estimated total price of shared expenses',
    junTanFeiYongGu2:
      'Estimated total price of shared expenses/participating billing traffic',
    junTanPingJunDan: 'Split the average cost evenly',
    guSuanZongJia: 'Estimated total price',
    canYuJiFeiLiu: 'Participating billing traffic',
    chanShengZongLiuLiang: 'Total generated traffic',
    jiFeiFangShi: 'Billing method',
    heTongBanBen: 'Contract version',
    heTongBianHao: 'Contract number',
    shuJuYuanFuWu: 'Data source service interface name',
    quDao: 'Channel',
    jieTiJiFeiGu:
      '3. Ladder Billing: Estimated Total Price = Unit Price in Ladder Range * Participating in Billing Flow',
    yueBaoNianBaoJi2:
      '2. Monthly package, annual package and quarterly package: total estimated price=average daily price * days, monthly package is calculated by 30 days, quarterly package is calculated by 90 days, and annual package is calculated by 365 days',
    anCiJiFeiGu:
      '1. Billing by Time: Estimated Total Price = Unit Price * Participating in Billing Flow',
    anZhaoJiFeiFang:
      'Estimate according to the billing method. estimate method:',
    chaXunLiuLiangShu:
      '3. Query Traffic: The data source service interface configuration selects query billing, regardless of whether or not the query is available',
    chaDeLiuLiangShu:
      '2. Checked traffic: The data source service interface configuration selects checked billing and requires successful checking',
    canYuJiFeiLiu2:
      '1. Participate in billing traffic = Checked traffic + Query traffic',
    yingYongXiTongJi: 'Application system billing report',
  },
  tab2: {
    gongTOTA: 'Totally {val1} Record(s)',
    junTanJiFeiFang:
      'The equalization billing method is the estimation method given by the system, which can only ensure the balance of internal and external income and expenditure on the statistical unit. you need to select an appropriate estimation method based on the scena',
    anRiJunTan: 'Divide it equally daily',
    qingXuanZeJunTan: 'Average daily share',
    qingXuanZeJiGou: 'Select an organization',
    qingXuanZeShuJu: 'Select interface',
    xiTongYiCiZui: 'The system can query for up to one year at a time',
    daoChu: 'Export',
    yueBaoNianBaoJi:
      '3. Monthly package, annual package and quarterly package will calculate the average daily cost',
    jiFeiLeiXingWei:
      '2. When the billing type is query billing, the amount of participating billing call=total flow - failed amount; When the billing type is queried, the amount of participating billing call=total flow - failure amount - success (the data source is not querie',
    anRiJiSuanJun:
      "1. The average cost (T+1) is calculated on a daily basis. The calculation formula is the total cost generated by multiplying the proportion of the single day's participating billing calls by the single day's system calls to the data source service interfa",
    chaKan: 'View',
    wuQuanXianCaoZuo: 'No permission operation',
    caoZuo: 'Action',
    anZhaoDiaoYongLiang:
      'According to the proportion of the call volume, the caller/caller group of other shared contracts will share the cost of system calls',
    junTanFeiYongGu: 'Estimated total price of shared expenses',
    junTanFeiYongGu2:
      'Estimated total price of shared expenses/participating billing traffic',
    junTanPingJunDan: 'Split the average cost evenly',
    guSuanZongJia: 'Estimated total price',
    canYuJiFeiLiu: 'Participating billing traffic',
    chanShengZongLiuLiang: 'Total generated traffic',
    jiFeiFangShi: 'Billing method',
    heTongBanBen: 'Contract version',
    heTongBianHao: 'Contract number',
    shuJuYuanFuWu: 'Data source service interface name',
    jiGou: 'Organization',
    jieTiJiFeiGu:
      '3. Ladder Billing: Estimated Total Price = Unit Price in Ladder Range * Participating in Billing Flow',
    yueBaoNianBaoJi2:
      '2. Monthly package, annual package and quarterly package: total estimated price=average daily price * days, monthly package is calculated by 30 days, quarterly package is calculated by 90 days, and annual package is calculated by 365 days',
    anCiJiFeiGu:
      '1. Billing by Time: Estimated Total Price = Unit Price * Participating in Billing Flow',
    anZhaoJiFeiFang:
      'Estimate according to the billing method. estimate method:',
    chaXunLiuLiangShu:
      '3. Query Traffic: The data source service interface configuration selects query billing, regardless of whether or not the query is available',
    chaDeLiuLiangShu:
      '2. Checked traffic: The data source service interface configuration selects checked billing and requires successful checking',
    canYuJiFeiLiu2:
      '1. Participate in billing traffic = Checked traffic + Query traffic',
    jiGouJiFeiBao: 'Organization billing report',
  },
  addmodifymodal: {
    mingChenZhiYunXu:
      'ETL processor name support Chinese, English, numbers, spaces, underscores, -, .',
    biaoShiZhiYunXu:
      'ETL processor code only supports the input combination of letters, numbers and underscores',
    mingChengBuNengChaoGuo: 'ETL processor name cannot exceed 200',
    biaoZhiBuNengChaoGuo: 'ETL processor identification cannot exceed 200',
    changDuBuNengChao:
      'The contract name cannot exceed 200 characters in length',
    changDuBuNengChao1:
      'The contract number cannot exceed 200 characters in length',
    fuWuBiaoShigBu: 'Service identification cannot exceed 200',
    fuWuMingChengBu: 'Service name length not exceeding 200',
    moRenZeYuShu:
      'The default is the same as the data source interface timeout',
    shuJuChaoShiShi: 'Data timeout (ms)',
    xiTongZiDongGen:
      'The system automatically matches the list of qualified data source service interfaces according to the output and input parameters for configuration. multiple interfaces can be specified and the switching priority can be set. the following two conditions ',
    fou: 'No',
    shi: 'Yes',
    qingXuanZe: 'Select',
    yiChangQieHuan: 'Abnormal switching',
    fanHuiLeiXing: 'Return type',
    moRenZeYuShu2:
      'The default is the same as the number of retries for the data source interface',
    shuJuZhongShiCi: 'Number of data retries',
    jieKouDiaoYongJi: 'Interface call mechanism',
    yiChuDangQianXing: 'Remove the current row',
    tianJiaYiXiang: 'Add an item',
    qingShuRuFenLiu: 'Enter the distributed flow ratio',
    buNengChongFuXuan: 'Non-repeatable selection',
    qingXuanZeJieKou: 'Select interface',
    qingXianXuanZeQu: 'Select a channel first',
    qingXuanZeLeiXing: 'Select type',
    shuJuYuanFuWu: 'Data source service interface name',
    qingXuanZeQuDao: 'Select a channel',
    quDao: 'Channel',
    qingTianXieYeWu: 'Enter service id of the business system',
    fuWuBiaoZhi: 'Service ID',
    qingShuRuFuWu: 'Enter the service name',
    fuWuMingCheng: 'Service name',
    jiBenXinXi: 'Basic information',
    queDing: 'OK',
    quXiao: 'Cancel',
    xiuGai: 'Modify',
    xinZeng: 'Add',
    diaoYongFangFuWu: 'Caller service',
    qingXianWanShanDang: 'Complete the current item configuration first',
    youBiTianXiangWei: 'The required fields are not filled in',
    fenLiuBiLiZong: 'The sum of distributed flow ratio is not equal to 100%',
    cunZaiFenLiuBi: 'An interface with 0% scattered flow ratio exists',
    zhiChiKuoZhanMing: 'Support extension name',
    shangChuanWenJian: 'Upload file',
    heTongFuJian: 'Contract attachment',
    lUAJiaoBen: 'Lua script',
    congShuXing: 'Subordinate field',
    heTongID: 'Contract id',
    shuJuYuan: 'Data source',
    zhuShuXing: 'Primary field',
    tongJiFangShi: 'Statistical method',
    shuRuFeiYongYuan: 'Input fee',
    jiFeiFangShi: 'Billing method',
    jieShuRiQi: 'End date',
    kaiShiRiQi: 'Start date',
    qingShuRu: 'Enter',
    heTongMingCheng: 'Contract name',
    heTongBianHaoZhi:
      'Only English and numbers are allowed for the contract number!',
    heTongBianHao: 'Contract number',
    heTongXiaZaiZhong: 'Contract download…',
    heTong: 'Contract',
    gaiWenJianYiShang: 'The file has been uploaded',
    chaoChuWenJianDa: 'The file size exceeds the upper limit by 500m',
    shangChuanGeShiJin:
      'The upload format only supports rar, zip, doc, docx, pdf, jpg, jpeg and png. please check the file format',
    shangChuanWenJianShu: 'A maximum of three files can be uploaded',
    jieShuRiQiBu: 'The end date cannot be earlier than the start date',
    kaiShiRiQiBu: 'The start date cannot be later than the end date',
    cunZaiBiTianXiang: 'There are required fields not filled in',
    geKongJianZhongDe:
      'The regular expression in each control cannot be repeated',
    youWeiTianXieDe:
      'There are controls that are not filled in, enter and save',
    chuCanBaoWen: 'Output parameter message',
    piPeiZiDuan: 'Matching field',
    xiuGaiMOC: 'Modify mock data',
    xinZengMOC: 'Add mock data',
    caoZuoChengGong: 'Operation successful',
    qingShuRuZhengQue: 'Enter the correct json message',
    baoWenBuNengWei: 'The message cannot be empty',
    zuiDuoGeZiFu: 'Up to 2000 characters',
    beiZhu: 'Remarks',
    gongGongDaiMaKuai: 'Common code block',
    shiYongFenYeCha: 'Apply paging query interface',
    eTLChuLi: 'Etl processor id',
    eTLChuLi2: 'Etl processor name',
    qianZhiETL:
      'The front etl processor is used to prepare data before service request, such as login check, input parameter processing, encryption and decryption, etc',
    eTLChuLi3: 'Type',
    baoCun: 'Save',
    zanCun: 'Stage',
    chaKan: 'View',
    zuiDuoWeiGeZi: 'Up to 2000 characters in length',
    biaoZhiBuNengWei: 'The id cannot be empty',
    mingChengBuNengWei: 'Name cannot be empty',
    chuangJianRen: 'Creator',
    chuangJianShiJian: 'Creation time',
    ruoTingYongDangQian:
      'If disabled, all external services under the current partner will be disabled',
    jinYong: 'Disable',
    tingYong: 'Disable',
    zhengChang: 'Normal',
    heZuoZhuangTai: 'Cooperation status',
    heZuoFangMingCheng: 'Partner name',
    heZuoFangJiBen: 'Basic information of partners',
    heZuoFangHeZuo: 'Partner cooperation status cannot be empty',
    heZuoFangMingCheng2: 'Partner name cannot exceed 200 characters in length',
    heZuoFangMingCheng3: 'Partner name cannot be empty',
    buNengWeiKong: ' cannot be empty',
  },
  routerconfig: {
    chengBenDi: 'Low cost',
    chengBenZhong: 'Medium cost',
    chengBenGao: 'High cost',
    zhiXinDuDi: 'Low confidence',
    zhiXinDuZhong: 'Medium confidence',
    zhiXinDuGao: 'High confidence',
    qingXuanZe: 'Select',
    youXianJi: 'Priority',
    xiaYi: 'Move down',
    shangYi: 'Move up',
    buNengChongFuXuan: 'Non-repeatable selection',
  },
  box: {
    kuWuLu: 'Ratios that do not have data in the database',
    kuWuLiang: 'Amount that do not have data in the database',
    shiBaiLu: 'Failure rate',
    shiBaiLiang: 'Number of failure',
    chengGongLu: 'Success rate',
    chengGongLiang: 'Number of success',
    diaoYongLiang: 'Number of calls',
  },
  batchquery: {
    dangQianJieGuoGong:
      'There are {val1} current results, {val2} successful results, and {val3} failed results. do you want to continue?',
    chaXunJieGuoZhong: 'Query results. repeated queries will incur expenses',
    ninYiCunZai: 'You already exist',
    zhongChaQuanBu: 'Re query all',
    zhongChaShiBai: 'Re query failed',
    quXiao: 'Cancel',
    qingXuanZeShuJu: 'Select the service interface',
    zhiXingChaXun: 'Execute query',
    yuLanShuJu: 'Preview data',
    caoZuo: 'Action',
    shangChuanRen: 'Uploader',
    shangChuanShiJian: 'Upload time',
    xuHao: 'Serial no',
    zuiDaZhiChiWan: 'Support up to 10000 pieces of data',
    shangChuanShuJu: 'Upload data',
    muBanXiaZai: 'Template download',
    chaXunXuYaoYi:
      'The query takes some time. please click "preview data" later to query progress and results',
    tiJiaoChaXunCheng: 'Query submitted successfully',
    shangChuanChengGong: 'Uploaded successfully',
    piLiangChaXunMo: 'Batch query template',
  },
  batchresult: {
    shiBai: 'Fail',
    chengGong: 'Success',
    tiao: 'Pieces',
    chaXunWanCheng: 'Query completed',
    daiZhaXun: 'Waiting query',
    zongTiaoShu: 'Total number',
    chaXunJinDu: 'Query progress',
    jieGuoShengChengShi: 'Result generation time',
    yiHaoShi: 'Elapsed time',
    shuaXin: 'Refresh',
    chaXunJieGuo: 'Query result ',
  },
  countrange: {
    qingShuRuZhengZe: 'Enter a regular expression',
    zhengZe: 'Regular expression',
    qingXuanZe: 'Select',
    xuanZeYiLaiXi: 'Select system field',
    piPeiZiDuan: 'Matching field',
    yuan: 'Yuan',
    shuRuFeiYongYuan: 'Input fee',
    feiYong: 'Cost',
    geKongJianZhongDe:
      'The regular expression in each control cannot be repeated',
  },
  daterange: {
    riQiQuJianJin:
      'The date range can only be selected within the validity period and completely covers the validity period; the starting time is fixed at 00:00:00, and the ending time is fixed at 23:59:59',
    shuRuFeiYongYuan: 'Input fee',
    zhi: 'To',
    xiTongZiDongTian: 'Auto fill',
    riQiBuNengDa:
      'The date cannot be later than or equal to the contract end date',
    diErGeRiQi: 'The second date should be greater than the first date',
    qingXianWanShanHe:
      'Complete the contract start date and contract end date first',
  },
  fieldpricesflowrange: {
    qingShuRuZhengZe: 'Enter a regular expression',
    zhengZe: 'Regular expression',
    qingXuanZe: 'Select',
    xuanZeYiLaiXi: 'Select',
    piPeiZiDuan: 'Matching field',
    yongBiaoShiQuJian:
      '- 99 indicates the positive infinity of the interval. This item cannot be deleted. It is the system default configuration item',
    shuRuFeiYongYuan: 'Input fee',
    shuRuQuJianZhi: 'Input interval value',
    zhi: 'To',
    xiTongZiDongTian: 'Auto fill',
    feiYong: 'Cost',
    geKongJianZhongDe:
      'The regular expression in each control cannot be repeated',
    diErGeQuJian:
      'The input value of the second interval should be greater than the input value of the first interval',
  },
  flowrange: {
    yongBiaoShiQuJian:
      '- 99 indicates the positive infinity of the interval. This item cannot be deleted. It is the system default configuration item',
    shuRuFeiYongYuan: 'Input fee',
    shuRuQuJianZhi: 'Input interval value',
    zhi: 'To',
    xiTongZiDongTian: 'Auto fill',
    diErGeQuJian:
      'The input value of the second interval should be greater than the input value of the first interval',
  },
  configmodal: {
    mOCKCan: 'Mock parameter configuration',
    miaoShu: 'Description',
    ruCan: 'Input parameter',
    caoZuoChengGong: 'Operation successful',
  },
  importmodal: {
    xiaZaiMuBan: 'Download template',
    dianJiXiaZaiMo: 'Click to download the template',
    shangChuanWenJian: 'Upload file',
    qingXuanZeShangChuan: 'Select a file to upload',
    daoRuMOC: 'Import mock data',
    muBan: 'Template',
    caoZuoChengGong: 'Operation successful',
    qingXuanZeWenJian: 'Select file',
  },
};
