export default {
  index: {
    bianJiQu: 'Edit area',
    yunXingQu: 'Run area',
    liuChengMuBanBiao: 'Process template ID',
    liuChengMuBanMing: 'Process template name',
    shuRuSouSuoNei: 'Enter search content',
    xin<PERSON><PERSON>: 'Add',
    shan<PERSON><PERSON>: 'Delete',
    ma: ']?',
    queRen<PERSON>han<PERSON>hu: 'Confirm deletion of [',
    xiuGai: 'Modify',
    cha<PERSON>an: 'View',
    bianPai: 'Configure',
    caoZuo: 'Action',
    xiuGaiShiJian: 'Modification time',
    xiuGaiRen: 'Modifier',
    ch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Creation time',
    chuangJian<PERSON>en: 'Creator',
    miao<PERSON><PERSON>: 'Description',
    shan<PERSON>hu<PERSON>hi<PERSON><PERSON>: 'Failed to delete',
    shan<PERSON>hu<PERSON>hengGong: 'Deleted successfully',
    fanHui: '< Back',
    quanBuQuDaoKe: 'Available for all channels',
    quanBuJiGouKe: 'Available for all institutions',
    shouQuanKeYongQu: 'Authorized Available Channels',
    shouQuanKeYongJi: 'Authorized Available Institutions',
    hanShuKuShouQuan: 'Function Library Authorization',
    shouQuanChengGong: 'Authorization Succeeded',
    liuChengMuBanShou: 'Process Template Authorization',
    shouQuan: 'Authorization',
    suoShuJiGou: 'Affiliated Institution',
    gongZuoLiuShouQuan: 'Workflow Authorization',
  },
};
