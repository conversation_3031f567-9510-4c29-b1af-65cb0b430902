export default {
  listmodal1: {
    gongMODA: 'Totally {val1} Record(s)',
    daoChu: 'Export',
    meiRiJiFeiMing: 'Daily billing details',
    chaKan: 'View',
    caoZuo: 'Action',
    danRiJunTanGu: 'Daily split estimate',
    danRiCanYuJi: 'Percentage of daily billing flow',
    danRiGuSuan: 'Single-day estimation',
    danRiCanYuJi2: 'Single-day participating billing flow',
    danRiChanShengZong: 'Total flow generated per day',
    riQi: 'Date',
    banBenHao: 'Version number',
    shuJuYuanFuWu: 'Data source service interface name',
    quDao: 'Channel',
    jieTiJiFeiDan:
      '3. Ladder Billing: Estimated Total Price = Unit Price in Ladder Range * Participating in Billing Flow',
    yueBaoNianBaoJi:
      '2. Monthly package, annual package and quarterly package: total estimated price=average daily price * days, monthly package is calculated by 30 days, quarterly package is calculated by 90 days, and annual package is calculated by 365 days',
    anCiJiFeiDan:
      '1. Billing by Time: Estimated Total Price = Unit Price * Participating in Billing Flow',
    anZhaoJiFeiFang:
      'Estimate according to the billing method. estimate method:',
    yingYongXiTongJi: 'Application system billing details report',
  },
  listmodal2: {
    gongMODA: 'Totally {val1} Record(s)',
    daoChu: 'Export',
    meiRiJiFeiMing: 'Daily billing details',
    chaKan: 'View',
    caoZuo: 'Action',
    danRiJunTanGu: 'Daily split estimate',
    danRiCanYuJi: 'Percentage of daily billing traffic',
    danRiGuSuan: 'Single-day estimation',
    danRiCanYuJi2: 'Single-day participating billing traffic',
    danRiChanShengZong: 'Total traffic generated per day',
    riQi: 'Date',
    banBenHao: 'Version number',
    shuJuYuanFuWu: 'Data source service interface name',
    jiGou: 'Organization',
    jieTiJiFeiDan:
      '3. Ladder Billing: Estimated Total Price = Unit Price in Ladder Range * Participating in Billing Flow',
    yueBaoNianBaoJi:
      '2. Monthly package, annual package and quarterly package: total estimated price=average daily price * days, monthly package is calculated by 30 days, quarterly package is calculated by 90 days, and annual package is calculated by 365 days',
    anCiJiFeiDan:
      '1. Billing by Time: Estimated Total Price = Unit Price * Participating in Billing Flow',
    anZhaoJiFeiFang:
      'Estimate according to the billing method. estimate method:',
    jiGouJiFeiMing: 'Organization billing details report',
  },
  onlinemodal: {
    qingShuRuShangXian: 'Enter the reason for go online',
    zuiDuoShuRuGe: 'Maximum 2000 characters',
    miaoShu: 'Description',
    piLiangShangXian: 'Batch online',
    shangXian: 'Go online',
  },
  publishresult: {
    queDing: 'OK',
    tiJiaoJieGuo: 'Submit result',
    miaoShu: 'Description',
    shiBai: 'Fail',
    chengGong: 'Success',
    jieGuo: 'result',
    hanShuMingCheng: 'Function Name',
  },
  addmodify: {
    qingTianXieMiaoShu: 'Fill in the description',
    zuiDuoGeZiFu: 'Up to 2000 characters',
    miaoShu: 'Description',
    qingTianXieGongZuo: 'Fill in the workflow ID',
    gongZuoLiuBiaoZhi:
      'The workflow ID only supports the input combination of letters, numbers and underscores',
    zuiDuoGeZiFu2: 'Maximum 200 characters',
    gongZuoLiuBiaoZhi2: 'Workflow ID',
    jinZhiChiZhongYing:
      'Only English, Chinese, numbers and underscore are supported',
    gongZuoLiuMingCheng:
      'Support Chinese, English, numbers, spaces, underscores, -, .',
    qingTianXieGongZuo2: 'Fill in the workflow name',
    gongZuoLiuMingCheng2: 'Workflow name',
    tITLE: '{val1}Process Template',
    queDing: 'OK',
    quXiao: 'Cancel',
    tITLE2: '{val1} process template failed',
    tITLE3: '{val1}Process template success',
    xinZeng: 'Add',
    qingTianXieLiuCheng: 'Fill in the process template ID',
    liuChengMuBanBiao:
      'The process template ID only supports the combination of letters, numbers and underscores',
    liuChengMuBanBiao2: 'Process template ID',
    liuChengMuBanMing:
      'The process template name only support Chinese, English, numbers, spaces, underscores, -, .',
    qingTianXieLiuCheng2: 'Fill in the process template name',
    liuChengMuBanMing2: 'Process template name',
    qingXuanZeSuoShu: 'Please select the affiliated channel',
    jiGouMingCheng: 'Institution Name',
    xuanZeJiGou: 'Select the institution',
    suoShuJiGou: 'Affiliated Institution',
  },
  importworkflow: {
    fuGaiXiangTongBiao: 'Override the same ID and continue importing',
    tiaoGuoXiangTongBiao: 'Skip the same ID and continue importing',
    qingXuanZeChuLi: 'Select a processing method',
    xiangTongGongZuoLiu: 'Processing method with the same workflow ID',
    dianJiXuanZeWen: 'Click to select a file',
    qingXuanZeShangChuan: 'Select a file to upload',
    xuanZeWenJian: 'Select file',
    daoRuGongZuoLiu: 'Import workflow',
    queDing: 'OK',
    quXiao: 'Cancel',
    wenJianDaXiaoBu: 'The file size can not exceed 50M, please re-upload',
    wenJianLeiXingCuo: 'The file type is wrong, please re-upload',
    daoRuShiBai: 'Import failed',
    daoRuChengGong: 'Imported successfully',
  },
  offlinemodal: {
    qingShuRuYiJian: 'Enter your opinion, less than 2000 words',
    qingShuRuYiJian2: 'Enter your opinion',
    yiJian: 'Comment',
    yingYongFuWuXia: 'Application service offline',
    xiaXianShiBai: 'Failed to go offline',
    caoZuoChengGong: 'Operation successful',
  },
  publish: {
    qingShuRuFaBan: 'Enter a description of your posting',
    faBanMiaoShuZui:
      'The maximum length of the board description is 2000 characters',
    miaoShu: 'Description',
    gongZuoLiuMingCheng: 'Workflow name',
    shiBai: 'Fail',
    chengGong: 'Success',
    gongZuoLiuShangXian: 'Workflow Online',
    gongZuoLiuPiLiang: 'Workflow Batch Online',
  },
};
