export default {
  common: {
    tiao<PERSON>ianJunBuMan: 'None of the conditions are met',
    manZuRenYiTiao: 'Meet any conditions',
    manZuSuoYouTiao: 'Meet all conditions',
  },
  index: {
    buEr: 'Boolean',
    meiJu: 'Enumeration',
    riQi: 'Date',
    xiao<PERSON>hu: 'Demical',
    fu<PERSON>ian: 'Float',
    shi<PERSON><PERSON>: 'Date',
    zheng<PERSON>hu: 'Integer',
    duiXiang: 'Object',
    ziFu: 'String',
    shuZu: 'Array',
    zuiJinRi: 'Last 90 days',
    zuiJinRi2: 'Last 30 days',
    zuiJinRi3: 'Last 7 days',
    zuoRi: 'Yesterday',
    jinRi: 'Today',
    gongSiMingChengMo: 'Fuzzy matching of company name, #fuzzy_comp(@name)',
    danWeiMingChengPi: 'Unit name matching',
    diZhiMoHuPi: 'Address fuzzy matching, #fuzzy_addr(@name)',
    diZhiPiPei: 'Address matching',
    moHuPiPeiHan: 'Fuzzy matching function',
    fanHuiSuiJiShu:
      'Return random number, #math_randomInt(10) will return random integers between [0,100), and #math_randomInt(100,1000) will return random integers between [100,1000)',
    suiJiShuZhiDing: 'Random number (specified interval)',
    fanHuiDeSuiJi: 'Return random demical in 0-1, #math_random()',
    suiJiShu: 'Random number (0-1)',
    shuZhiQiuPingJun: 'Averaging, #math_avg(@a1,@a2,@a3)',
    qiuJunZhi: 'Finding the mean',
    qiuDuiShuMA: 'Find logarithm, #math_log(@a,3)',
    qiuDuiShu: 'Find logarithm',
    shuZhiQiuHeM: 'Summing, #math_sum(@a1,@a2,@a3)',
    qiuHe: 'Sum',
    qiuZhiShuMA: 'Find the exponent, #math_pow(@a,3)',
    qiuZhiShu: 'Find index',
    siSheWuRuM: 'Rounding, #math_round(@salary)',
    siSheWuRu: 'Rounding',
    xiangXiaQuZhengM: 'Round down, #math_floor(@salary)',
    xiangXiaQuZheng: 'Round down',
    xiangShangQuZhengM: 'Round up, #math_ceil(@salary)',
    xiangShangQuZheng: 'Round up',
    qiuZuiXiaoZhiM: 'Find the min, #math_min(@a1,@a2,@a3)',
    zuiXiaoZhi: 'Min',
    qiuZuiDaZhiM: 'Find the max, #math_max(@a1,@a2,@a3',
    zuiDaZhi: 'Max',
    qiuJueDuiZhiM: 'Find absolute value, #math_abs(@age)',
    jueDuiZhi: 'Absolute value',
    shuZhiHanShu: 'Numerical function',
    shouZiMuDaXie: 'Capitalize, #str_capital(@name)',
    jieQuZiChuanS: 'Intercept substring, #str_slice(@name,0,10)',
    jieQuZiChuan: 'Intercept substring',
    huoQuZiChuanDe:
      'Get the index of the substring, #str_indexOf(@name, "xxx")',
    ziChuanSuoYin: 'Substring index',
    huLueDaXiaoXie:
      'Judge whether the contents of two strings are the same,ignore the case, #str_equalsIgnore(@a,@b)',
    xiangDengHuLueDa: 'Equal (ignoring case)',
    panDuanLiangGeZi:
      'Judge whether the contents of two strings are the same, #str_equals(@a,@b)',
    xiangDeng: 'Equal',
    kongChuanChuLiS:
      'Empty string processing, #str_IfEmpty (a, "hello") means that if a is null or an empty string, return to hello',
    kongZhiChuLi: 'Null value processing',
    ruGuoWeiNU:
      'If null, it will be returned as the default value, #str_Ifnull (a, "hello") means to return hello if a is null',
    panDuanShiFouWei: 'Determine if it is empty',
    jiangQiTaLeiXing:
      'Convert other types of data to strings, #str_tostr(@name)',
    zhuanWeiZiFuChuan: 'Convert to string',
    qingChuZiFuChuan:
      'Clear leading and trailing spaces of string, #str_trim(@name)',
    qingChuShouWeiKong: 'Clear first and last spaces',
    ziFuChuanChangDu: 'String length, #str_len(@name)',
    panDuanZiFuChuan:
      'Judge whether the string contains the specified one, #str_contain(@name, "xxx")',
    baoHan: 'Include',
    panDuanZiFuChuan2:
      'Judge whether the string ends with the specified one, #str_endsWith(@name, "xxx")',
    houZhuiPiPei: 'Suffix matching',
    panDuanZiFuChuan3:
      'Judge whether the string starts with the specified string, #str_startsWith(@name, "xxx")',
    qianZhuiPiPei: 'Prefix match ',
    ziFuChuanDaXie: 'String uppercase, #str_upper(@name)',
    zhuanDaXie: 'Upper',
    ziFuChuanXiaoXie: 'String lower case, #str_lower(@name)',
    zhuanXiaoXie: 'Lower',
    ziFuChuanTiHuan: 'String replacement, #str_replace(@name,"xxx", "yyy")',
    ziFuChuanPinJie: 'String splicing, #str_concat(@a,@b)',
    ziFuChuanMD: 'String md5 value, #str_md5(@name)',
    mDJiaMi: 'MD5 encryption',
    ziFuChuanHanShu: 'String function',
    zhongZiJieDianKuo: 'Seed node extension',
    sanYuanZuShaiXuan: 'Triple filtering',
    miao: ' Second(s) ',
    fenZhong: ' Minute(s) ',
    xiaoShi: ' Hour(s) ',
    tian: ' Day(s) ',
    yue: ' Month(s) ',
    nian: ' Year(s) ',
    shiShiZhiBiao: 'Realtime indicator',
    shuJuBiao: 'Data table',
    qunZu: 'Group',
    ziTu: 'Subgraph',
    zhuTu: 'Main graph',
    qunZuZhiBiao: 'Group indicator',
    shiTiZhiBiao: 'Entity indicator',
    duiGongChangJing: 'Public scenarios',
    tongYongChangJing: 'Common scenarios',
    tuPu: 'Graph',
    weiDaoRu: 'Not imported',
    buFenDaoRu: 'Partial import',
    quanBuDaoRu: 'Import all',
    yiShanChu: 'Deleted',
    faBuShiBai: 'Published failed',
    yiFaBu: 'Released',
    faBuZhong: 'Publishing',
    yiJiuXu: 'Ready',
    jieDianJinYong: 'Node disable',
    yiTingZhi: 'Stopped',
    shiBai: 'Fail',
    wanCheng: 'Complete',
    tingZhiZhong: 'Stopping',
    yunXingZhong: 'Running',
    tiJiaoZhong: 'Submitting',
    daiYunXing: 'Pending run',
    genJuShenFenZheng: 'Calculate age according to identifier card',
    genJuChuShengRi: 'Calculate age based on date of birth',
    qianTian: 'Last 360 days',
    qianTian2: 'Last 180 days',
    qianTian3: 'Last 90 days',
    qianTian4: 'Last 60 days',
    qianTian5: 'Last 30 days',
    qianTian6: 'Last 7 days',
    qianTian7: 'Last 1 day',
    dangQianRiQi: 'Current date',
    tuiLi: 'Reasoning',
    yeWuQunZu: 'Business group',
    suanFaQunZu: 'Algorithm group',
    xiaoYuDengYu: 'Less than or equal to',
    xiaoYu: 'Less than',
    daYuDengYu: 'Greater than or equal to',
    daYu: 'Greater than',
    buDengYu: 'Not equal to',
    dengYu: 'Equal to',
    houZhui: 'Suffix',
    qianZhui: 'Prefix',
    jieYu: 'Be situated between',
    buWeiKongPanDuan: 'Not null judgment',
    weiKongPanDuan: 'Null judgment',
    shenFenZhengHaoXiao: 'ID number verification',
    shouJiHaoJiaoYan: 'Phone number verification',
    youXiangJiaoYan: 'Mailbox verification',
    zhengZePiPei: 'Regular matching',
    buWeiKong: 'Not empty',
    weiKong: 'Empty',
    zhengZe: 'Regular',
    buBaoHan: 'Exclude',
    huo: 'Or',
    qie: 'And',
    manZuYiXiaRen: 'Meet any of the following',
    manZuYiXiaSuo: 'Meet all of the following conditions',
    jingQue: 'Accurate',
    moHu: 'Fuzzy',
    fuGai: 'Overwrite',
    gengXin: 'Update',
    zhuiJia: 'Append',
  },
  workflow: {
    yiXiaXian: 'Offline',
    yiShangXian: 'Online',
    daoRuDaiTiJiao: 'Imported',
    daiTiJiao: 'Pending submission',
  },
};
