export default {
  defaultdataconvert: {
    peiZhiBuNengWei: '配置不能为空',
    peiZhiBuHeFa: '配置不合法，原因如下：',
    liuChengMuBanWei: '[流程模板]未设置节点名称',
    liuChengMuBan: '流程模板',
    dianYouYuJingWei: '[电邮预警]未设置邮件内容',
    dianYouYuJingWei2: '[电邮预警]未设置邮件地址',
    dianYouYuJingWei3: '[电邮预警]未设置邮件标题',
    dianYouYuJing: '电邮预警',
    jiXuBuChongWei: '[继续补充]未设置决策补充服务',
    jiXuBuChong: '继续补充',
    fenLiuBiLiLei: ']分流比例累加不等于100',
    fenLiuBiLiWei: ']分流比例未填写',
    tiaoJianPeiZhiWei: ']条件配置未填写',
    zhiShaoPeiZhiTiao: ']至少配置2条输出线',
    zhiNengLuYouWei: '[智能路由]未设置节点名称',
    zhiNengLuYou: '智能路由',
    aPIJieKou: '[API接口]未设置执行数据',
    aPIJieKou2: 'API接口',
    zhiNengPeiZhiYi: ']只能配置一条输出线',
    xian: '线',
    shuRu: '输入',
    shuChu: '输出',
    tiao: '条',
    zuiDuoPeiZhi: ']最多配置',
    kaiShi: '开始',
    jieShu: '结束',
    bingXing: '[并行',
    zhiShaoPeiZhiTiao2: ']至少配置2条',
    bingXing2: '并行',
    xuYaoQieZhiNeng: ']需要且只能配置1条默认分支',
    panDuanKaiShiTiao: '[判断开始]条件配置未填写',
    panDuan: '判断',
    hanShuWeiSheZhi: '[函数]未设置执行数据',
    jiaoBenHanShu: '脚本函数',
    jieShuQueShaoShu: '[结束]缺少输入流',
    kaiShiKaiShiJie: '[开始]开始节点只能有一个',
    kaiShiQueShaoShu: '[开始]缺少输出流',
    queShaoShuChuLiu: ']缺少输出流',
    queShaoShuRuLiu: ']缺少输入流',
    queShaoShuRuShu: ']缺少输入输出流',
    canShuPeiZhiGe: '参数配置格式错误',
    shuJuBiaoBuNeng: '数据表不能为空',
    shuJuYuanLeiXing: '数据源类型不能为空',
    shuJuBiaoMingCheng: '数据表名称不能为空',
  },
  index: {
    yunXingShiBai: '运行失败',
    yunXingZhong: '运行中',
    yunXingWanCheng: '运行完成',
    zhiNengLuYou: '智能路由',
    jieXiShuJuCuo: '解析数据错误,',
    zhiNengYouYiGe: '只能有一个输出',
    buNengSheZhiShu: '不能设置输出流',
    buNengSheZhiShu2: '不能设置输入流',
  },
};
