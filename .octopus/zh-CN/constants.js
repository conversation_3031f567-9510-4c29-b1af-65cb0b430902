export default {
  common: {
    tiaoJianJunBuMan: '条件均不满足',
    manZuRenYiTiao: '满足任意条件',
    manZuSuoYouTiao: '满足所有条件',
  },
  index: {
    buEr: '布尔',
    meiJu: '枚举',
    riQi: '日期',
    xiao<PERSON>hu: '小数',
    fuDian: '浮点',
    shi<PERSON><PERSON>: '时间',
    zhengShu: '整数',
    duiXiang: '对象',
    ziFu: '字符',
    shuZu: '数组',
    zuiJinRi: '最近90日',
    zuiJinRi2: '最近30日',
    zuiJinRi3: '最近7日',
    zuoRi: '昨日',
    jinRi: '今日',
    gongSiMingChengMo: '公司名称模糊匹配，#fuzzy_comp(@name)',
    danWeiMingChengPi: '单位名称匹配',
    diZhiMoHuPi: '地址模糊匹配，#fuzzy_addr(@name)',
    diZhiPiPei: '地址匹配',
    moHuPiPeiHan: '模糊匹配函数',
    fanHuiSuiJiShu:
      '返回随机数，#math_randomInt(10)会返回[0,100)之间的随机整数，#math_randomInt(100,1000)则会返回[100,1000)之间的随机整数',
    suiJiShuZhiDing: '随机数（指定区间）',
    fanHuiDeSuiJi: '返回0-1的随机小数，#math_random()',
    suiJiShu: '随机数（0-1）',
    shuZhiQiuPingJun: '数值求平均，#math_avg(@a1, @a2, @a3)',
    qiuJunZhi: '求均值',
    qiuDuiShuMA: '求对数，#math_log(@a, 3)',
    qiuDuiShu: '求对数',
    shuZhiQiuHeM: '数值求和，#math_sum(@a1, @a2, @a3)',
    qiuHe: '求和',
    qiuZhiShuMA: '求指数，#math_pow(@a, 3)',
    qiuZhiShu: '求指数',
    siSheWuRuM: '四舍五入，#math_round(@salary)',
    siSheWuRu: '四舍五入',
    xiangXiaQuZhengM: '向下取整，#math_floor(@salary)',
    xiangXiaQuZheng: '向下取整',
    xiangShangQuZhengM: '向上取整，#math_ceil(@salary)',
    xiangShangQuZheng: '向上取整',
    qiuZuiXiaoZhiM: '求最小值，#math_min(@a1, @a2, @a3)',
    zuiXiaoZhi: '最小值',
    qiuZuiDaZhiM: '求最大值，#math_max(@a1, @a2, @a3)',
    zuiDaZhi: '最大值',
    qiuJueDuiZhiM: '求绝对值，#math_abs(@age)',
    jueDuiZhi: '绝对值',
    shuZhiHanShu: '数值函数',
    shouZiMuDaXie: '首字母大写',
    jieQuZiChuanS: '截取子串，#str_slice(@name,0,10)',
    jieQuZiChuan: '截取子串',
    huoQuZiChuanDe: '获取子串的索引，#str_indexOf(@name, "xxx")',
    ziChuanSuoYin: '子串索引',
    huLueDaXiaoXie:
      '忽略大小写判断两个字符串内容相同，#str_equalsIgnore(@a,@b)',
    xiangDengHuLueDa: '相等(忽略大小写)',
    panDuanLiangGeZi: '判断两个字符串内容相同，#str_equals(@a,@b)',
    xiangDeng: '相等',
    kongChuanChuLiS:
      '空串处理，#str_ifEmpty(a, "hello")意思是a为null或者空字符串则返回hello',
    kongZhiChuLi: '空值处理',
    ruGuoWeiNU:
      '如果为null，则以默认值返回，#str_ifnull(a, "hello")  意思是如果a为null则返回hello',
    panDuanShiFouWei: '判断是否为空',
    jiangQiTaLeiXing: '将其他类型数据转换成字符串，#str_tostr(@name)',
    zhuanWeiZiFuChuan: '转为字符串',
    qingChuZiFuChuan: '清除字符串首尾空格，#str_trim(@name)',
    qingChuShouWeiKong: '清除首尾空格',
    ziFuChuanChangDu: '字符串长度',
    panDuanZiFuChuan:
      '判断字符串是否包含指定字符串，#str_contain(@name, "xxx")',
    baoHan: '包含',
    panDuanZiFuChuan2:
      '判断字符串是否以指定字符串结尾，#str_endsWith(@name, "xxx")',
    houZhuiPiPei: '后缀匹配',
    panDuanZiFuChuan3:
      '判断字符串是否以指定字符串开头，#str_startsWith(@name, "xxx")',
    qianZhuiPiPei: '前缀匹配',
    ziFuChuanDaXie: '字符串大写，#str_upper(@name)',
    zhuanDaXie: '转大写',
    ziFuChuanXiaoXie: '字符串小写，#str_lower(@name)',
    zhuanXiaoXie: '转小写',
    ziFuChuanTiHuan: '字符串替换',
    ziFuChuanPinJie: '字符串拼接',
    ziFuChuanMD: '字符串md5值,#str_md5(@name)',
    mDJiaMi: 'MD5加密',
    ziFuChuanHanShu: '字符串函数',
    zhongZiJieDianKuo: '种子节点扩展',
    sanYuanZuShaiXuan: '三元组筛选',
    miao: '秒',
    fenZhong: '分钟',
    xiaoShi: '小时',
    tian: '天',
    yue: '月',
    nian: '年',
    shiShiZhiBiao: '实时指标',
    shuJuBiao: '数据表',
    qunZu: '群组',
    ziTu: '子图',
    zhuTu: '主图',
    qunZuZhiBiao: '群组指标',
    shiTiZhiBiao: '实体指标',
    duiGongChangJing: '对公场景',
    tongYongChangJing: '通用场景',
    tuPu: '图谱',
    weiDaoRu: '未导入',
    buFenDaoRu: '部分导入',
    quanBuDaoRu: '全部导入',
    yiShanChu: '已删除',
    faBuShiBai: '发布失败',
    yiFaBu: '已发布',
    faBuZhong: '发布中',
    yiJiuXu: '已就绪',
    jieDianJinYong: '节点禁用',
    yiTingZhi: '已停止',
    shiBai: '失败',
    wanCheng: '完成',
    tingZhiZhong: '停止中',
    yunXingZhong: '运行中',
    tiJiaoZhong: '提交中',
    daiYunXing: '待运行',
    genJuShenFenZheng: '根据身份证计算年龄',
    genJuChuShengRi: '根据出生日期计算年龄',
    qianTian: '前360天',
    qianTian2: '前180天',
    qianTian3: '前90天',
    qianTian4: '前60天',
    qianTian5: '前30天',
    qianTian6: '前7天',
    qianTian7: '前1天',
    dangQianRiQi: '当前日期',
    tuiLi: '推理',
    yeWuQunZu: '业务群组',
    suanFaQunZu: '算法群组',
    xiaoYuDengYu: '小于等于',
    xiaoYu: '小于',
    daYuDengYu: '大于等于',
    daYu: '大于',
    buDengYu: '不等于',
    dengYu: '等于',
    houZhui: '后缀',
    qianZhui: '前缀',
    jieYu: '介于',
    buWeiKongPanDuan: '不为空判断',
    weiKongPanDuan: '为空判断',
    shenFenZhengHaoXiao: '身份证号校验',
    shouJiHaoJiaoYan: '手机号校验',
    youXiangJiaoYan: '邮箱校验',
    zhengZePiPei: '正则匹配',
    buWeiKong: '不为空',
    weiKong: '为空',
    zhengZe: '正则',
    buBaoHan: '不包含',
    huo: '或',
    qie: '且',
    manZuYiXiaRen: '满足以下任意条件',
    manZuYiXiaSuo: '满足以下所有条件',
    jingQue: '精确',
    moHu: '模糊',
    fuGai: '覆盖',
    gengXin: '更新',
    zhuiJia: '追加',
  },
  workflow: {
    yiXiaXian: '已下线',
    yiShangXian: '已上线',
    daoRuDaiTiJiao: '导入待提交',
    daiTiJiao: '待提交',
  },
};
