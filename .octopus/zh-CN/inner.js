export default {
  listmodal: {
    gongMODA: '共 {val1} 条记录',
    daoChu: '导出',
    meiRiJiFeiMing: '每日计费明细',
    chaKan: '查看',
    caoZuo: '操作',
    danRiGuSuan: '单日估算',
    danRiCanYuJi: '单日参与计费流量',
    diaoYongChengGongDe: '调用成功的流量数量，参与流量上限的统计和预警',
    danRiChengGongLiu: '单日成功流量',
    danRiChanShengZong: '单日产生总流量',
    jiFeiFangShi: '计费方式',
    riQi: '日期',
    banBenHao: '版本号',
    heZuoFangMingCheng: '合作方名称',
    heTongMingCheng: '合同名称',
    jieTiJiFeiDan: '3.阶梯计费：单日估算=阶梯区间的单价 * 参与计费流量',
    yueBaoNianBaoJi:
      '2.月包、年包、季包：单日估算=每天平均价格 * 天数，月包 按30天计算，季包 按90天计算，年包按365天计算',
    anCiJiFeiDan: '1.按次计费：单日估算= 单价 * 参与计费流量',
    anZhaoJiFeiFang: '按照计费方式进行估算，估算方法：',
    heTongJiFeiMing: '合同计费明细报表',
    shuJuYuanFuWu: '数据源服务接口名称',
    shuJuYuanJiFei: '数据源计费明细报表',
  },
  tab1: {
    gongTOTA: '共 {val1} 条记录',
    junTanJiFeiFang:
      '均摊计费方式是系统给出的估算方法，只能保证在统计单位上内外收支平衡。需要根据场景选择适合的估算方法。',
    anRiJunTan: '按日均摊',
    qingXuanZeJunTan: '请选择均摊计费方式',
    qingXuanZeShuJu: '请选择数据源服务接口',
    xiTongYiCiZui: '系统一次最多支持查询一年',
    daoChu: '导出',
    yueBaoNianBaoJi: '3.月包、年包、季包会计算平均每天费用进行计算。',
    jiFeiLeiXingWei:
      '2.计费类型为查询计费时，参与计费调用量=总流量-失败量；计费类型为查得计费时，参与计费调用量=总流量-失败量-成功（数据源未查得）。',
    anRiJiSuanJun:
      '1. 按日计算均摊费用（T+1），计算公式是 单日参与计费调用量占比 乘以 单日系统调用数据源服务接口产生的总费用。',
    chaKan: '查看',
    wuQuanXianCaoZuo: '无权限操作',
    caoZuo: '操作',
    anZhaoDiaoYongLiang:
      '按照调用量占比，和其他共用合同的调用方/调用方组，共同均摊系统调用所产生的费用。',
    junTanFeiYongGu: '均摊费用估算总价',
    junTanFeiYongGu2: '均摊费用估算总价/参与计费流量',
    junTanPingJunDan: '均摊平均单次成本',
    guSuanZongJia: '估算总价',
    canYuJiFeiLiu: '参与计费流量',
    chanShengZongLiuLiang: '产生总流量',
    jiFeiFangShi: '计费方式',
    heTongBanBen: '合同版本',
    heTongBianHao: '合同编号',
    shuJuYuanFuWu: '数据源服务接口名称',
    quDao: '渠道',
    jieTiJiFeiGu: '3.阶梯计费：估算总价=阶梯区间的单价 * 参与计费流量',
    yueBaoNianBaoJi2:
      '2.月包、年包、季包：估算总价=每天平均价格 * 天数，月包 按30天计算，季包 按90天计算，年包按365天计算',
    anCiJiFeiGu: '1.按次计费：估算总价= 单价 * 参与计费流量',
    anZhaoJiFeiFang: '按照计费方式进行估算，估算方法：',
    chaXunLiuLiangShu:
      '3.查询流量：数据源服务接口配置选查询计费，不考虑是否查得。',
    chaDeLiuLiangShu:
      '2.查得流量：数据源服务接口配置选查得计费，要求成功查得。',
    canYuJiFeiLiu2: '1.参与计费流量 = 查得流量 + 查询流量。',
    yingYongXiTongJi: '应用系统计费报表',
  },
  tab2: {
    gongTOTA: '共 {val1} 条记录',
    junTanJiFeiFang:
      '均摊计费方式是系统给出的估算方法，只能保证在统计单位上内外收支平衡。需要根据场景选择适合的估算方法。',
    anRiJunTan: '按日均摊',
    qingXuanZeJunTan: '请选择均摊计费方式',
    qingXuanZeJiGou: '请选择机构',
    qingXuanZeShuJu: '请选择数据源服务接口',
    xiTongYiCiZui: '系统一次最多支持查询一年',
    daoChu: '导出',
    yueBaoNianBaoJi: '3.月包、年包、季包会计算平均每天费用进行计算。',
    jiFeiLeiXingWei:
      '2.计费类型为查询计费时，参与计费调用量=总流量-失败量；计费类型为查得计费时，参与计费调用量=总流量-失败量-成功（数据源未查得）。',
    anRiJiSuanJun:
      '1. 按日计算均摊费用（T+1），计算公式是 单日参与计费调用量占比 乘以 单日系统调用数据源服务接口产生的总费用。',
    chaKan: '查看',
    wuQuanXianCaoZuo: '无权限操作',
    caoZuo: '操作',
    anZhaoDiaoYongLiang:
      '按照调用量占比，和其他共用合同的调用方/调用方组，共同均摊系统调用所产生的费用。',
    junTanFeiYongGu: '均摊费用估算总价',
    junTanFeiYongGu2: '均摊费用估算总价/参与计费流量',
    junTanPingJunDan: '均摊平均单次成本',
    guSuanZongJia: '估算总价',
    canYuJiFeiLiu: '参与计费流量',
    chanShengZongLiuLiang: '产生总流量',
    jiFeiFangShi: '计费方式',
    heTongBanBen: '合同版本',
    heTongBianHao: '合同编号',
    shuJuYuanFuWu: '数据源服务接口名称',
    jiGou: '机构',
    jieTiJiFeiGu: '3.阶梯计费：估算总价=阶梯区间的单价 * 参与计费流量',
    yueBaoNianBaoJi2:
      '2.月包、年包、季包：估算总价=每天平均价格 * 天数，月包 按30天计算，季包 按90天计算，年包按365天计算',
    anCiJiFeiGu: '1.按次计费：估算总价= 单价 * 参与计费流量',
    anZhaoJiFeiFang: '按照计费方式进行估算，估算方法：',
    chaXunLiuLiangShu:
      '3.查询流量：数据源服务接口配置选查询计费，不考虑是否查得。',
    chaDeLiuLiangShu:
      '2.查得流量：数据源服务接口配置选查得计费，要求成功查得。',
    canYuJiFeiLiu2: '1.参与计费流量 = 查得流量 + 查询流量。',
    jiGouJiFeiBao: '机构计费报表',
  },
  addmodifymodal: {
    mingChenZhiYunXu: 'ETL处理器名称只支持中文, 英文, 数字, 空格, 下划线, -, .',
    biaoShiZhiYunXu: 'ETL处理器标识只支持字母、数字、下划线的输入组合',
    mingChengBuNengChaoGuo: 'ETL处理器名称不能超过200',
    biaoZhiBuNengChaoGuo: 'ETL处理器标识不能超过200',
    changDuBuNengChao: '合同名称长度不能超过200',
    changDuBuNengChao1: '合同编号长度不能超过200',
    fuWuBiaoShigBu: '服务标识不超过200',
    fuWuMingChengBu: '服务名称长度不超过200',
    moRenZeYuShu: '默认则与数据源接口超时时间一致',
    shuJuChaoShiShi: '数据超时时间(ms)',
    xiTongZiDongGen:
      '系统自动根据输出入参匹配符合条件的数据源服务接口列表供选择配置，可指定多个，设置切换优先级。以下两种情况触发异常切换：1.已达到配置重试的次数后，仍然失败 2.当前接口已达到设置的流量上限控制',
    fou: '否',
    shi: '是',
    qingXuanZe: '请选择',
    yiChangQieHuan: '异常切换',
    fanHuiLeiXing: '返回类型',
    moRenZeYuShu2: '默认则与数据源接口重试次数一致',
    shuJuZhongShiCi: '数据重试次数',
    jieKouDiaoYongJi: '接口调用机制',
    yiChuDangQianXing: '移除当前行',
    tianJiaYiXiang: '添加一项',
    qingShuRuFenLiu: '请输入分流比例',
    buNengChongFuXuan: '不能重复选择',
    qingXuanZeJieKou: '请选择接口',
    qingXianXuanZeQu: '请先选择渠道',
    qingXuanZeLeiXing: '请选择类型',
    shuJuYuanFuWu: '数据源服务接口名称',
    qingXuanZeQuDao: '请选择渠道',
    quDao: '渠道',
    qingTianXieYeWu: '请填写业务系统服务标识',
    fuWuBiaoZhi: '服务标识',
    qingShuRuFuWu: '请输入服务名称',
    fuWuMingCheng: '服务名称',
    jiBenXinXi: '基本信息',
    queDing: '确定',
    quXiao: '取消',
    xiuGai: '修改',
    xinZeng: '新增',
    diaoYongFangFuWu: '调用方服务',
    qingXianWanShanDang: '请先完善当前项配置',
    youBiTianXiangWei: '有必填项未填',
    fenLiuBiLiZong: '分流比例总和不等于100%',
    cunZaiFenLiuBi: '存在分流比例为0%的接口',
    zhiChiKuoZhanMing: '支持扩展名',
    shangChuanWenJian: '上传文件',
    heTongFuJian: '合同附件',
    lUAJiaoBen: 'Lua脚本',
    congShuXing: '从属性',
    heTongID: '合同ID',
    shuJuYuan: '数据源',
    zhuShuXing: '主属性',
    tongJiFangShi: '统计方式',
    shuRuFeiYongYuan: '输入费用/元',
    jiFeiFangShi: '计费方式',
    jieShuRiQi: '结束日期',
    kaiShiRiQi: '开始日期',
    qingShuRu: '请输入',
    heTongMingCheng: '合同名称',
    heTongBianHaoZhi: '合同编号只允许输入英文和数字！',
    heTongBianHao: '合同编号',
    heTongXiaZaiZhong: '合同下载中...',
    heTong: '合同',
    gaiWenJianYiShang: '该文件已上传',
    chaoChuWenJianDa: '超出文件大小上限500M',
    shangChuanGeShiJin:
      '上传格式仅支持rar、zip、doc、docx、pdf、jpg、jpeg、png请检查文件格式',
    shangChuanWenJianShu: '上传文件数最多3个',
    jieShuRiQiBu: '结束日期不能小于开始日期',
    kaiShiRiQiBu: '开始日期不能大于结束日期',
    cunZaiBiTianXiang: '存在必填项未填',
    geKongJianZhongDe: '各控件中的正则不能重复',
    youWeiTianXieDe: '有未填写的控件，请填写后保存',
    chuCanBaoWen: '出参报文',
    piPeiZiDuan: '匹配字段',
    xiuGaiMOC: '修改mock数据',
    xinZengMOC: '新增mock数据',
    caoZuoChengGong: '操作成功',
    qingShuRuZhengQue: '请输入正确的JSON报文',
    baoWenBuNengWei: '报文不能为空',
    zuiDuoGeZiFu: '最多2000个字符',
    beiZhu: '备注',
    gongGongDaiMaKuai: '公共代码块',
    shiYongFenYeCha: '适用分页查询接口',
    eTLChuLi: 'ETL处理器标识',
    eTLChuLi2: 'ETL处理器名称',
    qianZhiETL:
      '前置ETL处理器用于准备服务请求前的数据；后置ETL处理器用于组装服务返回后的结果。',
    eTLChuLi3: 'ETL处理器类型',
    baoCun: '保存',
    zanCun: '暂存',
    chaKan: '查看',
    zuiDuoWeiGeZi: '最多为2000个字符长度',
    biaoZhiBuNengWei: '标识不能为空',
    mingChengBuNengWei: '名称不能为空',
    chuangJianRen: '创建人',
    chuangJianShiJian: '创建时间',
    ruoTingYongDangQian: '若停用，当前合作方下的三方服务将全部停用',
    jinYong: '禁用',
    tingYong: '停用',
    zhengChang: '正常',
    heZuoZhuangTai: '合作状态',
    heZuoFangMingCheng: '合作方名称',
    heZuoFangJiBen: '合作方基本信息',
    heZuoFangHeZuo: '合作方合作状态不能为空',
    heZuoFangMingCheng2: '合作方名称不能超过200长度',
    heZuoFangMingCheng3: '合作方名称不能为空',
    buNengWeiKong: '不能为空',
  },
  routerconfig: {
    chengBenDi: '成本低',
    chengBenZhong: '成本中',
    chengBenGao: '成本高',
    zhiXinDuDi: '置信度低',
    zhiXinDuZhong: '置信度中',
    zhiXinDuGao: '置信度高',
    qingXuanZe: '请选择',
    youXianJi: '优先级',
    xiaYi: '下移',
    shangYi: '上移',
    buNengChongFuXuan: '不能重复选择',
  },
  box: {
    kuWuLu: '库无率',
    kuWuLiang: '库无量',
    shiBaiLu: '失败率',
    shiBaiLiang: '失败量',
    chengGongLu: '成功率',
    chengGongLiang: '成功量',
    diaoYongLiang: '调用量',
  },
  batchquery: {
    dangQianJieGuoGong:
      '当前结果共 {val1} 条，成功 {val2} 条，失败 {val3} 条。还要继续吗？',
    chaXunJieGuoZhong: '查询结果，重复查询会产生费用',
    ninYiCunZai: '您已存在',
    zhongChaQuanBu: '重查全部',
    zhongChaShiBai: '重查失败',
    quXiao: '取消',
    qingXuanZeShuJu: '请选择数据源服务接口',
    zhiXingChaXun: '执行查询',
    yuLanShuJu: '预览数据',
    caoZuo: '操作',
    shangChuanRen: '上传人',
    shangChuanShiJian: '上传时间',
    xuHao: '序号',
    zuiDaZhiChiWan: '最大支持1万条数据',
    shangChuanShuJu: '上传数据',
    muBanXiaZai: '模板下载',
    chaXunXuYaoYi: '查询需要一些时间，请稍后再点击“预览数据”查询进度和结果',
    tiJiaoChaXunCheng: '提交查询成功',
    shangChuanChengGong: '上传成功',
    piLiangChaXunMo: '批量查询模板',
  },
  batchresult: {
    shiBai: '失败',
    chengGong: '成功',
    tiao: '条',
    chaXunWanCheng: '查询完成',
    daiZhaXun: '待查询',
    zongTiaoShu: '总条数',
    chaXunJinDu: '查询进度',
    jieGuoShengChengShi: '结果生成时间',
    yiHaoShi: '已耗时',
    shuaXin: '刷新',
    chaXunJieGuo: '查询结果',
  },
  countrange: {
    qingShuRuZhengZe: '请输入正则',
    zhengZe: '正则',
    qingXuanZe: '请选择',
    xuanZeYiLaiXi: '选择依赖系统字段',
    piPeiZiDuan: '匹配字段',
    yuan: '元',
    shuRuFeiYongYuan: '输入费用/元',
    feiYong: '费用',
    geKongJianZhongDe: '各控件中的正则不能重复',
  },
  daterange: {
    riQiQuJianJin:
      '日期区间仅可在有效期内选择，且完全覆盖有效期；起始时间时分秒固定00:00:00,截至时间 时分秒 固定为23:59:59',
    shuRuFeiYongYuan: '输入费用/元',
    zhi: '至',
    xiTongZiDongTian: '系统自动填充',
    riQiBuNengDa: '日期不能大于等于合同结束日期',
    diErGeRiQi: '第二个日期要大于第一个日期',
    qingXianWanShanHe: '请先完善合同开始日期和合同结束日期',
  },
  fieldpricesflowrange: {
    qingShuRuZhengZe: '请输入正则',
    zhengZe: '正则',
    qingXuanZe: '请选择',
    xuanZeYiLaiXi: '选择依赖系统字段',
    piPeiZiDuan: '匹配字段',
    yongBiaoShiQuJian: '用-99表示区间的正无穷，此项不可删除，系统默认配置项',
    shuRuFeiYongYuan: '输入费用/元',
    shuRuQuJianZhi: '输入区间值',
    zhi: '至',
    xiTongZiDongTian: '系统自动填充',
    feiYong: '费用',
    geKongJianZhongDe: '各控件中的正则不能重复',
    diErGeQuJian: '第二个区间输入值要大于第一个区间输入值',
  },
  flowrange: {
    yongBiaoShiQuJian: '用-99表示区间的正无穷，此项不可删除，系统默认配置项',
    shuRuFeiYongYuan: '输入费用/元',
    shuRuQuJianZhi: '输入区间值',
    zhi: '至',
    xiTongZiDongTian: '系统自动填充',
    diErGeQuJian: '第二个区间输入值要大于第一个区间输入值',
  },
  configmodal: {
    mOCKCan: 'Mock参数配置',
    miaoShu: '描述',
    ruCan: '入参',
    caoZuoChengGong: '操作成功',
  },
  importmodal: {
    xiaZaiMuBan: '下载模板',
    dianJiXiaZaiMo: '点击下载模板',
    shangChuanWenJian: '上传文件',
    qingXuanZeShangChuan: '请选择上传文件',
    daoRuMOC: '导入mock数据',
    muBan: '模板',
    caoZuoChengGong: '操作成功',
    qingXuanZeWenJian: '请选择文件',
  },
};
