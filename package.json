{"name": "tianzuo-react", "version": "1.0.0", "description": "同盾私有云外部数据服务管理平台", "private": true, "scripts": {"start": "nodemon -w build -w mock/index.js build/server.js", "build:v2": "webpack --progress --config build/webpack.prod.conf.js", "build:v3": "webpack --progress --config build/webpack.prod.conf.js", "build": "npm run build:v2 && npm run build:v3", "build:dll": "webpack --config ./build/webpack.dll.conf.js", "build:overview": "webpack --config ./build/webpack.overview.runtime", "os": "sh ./command/os.sh", "prepare": "husky install", "changeLog": "rm -rf CHANGELOG.md && conventional-changelog -p angular -i CHANGELOG.md -s", "eslint-fixed": "npx eslint --max-warnings 0 --fix --ext .js,.jsx,.ts,.tsx ./src"}, "dependencies": {"@tddc/assign-modal": "^2.0.5", "@tddc/bread-crumb": "^3.0.8", "@tddc/reference": "^2.0.6", "@tddc/tree-view": "^2.0.4", "@tddc/virtual-cascader": "^2.0.11", "@tntd/ant3-virtual-select": "^2.0.4", "@tntd/dll": "^1.0.2", "@tntd/formula-edit": "^2.1.13", "@tntd/hooks": "^1.0.5", "@tntd/tooltip-select": "^4.3.2", "@tntd/user-status-modal": "^2.0.1", "@tntd/utils": "^1.0.3", "@tntx/custom-syntax-editor": "^1.0.3", "ace-builds": "^1.6.0", "antd": "^3.26.18", "classnames": "^2.2.5", "codemirror": "^5.47.0", "core-js": "^3.8.1", "cron-parser": "^2.18.0", "diff": "^5.1.0", "dragm": "^0.0.5", "dva": "^2.4.1", "echarts": "^4.2.1", "eventemitter3": "^4.0.7", "immutability-helper": "^3.0.1", "jsencrypt": "3.2.1", "kiwi-intl": "^1.2.6-beta.0", "lodash": "^4.17.4", "lodash.clonedeep": "^4.5.0", "mmeditor": "^3.1.5", "moment": "^2.24.0", "prop-types": "^15.7.2", "query-string": "^6.7.0", "react": "^16.8.6", "react-ace": "^10.1.0", "react-clipboard.js": "^2.0.7", "react-dnd": "^5.0.0", "react-dnd-html5-backend": "^5.0.1", "react-dom": "^16.8.6", "react-json-view": "^1.21.3", "store": "^2.0.12", "tntd-v2": "npm:tntd@2.8.49", "tntd-v3": "npm:tntd@3.0.66", "universal-cookie": "^2.2.0"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.10.1", "@babel/plugin-proposal-decorators": "^7.12.12", "@babel/plugin-proposal-optional-chaining": "^7.14.2", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.1", "@babel/plugin-transform-runtime": "^7.10.1", "@babel/preset-env": "^7.10.2", "@babel/preset-react": "^7.10.1", "@babel/runtime": "^7.5.0", "@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@tntd/webpack-branch-plugin": "^1.0.10", "@tntd/webpack-transform-path-resolve-plugin": "^1.0.2", "autoprefixer": "^10.4.4", "babel-loader": "^8.2.3", "babel-plugin-import": "^1.13.3", "babel-plugin-lodash": "^3.3.4", "babel-plugin-transform-remove-console": "^6.9.4", "clean-webpack-plugin": "^4.0.0", "commitizen": "^4.3.0", "conventional-changelog-cli": "^2.2.2", "copy-webpack-plugin": "^9.1.0", "css-loader": "^6.7.1", "css-minimizer-webpack-plugin": "^3.4.1", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^6.3.0", "eslint": "^8.35.0", "eslint-config-tongdun": "^1.1.11", "eslint-plugin-td-rules-plugin": "^1.0.1", "html-webpack-plugin": "^5.5.0", "husky": "^8.0.3", "less": "^3.8.1", "less-loader": "^11.0.0", "lint-staged": "13.2.3", "mini-css-extract-plugin": "^2.6.0", "nodemon": "^2.0.15", "path-browserify": "^1.0.1", "postcss-loader": "^6.2.1", "raw-loader": "^4.0.2", "style-loader": "^3.3.1", "terser-webpack-plugin": "^5.3.1", "webpack": "^5.73.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.9.2", "webpack-merge": "^5.8.0"}, "browserslist": ["> 1%", "last 2 versions", "ie >= 11"], "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --quiet --fix --ext .js,.jsx,.ts,.tsx"]}}