module.exports = {
  "/bridgeApi/user/menuTree": "bridgeApi/user/menuTree",
  "/bridgeApi/serviceProvider/listAll": "bridgeApi/serviceProvider/listAll",
  "/bridgeApi/layout/common/dict": "bridgeApi/layout/common/dict",
  "/bridgeApi/layout/common/field/listAll": "bridgeApi/layout/common/field/listAll",
  "/bridgeApi/serviceConfig/listAll": "bridgeApi/serviceConfig/listAll",
  "/bridgeApi/userCenter/getUserInfo": "bridgeApi/userCenter/getUserInfo",
  "/bridgeApi/layout/common/mode/integration": "bridgeApi/layout/common/mode/integration",
  "/bridgeApi/workflow/variable/select": "bridgeApi/workflow/variable/select",
  "/bridgeApi/user/getAuthCode": "bridgeApi/user/getAuthCode",
  "/bridgeApi/user/login": "bridgeApi/user/login",
  "/bridgeApi/user/app": "bridgeApi/user/app",
  "/handleApi/statistics/channel/getInvokeRankStat": "handleApi/statistics/channel/getInvokeRankStat",
  "/handleApi/statistics/channel/getInvokeStatistic": "handleApi/statistics/channel/getInvokeStatistic",
  "/handleApi/statistics/channel/getInvokeDetailStat": "handleApi/statistics/channel/getInvokeDetailStat",
  "/handleApi/statistics/channel/getReasonCodeStatistic": "handleApi/statistics/channel/getReasonCodeStatistic",
  "/handleApi/statistics/channel/getWholeView": "handleApi/statistics/channel/getWholeView",
  "/handleApi/statistics/thirdService/getInvokeDetailForDay": "handleApi/statistics/thirdService/getInvokeDetailForDay",
  "/handleApi/statistics/getInvokeDetailStat": "handleApi/statistics/getInvokeDetailStat",
  "/handleApi/statistics/thirdService/getWholeView": "handleApi/statistics/thirdService/getWholeView",
  "/handleApi/statistics/getInvokeSourceTypeStat": "handleApi/statistics/getInvokeSourceTypeStat",
  "/handleApi/statistics/getInvokeRankStat": "handleApi/statistics/getInvokeRankStat",
  "/handleApi/statistics/getReasonCodeStatistic": "handleApi/statistics/getReasonCodeStatistic",
  "/bridgeApi/serviceConfig/list": "bridgeApi/serviceConfig/list",
  "/bridgeApi/contract/getContractList": "bridgeApi/contract/getContractList",
  "/bridgeApi/serviceProvider/list": "bridgeApi/serviceProvider/list",
  "/bridgeApi/bifrost/relationAnalysis": "bridgeApi/bifrost/relationAnalysis",
  "/bridgeApi/serviceConfig/getById": "bridgeApi/serviceConfig/getById",
  "/bridgeApi/serviceConfig/listMockData": "bridgeApi/serviceConfig/listMockData",
  "/captainApi/documentType/list": "captainApi/documentType/list",
  "/bridgeApi/etlHandler/listByStatus": "bridgeApi/etlHandler/listByStatus",
  "/bridgeApi/contract/select": "bridgeApi/contract/select",
  "/captainApi/indexPackage/simpleList": "captainApi/indexPackage/simpleList",
  "/bridgeApi/serviceConfig/update": "bridgeApi/serviceConfig/update",
  "/bridgeApi/serviceConfig/copy": "bridgeApi/serviceConfig/copy",
  "/bridgeApi/serviceConfig/sync/test/third": "bridgeApi/serviceConfig/sync/test/third",
  "/bridgeApi/bifrost/checkComponentReference": "bridgeApi/bifrost/checkComponentReference",
  "/bridgeApi/serviceConfig/delete": "bridgeApi/serviceConfig/delete",
  "/bridgeApi/etlHandler/listAllCommonCode": "bridgeApi/etlHandler/listAllCommonCode",
  "/bridgeApi/etlHandler/list": "bridgeApi/etlHandler/list",
  "/bridgeApi/service/interface/dict": "bridgeApi/service/interface/dict",
  "/bridgeApi/service/interface/list": "bridgeApi/service/interface/list",
  "/bridgeApi/org": "bridgeApi/org",
  "/bridgeApi/serviceConfig/getOutPutConfig": "bridgeApi/serviceConfig/getOutPutConfig",
  "/bridgeApi/serviceConfig/findRouteService": "bridgeApi/serviceConfig/findRouteService",
  "/bridgeApi/serviceConfig/findDefaultRouteServiceList": "bridgeApi/serviceConfig/findDefaultRouteServiceList",
  "/handleApi/channelServiceGroup/serviceGroupInputInfo": "handleApi/channelServiceGroup/serviceGroupInputInfo",
  "/handleApi/channelServiceGroup/list": "handleApi/channelServiceGroup/list",
  "/handleApi/dataManage/thirdService/getInvokeDetailList": "handleApi/dataManage/thirdService/getInvokeDetailList",
  "/handleApi/dataManage/thirdService/getInvokeDetail": "handleApi/dataManage/thirdService/getInvokeDetail",
  "/handleApi/invokeCommon/batchList": "handleApi/invokeCommon/batchList",
  "/bridgeApi/serviceConfig/sync/test/query": "bridgeApi/serviceConfig/sync/test/query",
  "/bridgeApi/layout/common/app/list/all": "bridgeApi/layout/common/app/list/all",
  "/bridgeApi/layout/common/org/list/all": "bridgeApi/layout/common/org/list/all",
  "/bridgeApi/statistics/invoke/cost": "bridgeApi/statistics/invoke/cost",
  "/bridgeApi/statistics/reason/code": "bridgeApi/statistics/reason/code",
  "/bridgeApi/statistics/invoke/cost/channel/avg": "bridgeApi/statistics/invoke/cost/channel/avg",
  "/bridgeApi/statistics/invoke/cost/channel/status": "bridgeApi/statistics/invoke/cost/channel/status",
  "/bridgeApi/statistics/datasource/rank": "bridgeApi/statistics/datasource/rank",
  "/bridgeApi/statistics/channel/getWholeView": "bridgeApi/statistics/channel/getWholeView",
  "/bridgeApi/layout/function/functionQuery": "bridgeApi/layout/function/functionQuery",
  "/bridgeApi/layout/function/authorizationQuery": "bridgeApi/layout/function/authorizationQuery",
  "/bridgeApi/layout/function/authorization": "bridgeApi/layout/function/authorization",
  "/bridgeApi/bifrost/relationAnalysis/function": "bridgeApi/bifrost/relationAnalysis/function",
  "/bridgeApi/layout/function/versionDetail": "bridgeApi/layout/function/versionDetail",
  "/bridgeApi/layout/function/functionCreate": "bridgeApi/layout/function/functionCreate",
  "/bridgeApi/workflowTemplate/page": "bridgeApi/workflowTemplate/page",
  "/bridgeApi/workflowConfig/node/config": "bridgeApi/workflowConfig/node/config",
  "/bridgeApi/workflowTemplate/detail": "bridgeApi/workflowTemplate/detail",
  "/bridgeApi/layout/function/functionUpdate": "bridgeApi/layout/function/functionUpdate",
  "/bridgeApi/layout/function/functionParseParams": "bridgeApi/layout/function/functionParseParams",
  "/bridgeApi/workflow/formula/select": "bridgeApi/workflow/formula/select",
  "/bridgeApi/workflowConfig/dataSourceService/select": "bridgeApi/workflowConfig/dataSourceService/select",
  "/bridgeApi/layout/common/index/captain": "bridgeApi/layout/common/index/captain",
  "/bridgeApi/workflowConfig/dataSourceService/mapping/select": "bridgeApi/workflowConfig/dataSourceService/mapping/select",
  "/bridgeApi/workflow/page": "bridgeApi/workflow/page",
  "/bridgeApi/workflowConfig/dict/config": "bridgeApi/workflowConfig/dict/config",
  "/bridgeApi/workflow/detail": "bridgeApi/workflow/detail",
  "/bridgeApi/service/interface/detail": "bridgeApi/service/interface/detail",
  "/bridgeApi/workflow/list": "bridgeApi/workflow/list",
  "/bridgeApi/service/interface/listAll": "bridgeApi/service/interface/listAll",
  "/bridgeApi/systemField/allField": "bridgeApi/systemField/allField",
  "/bridgeApi/workflowConfig/workflow/params": "bridgeApi/workflowConfig/workflow/params",
  "/bridgeApi/service/interface/authorizationQuery": "bridgeApi/service/interface/authorizationQuery",
  "/bridgeApi/service/interface/update/roster": "bridgeApi/service/interface/update/roster",
  "/bridgeApi/service/interface/update/encryption": "bridgeApi/service/interface/update/encryption"
}